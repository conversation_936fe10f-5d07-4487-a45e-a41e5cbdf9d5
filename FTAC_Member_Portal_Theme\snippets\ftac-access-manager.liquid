{% comment %}
  FTAC Access Code Management System
  
  This snippet provides comprehensive access code management including:
  - Access code validation
  - Product unlocking
  - Demo access codes for testing
  - Integration with existing product access system
  
  Parameters:
  - action: 'validate_code', 'unlock_product', 'get_demo_codes', 'check_product_access'
  - customer: Current customer object
  - access_code: Access code to validate/use
  - product_handle: Product handle for validation
  
  Usage:
  {% render 'ftac-access-manager', action: 'validate_code', customer: customer, access_code: 'FTAC-DEMO-TEST-1234' %}
  {% assign validation_result = ftac_validation_result %}
  {% assign unlocked_product = ftac_unlocked_product %}
{% endcomment %}

{% assign action = action | default: 'validate_code' %}
{% assign customer = customer %}
{% assign access_code = access_code %}
{% assign product_handle = product_handle %}

{% comment %} Initialize global result variables {% endcomment %}
{% assign ftac_validation_result = false %}
{% assign ftac_unlocked_product = '' %}
{% assign ftac_error_message = '' %}
{% assign ftac_success_message = '' %}

{% comment %} Product Access Code Database {% endcomment %}
{% comment %} In production, this would be stored in Shopify metafields or external database {% endcomment %}
{% assign demo_access_codes = 'FTAC-DEMO-TEST-1234:future-tech-foundations,FTAC-DEMO-MAST-5678:advanced-tech-mastery,FTAC-TEST-FOUN-9012:future-tech-foundations,FTAC-TEST-LEAD-3456:tech-leadership-suite' %}

{% comment %} Product Handle to Code Mapping {% endcomment %}
{% assign product_code_mapping = 'future-tech-foundations:FOUN,advanced-tech-mastery:MAST,tech-leadership-suite:LEAD,custom-bundle:CUST,test-product:TEST' %}

{% comment %} Product Information Database {% endcomment %}
{% assign product_info = 'future-tech-foundations:Future Tech Foundations:14.99,advanced-tech-mastery:Advanced Tech Mastery:29.99,tech-leadership-suite:Tech Leadership Suite:49.99,test-product:Test Product:14.99' %}

{% case action %}
  {% when 'validate_code' %}
    {% comment %} Validate access code format and check if it exists {% endcomment %}
    {% if access_code %}
      {% comment %} Check format: FTAC-XXXX-YYYY-ZZZZ {% endcomment %}
      {% assign code_pattern_valid = false %}
      {% if access_code.size == 19 %}
        {% assign code_parts = access_code | split: '-' %}
        {% if code_parts.size == 4 and code_parts[0] == 'FTAC' %}
          {% assign code_pattern_valid = true %}
        {% endif %}
      {% endif %}
      
      {% if code_pattern_valid %}
        {% comment %} Check if it's a demo/test code {% endcomment %}
        {% assign demo_codes = demo_access_codes | split: ',' %}
        {% for demo_code_pair in demo_codes %}
          {% assign code_product = demo_code_pair | split: ':' %}
          {% if code_product[0] == access_code %}
            {% assign ftac_validation_result = true %}
            {% assign ftac_unlocked_product = code_product[1] %}
            {% assign ftac_success_message = 'Valid access code for demo/test product' %}
            {% break %}
          {% endif %}
        {% endfor %}
        
        {% unless ftac_validation_result %}
          {% comment %} Check against real product codes (would be database lookup in production) {% endcomment %}
          {% comment %} For now, validate format and product code match {% endcomment %}
          {% assign product_code = code_parts[1] %}
          {% assign mapping_pairs = product_code_mapping | split: ',' %}
          {% for mapping in mapping_pairs %}
            {% assign handle_code = mapping | split: ':' %}
            {% if handle_code[1] == product_code %}
              {% assign ftac_validation_result = true %}
              {% assign ftac_unlocked_product = handle_code[0] %}
              {% assign ftac_success_message = 'Valid access code format' %}
              {% break %}
            {% endif %}
          {% endfor %}
        {% endunless %}
        
        {% unless ftac_validation_result %}
          {% assign ftac_error_message = 'Invalid access code. Please check your welcome email or contact support.' %}
        {% endunless %}
      {% else %}
        {% assign ftac_error_message = 'Invalid access code format. Please use format: FTAC-XXXX-YYYY-ZZZZ' %}
      {% endif %}
    {% else %}
      {% assign ftac_error_message = 'Please enter an access code' %}
    {% endif %}

  {% when 'unlock_product' %}
    {% comment %} Unlock product for customer (would update metafields in production) {% endcomment %}
    {% if customer and access_code %}
      {% comment %} First validate the code {% endcomment %}
      {% render 'ftac-access-manager', action: 'validate_code', access_code: access_code %}
      
      {% if ftac_validation_result and ftac_unlocked_product != '' %}
        {% comment %} In production, this would update customer metafields via Shopify Admin API {% endcomment %}
        {% comment %} For now, simulate the unlock process {% endcomment %}
        {% assign ftac_success_message = 'Product unlocked successfully! You now have access to ' | append: ftac_unlocked_product %}
        
        {% comment %} Get product display name {% endcomment %}
        {% assign product_infos = product_info | split: ',' %}
        {% for info in product_infos %}
          {% assign info_parts = info | split: ':' %}
          {% if info_parts[0] == ftac_unlocked_product %}
            {% assign product_display_name = info_parts[1] %}
            {% assign ftac_success_message = 'Product unlocked successfully! You now have access to ' | append: product_display_name %}
            {% break %}
          {% endif %}
        {% endfor %}
      {% else %}
        {% assign ftac_error_message = 'Unable to unlock product. Invalid access code.' %}
      {% endif %}
    {% else %}
      {% assign ftac_error_message = 'Customer login and access code required' %}
    {% endif %}

  {% when 'get_demo_codes' %}
    {% comment %} Return list of demo access codes for testing {% endcomment %}
    {% assign ftac_demo_codes = demo_access_codes %}

  {% when 'check_product_access' %}
    {% comment %} Check if customer has access to specific product {% endcomment %}
    {% if customer and product_handle %}
      {% render 'ftac-product-access', action: 'check_access', customer: customer, product_handle: product_handle %}
      {% assign ftac_validation_result = ftac_access_result %}
    {% endif %}

  {% when 'get_product_info' %}
    {% comment %} Get product information by handle {% endcomment %}
    {% if product_handle %}
      {% assign product_infos = product_info | split: ',' %}
      {% for info in product_infos %}
        {% assign info_parts = info | split: ':' %}
        {% if info_parts[0] == product_handle %}
          {% assign ftac_product_name = info_parts[1] %}
          {% assign ftac_product_price = info_parts[2] %}
          {% break %}
        {% endif %}
      {% endfor %}
    {% endif %}

  {% else %}
    {% assign ftac_error_message = 'Invalid action specified' %}
{% endcase %}

{% comment %}
  Demo Access Codes for Testing:
  
  FTAC-DEMO-TEST-1234 → future-tech-foundations (Future Tech Foundations)
  FTAC-DEMO-MAST-5678 → advanced-tech-mastery (Advanced Tech Mastery)  
  FTAC-TEST-FOUN-9012 → future-tech-foundations (Future Tech Foundations)
  FTAC-TEST-LEAD-3456 → tech-leadership-suite (Tech Leadership Suite)
  
  Test Product Access Code:
  FTAC-TEST-PROD-1499 → test-product (Test Product - $14.99)
{% endcomment %}

{% comment %}
  Product Bundle Configuration System
  
  This section defines what each product bundle includes:
  - App access
  - PDF downloads  
  - Print-on-Demand (P.O.D.) physical items
  
  Bundle Configuration Format:
  product_handle:bundle_type:includes_app:includes_pdf:includes_pod
  
  Examples:
  future-tech-foundations:basic:true:true:false
  advanced-tech-mastery:premium:true:true:true
  tech-leadership-suite:enterprise:true:true:true
{% endcomment %}

{% assign bundle_configurations = 'future-tech-foundations:basic:true:true:false,advanced-tech-mastery:premium:true:true:true,tech-leadership-suite:enterprise:true:true:true,test-product:basic:true:false:false' %}

{% if action == 'get_bundle_config' and product_handle %}
  {% assign bundle_configs = bundle_configurations | split: ',' %}
  {% for config in bundle_configs %}
    {% assign config_parts = config | split: ':' %}
    {% if config_parts[0] == product_handle %}
      {% assign ftac_bundle_type = config_parts[1] %}
      {% assign ftac_includes_app = config_parts[2] %}
      {% assign ftac_includes_pdf = config_parts[3] %}
      {% assign ftac_includes_pod = config_parts[4] %}
      {% break %}
    {% endif %}
  {% endfor %}
{% endif %}
