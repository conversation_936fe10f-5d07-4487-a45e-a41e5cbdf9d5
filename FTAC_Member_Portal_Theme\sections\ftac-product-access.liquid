{% comment %}
  FTAC Product Access Section
  
  This section handles product-level access control and displays
  the appropriate interface based on customer login status and
  product access permissions.
  
  Usage: Add this section to product templates where access control is needed.
{% endcomment %}

{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<div class="ftac-product-access">
  <div class="ftac-product-access__container">
    
    {% comment %} Check if customer is logged in {% endcomment %}
    {% unless customer %}
      {% comment %} Show login prompt for non-logged-in users {% endcomment %}
      <div class="ftac-product-access__login-prompt">
        <div class="ftac-product-access__content">
          <div class="ftac-product-access__icon">
            <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
          <h2 class="ftac-product-access__title">Premium Content</h2>
          <p class="ftac-product-access__message">
            This is premium content from the Future Tech Academy Club. 
            Sign in to your account to unlock this content with your access code.
          </p>
          <div class="ftac-product-access__actions">
            <a href="/pages/member-access" class="ftac-product-access__button ftac-product-access__button--primary">
              Sign In to Access
            </a>
            <a href="/pages/member-access" class="ftac-product-access__button ftac-product-access__button--secondary">
              Create Free Account
            </a>
          </div>
          <p class="ftac-product-access__help">
            Don't have an access code? 
            <a href="/pages/contact-support" class="ftac-product-access__link">Contact Sarah for help</a>
          </p>
        </div>
      </div>
    {% else %}
      {% comment %} Customer is logged in - check product access {% endcomment %}
      {% assign has_access = false %}
      {% render 'ftac-auth-logic', action: 'check_product_access', customer: customer, product_handle: product.handle %}
      {% render 'ftac-product-access', action: 'check_access', customer: customer, product_handle: product.handle, return_var: 'has_access' %}
      
      {% if has_access %}
        {% comment %} Customer has access - show success message {% endcomment %}
        <div class="ftac-product-access__granted">
          <div class="ftac-product-access__content">
            <div class="ftac-product-access__success-icon">✓</div>
            <h2 class="ftac-product-access__title">Access Granted</h2>
            <p class="ftac-product-access__message">
              Welcome back! You have access to <strong>{{ product.title }}</strong>. 
              Enjoy your premium content below.
            </p>
            <div class="ftac-product-access__actions">
              <a href="/account" class="ftac-product-access__button ftac-product-access__button--secondary">
                View Dashboard
              </a>
            </div>
          </div>
        </div>
      {% else %}
        {% comment %} Customer needs to enter access code {% endcomment %}
        <div class="ftac-product-access__code-entry">
          <div class="ftac-product-access__content">
            <div class="ftac-product-access__icon">
              <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
              </svg>
            </div>
            <h2 class="ftac-product-access__title">Enter Access Code</h2>
            <p class="ftac-product-access__message">
              You need an access code to unlock <strong>{{ product.title }}</strong>. 
              Enter the code you received after purchasing this product.
            </p>
            
            {% comment %} Render the access code entry form {% endcomment %}
            {% render 'ftac-access-code-entry', 
               product_handle: product.handle, 
               product_name: product.title,
               customer: customer,
               redirect_url: product.url
            %}
          </div>
        </div>
      {% endif %}
    {% endunless %}
    
  </div>
</div>

<style>
  .ftac-product-access {
    background: linear-gradient(135deg, var(--ftac-warm-cream), #ffffff);
    padding: var(--ftac-space-12) 0;
    margin: var(--ftac-space-8) 0;
  }
  
  .ftac-product-access__container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
  }
  
  .ftac-product-access__content {
    background-color: white;
    border-radius: var(--ftac-radius-2xl);
    padding: var(--ftac-space-12);
    text-align: center;
    box-shadow: var(--ftac-shadow-xl);
    border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.1);
  }
  
  .ftac-product-access__granted .ftac-product-access__content {
    border-color: var(--ftac-learning-green);
    background: linear-gradient(135deg, #f0fdf4, white);
  }
  
  .ftac-product-access__icon {
    width: 80px;
    height: 80px;
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--ftac-space-6);
    color: var(--ftac-academy-blue);
  }
  
  .ftac-product-access__success-icon {
    width: 80px;
    height: 80px;
    background-color: var(--ftac-learning-green);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--ftac-text-3xl);
    font-weight: bold;
    margin: 0 auto var(--ftac-space-6);
  }
  
  .ftac-product-access__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-3xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-product-access__message {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-charcoal);
    line-height: 1.6;
    margin-bottom: var(--ftac-space-8);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }
  
  .ftac-product-access__actions {
    display: flex;
    flex-direction: column;
    gap: var(--ftac-space-4);
    align-items: center;
    margin-bottom: var(--ftac-space-6);
  }
  
  @media (min-width: 640px) {
    .ftac-product-access__actions {
      flex-direction: row;
      justify-content: center;
    }
  }
  
  .ftac-product-access__button {
    display: inline-block;
    padding: var(--ftac-space-4) var(--ftac-space-8);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
    min-width: 200px;
    text-align: center;
  }
  
  .ftac-product-access__button--primary {
    background-color: var(--ftac-academy-blue);
    color: white;
    box-shadow: var(--ftac-shadow-lg);
  }
  
  .ftac-product-access__button--primary:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
    box-shadow: var(--ftac-shadow-xl);
  }
  
  .ftac-product-access__button--secondary {
    background-color: white;
    color: var(--ftac-academy-blue);
    border-color: var(--ftac-academy-blue);
  }
  
  .ftac-product-access__button--secondary:hover {
    background-color: var(--ftac-academy-blue);
    color: white;
    transform: translateY(-1px);
  }
  
  .ftac-product-access__help {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
    opacity: 0.7;
  }
  
  .ftac-product-access__link {
    color: var(--ftac-academy-blue);
    text-decoration: none;
    font-weight: var(--ftac-font-medium);
  }
  
  .ftac-product-access__link:hover {
    text-decoration: underline;
  }
  
  /* Responsive adjustments */
  @media (max-width: 640px) {
    .ftac-product-access__content {
      padding: var(--ftac-space-8);
    }
    
    .ftac-product-access__title {
      font-size: var(--ftac-text-2xl);
    }
    
    .ftac-product-access__message {
      font-size: var(--ftac-text-base);
    }
    
    .ftac-product-access__button {
      min-width: 100%;
    }
  }
</style>

{% schema %}
{
  "name": "FTAC Product Access",
  "settings": [
    {
      "type": "header",
      "content": "Product Access Control"
    },
    {
      "type": "paragraph",
      "content": "This section automatically handles product access control based on customer login status and access codes. No configuration needed."
    }
  ],
  "presets": [
    {
      "name": "FTAC Product Access"
    }
  ]
}
{% endschema %}
