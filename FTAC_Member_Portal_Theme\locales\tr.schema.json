{"settings_schema": {"colors": {"name": "Ren<PERSON>r", "settings": {"background": {"label": "Arka plan"}, "background_gradient": {"label": "Arka plan gradyanı", "info": "Arka plan gradyanı, mümkün olduğunda arka planın yerine geçer."}, "text": {"label": "<PERSON><PERSON>"}, "button_background": {"label": "Sabit düğme arka planı"}, "button_label": {"label": "<PERSON><PERSON> dü<PERSON> et<PERSON>"}, "secondary_button_label": {"label": "<PERSON><PERSON>ş çizgi düğmes<PERSON>"}, "shadow": {"label": "<PERSON><PERSON><PERSON>"}}}, "typography": {"name": "Tipografi", "settings": {"type_header_font": {"label": "Yazı tipi"}, "header__1": {"content": "Başlıklar"}, "header__2": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "type_body_font": {"label": "Yazı tipi"}, "heading_scale": {"label": "Ölçek"}, "body_scale": {"label": "Ölçek"}}}, "social-media": {"name": "So<PERSON>al medya", "settings": {"social_twitter_link": {"label": "X/Twitter", "info": "https://x.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "So<PERSON>al medya he<PERSON>ı"}}}, "currency_format": {"name": "Para birimi biçimi", "settings": {"currency_code_enabled": {"label": "Para birimi kodları"}, "paragraph": "Sepet ve ödeme ücretleri her zaman para birimi kodlarını gösterir"}}, "layout": {"name": "D<PERSON>zen", "settings": {"page_width": {"label": "Say<PERSON> genişliği"}, "spacing_sections": {"label": "Şablon bölümleri arasındaki alan"}, "header__grid": {"content": "Izgara"}, "paragraph__grid": {"content": "Birden fazla sütun veya satır içeren alanları etkiler"}, "spacing_grid_horizontal": {"label": "<PERSON><PERSON><PERSON>"}, "spacing_grid_vertical": {"label": "<PERSON><PERSON> b<PERSON>"}}}, "search_input": {"name": "<PERSON><PERSON>", "settings": {"predictive_search_enabled": {"label": "<PERSON><PERSON>"}, "predictive_search_show_vendor": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON> etkin durumdayken gösterilir"}, "predictive_search_show_price": {"label": "<PERSON><PERSON><PERSON><PERSON>tı", "info": "<PERSON><PERSON> etkin durumdayken gösterilir"}}}, "global": {"settings": {"header__border": {"content": "Kenarlık"}, "header__shadow": {"content": "<PERSON><PERSON><PERSON>"}, "blur": {"label": "Bulanıklık"}, "corner_radius": {"label": "Köşe yarıçapı"}, "horizontal_offset": {"label": "<PERSON><PERSON><PERSON>"}, "vertical_offset": {"label": "<PERSON><PERSON> den<PERSON>e"}, "thickness": {"label": "Kalınlık"}, "opacity": {"label": "Opaklık"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>gus<PERSON>"}, "text_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON>"}}}, "badges": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"position": {"options__1": {"label": "Alt sol"}, "options__2": {"label": "Alt sağ"}, "options__3": {"label": "Üst sol"}, "options__4": {"label": "Üst sağ"}, "label": "<PERSON><PERSON><PERSON><PERSON> konum"}, "sale_badge_color_scheme": {"label": "İndirim rozeti renk şeması"}, "sold_out_badge_color_scheme": {"label": "Tükendi rozeti renk şeması"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "variant_pills": {"name": "<PERSON><PERSON><PERSON><PERSON>", "paragraph": "<PERSON><PERSON><PERSON><PERSON>, [ür<PERSON><PERSON> varyasyonlarınızı](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#variant-picker-block) göstermenin bir yoludur"}, "inputs": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "content_containers": {"name": "İçerik kapsayıcıları"}, "popups": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve pencereler", "paragraph": "Gezinme açı<PERSON>ı<PERSON>, açılır pencere modları ve sepet açılır pencereleri gibi alanları etkiler"}, "media": {"name": "<PERSON><PERSON><PERSON>"}, "drawers": {"name": "Çekmeceler"}, "cart": {"name": "Sepet", "settings": {"cart_type": {"label": "<PERSON><PERSON><PERSON>", "drawer": {"label": "Çekmece"}, "page": {"label": "Say<PERSON>"}, "notification": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere bildirimi"}}, "show_vendor": {"label": "Satıcı"}, "show_cart_note": {"label": "Sepet notu"}, "cart_drawer": {"header": "Sepet çekmecesi", "collection": {"label": "Koleksiyon", "info": "Sepet çekmecesi boş olduğunda gösterilir"}}}}, "cards": {"name": "<PERSON>rün kartları", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Kart"}, "label": "Stil"}}}, "collection_cards": {"name": "Koleksiyon kartları", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Kart"}, "label": "Stil"}}}, "blog_cards": {"name": "Blog kartları", "settings": {"style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Kart"}, "label": "Stil"}}}, "logo": {"name": "Logo", "settings": {"logo_image": {"label": "Logo"}, "logo_width": {"label": "Genişlik"}, "favicon": {"label": "Favicon", "info": "32 x 32 pikselde görüntüleniyor"}}}, "brand_information": {"name": "<PERSON><PERSON>i", "settings": {"brand_headline": {"label": "Başlık"}, "brand_description": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "brand_image": {"label": "G<PERSON><PERSON><PERSON>"}, "brand_image_width": {"label": "G<PERSON><PERSON><PERSON> genişliği"}, "paragraph": {"content": "Altbilginin marka bilgileri bloğunda görüntülenir"}}}, "animations": {"name": "Animasyonlar", "settings": {"animations_reveal_on_scroll": {"label": "Kaydır<PERSON><PERSON><PERSON> bölümleri göster"}, "animations_hover_elements": {"options__1": {"label": "Yok"}, "options__2": {"label": "Dikey lift"}, "label": "Üzerine gelme efekti", "info": "Kartları ve düğmeleri etkiler", "options__3": {"label": "3D kaldırma"}}}}}, "sections": {"all": {"padding": {"section_padding_heading": "<PERSON><PERSON><PERSON>", "padding_top": "Üst", "padding_bottom": "Alt"}, "spacing": "Boşluk", "colors": {"label": "Renk şeması", "has_cards_info": "Kart renk şemasını değiştirmek için tema ayarlarınızı güncelleyin."}, "heading_size": {"label": "Başlık boyutu", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "options__4": {"label": "Çok büyük"}, "options__5": {"label": "Çok çok büyük"}}, "image_shape": {"options__1": {"label": "Varsayılan"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Sola ok"}, "options__5": {"label": "Sağa ok"}, "options__6": {"label": "Baklava"}, "options__7": {"label": "Paralelkenar"}, "options__8": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON><PERSON>"}, "animation": {"content": "Animasyonlar", "image_behavior": {"options__1": {"label": "Yok"}, "options__2": {"label": "Ortam iç<PERSON> ha<PERSON>et"}, "label": "Animasyon", "options__3": {"label": "Sabit arka plan konumu"}, "options__4": {"label": "Kaydırarak yakınlaştır"}}}}, "announcement-bar": {"name": "<PERSON><PERSON><PERSON>", "blocks": {"announcement": {"name": "<PERSON><PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "Mağazamıza hoş geldiniz"}, "text_alignment": {"label": "<PERSON><PERSON>", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}}, "link": {"label": "Bağlantı"}}}}, "settings": {"auto_rotate": {"label": "Duyuruları otomatik olarak döndür"}, "change_slides_speed": {"label": "<PERSON><PERSON> zaman aralığında değiştir:"}, "show_social": {"label": "So<PERSON><PERSON> medya sim<PERSON>eri", "info": "[Sosyal medya hesaplarını yönet](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "<PERSON><PERSON><PERSON>/bö<PERSON> se<PERSON>", "info": "[Ülk<PERSON><PERSON>/bölgeleri yönet](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "info": "[<PERSON><PERSON><PERSON> y<PERSON>](/admin/settings/languages)"}, "heading_utilities": {"content": "Yardımcı araçlar"}, "paragraph": {"content": "Yalnızca geniş ekranlarda göster"}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "collage": {"name": "<PERSON><PERSON><PERSON>", "settings": {"heading": {"label": "Başlık", "default": "Multimedya kolajı"}, "desktop_layout": {"label": "D<PERSON>zen", "options__1": {"label": "İlk büyük blok"}, "options__2": {"label": "<PERSON> b<PERSON><PERSON><PERSON><PERSON> blok"}}, "mobile_layout": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "card_styles": {"label": "Kart stili", "info": "Bireysel kart stillerini [te<PERSON> a<PERSON>](/editor?context=theme&category=product%20cards) bölümünde yönetin", "options__1": {"label": "Bireysel kart stilleri kullanın"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON> stilini ürün kartı şeklinde ayarla"}}, "header_layout": {"content": "D<PERSON>zen"}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}}}, "product": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "secondary_background": {"label": "İkincil arka planı göster"}, "second_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}}}, "collection": {"name": "Koleksiyon", "settings": {"collection": {"label": "Koleksiyon"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Kapak gö<PERSON>"}, "video_url": {"label": "URL", "info": "Bölümde başka bloklar varsa video açılır pencerede oynatılır.", "placeholder": "YouTube veya Vimeo URL'si kullanın"}, "description": {"label": "Video alternatif metni", "info": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)", "default": "Videoyu açıklayın"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON>"}}, "collection-list": {"name": "Koleks<PERSON><PERSON>esi", "settings": {"title": {"label": "Başlık", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "swipe_on_mobile": {"label": "Döngü"}, "show_view_all": {"label": "\"Tümünü görüntü<PERSON>\" düğmesi", "info": "Listede gösterilen daha fazla koleksiyon varsa görü<PERSON>ür"}, "columns_desktop": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_layout": {"content": "D<PERSON>zen"}}, "blocks": {"featured_collection": {"name": "Koleksiyon", "settings": {"collection": {"label": "Koleksiyon"}}}}, "presets": {"name": "Koleks<PERSON><PERSON>esi"}}, "contact-form": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "presets": {"name": "İletişim formu"}, "settings": {"title": {"default": "İletişim formu", "label": "Başlık"}}}, "custom-liquid": {"name": "Özel Liquid", "settings": {"custom_liquid": {"label": "Liquid kodu", "info": "Gelişmiş özelleştirmeler oluşturmak için uygulama parçacıkları veya başka bir kod ekleyin. [Daha fazla bilgi edinin](https://shopify.dev/docs/api/liquid)"}}, "presets": {"name": "Özel Liquid"}}, "featured-blog": {"name": "Blog gönderileri", "settings": {"heading": {"label": "Başlık", "default": "Blog gönderileri"}, "blog": {"label": "Blog"}, "post_limit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "\"Tümünü görüntü<PERSON>\" düğmesi", "info": "Blogda gösterilenden daha fazla gönderi varsa görü<PERSON>ür"}, "show_image": {"label": "<PERSON>ne <PERSON>"}, "show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "<PERSON><PERSON>"}, "columns_desktop": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "layout_header": {"content": "D<PERSON>zen"}, "text_header": {"content": "<PERSON><PERSON>"}}, "presets": {"name": "Blog gönderileri"}}, "featured-collection": {"name": "Öne çıkan koleksiyon", "settings": {"title": {"label": "Başlık", "default": "Öne çıkan koleksiyon"}, "collection": {"label": "Koleksiyon"}, "products_to_show": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_view_all": {"label": "\"Tümünü görüntü<PERSON>\" düğmesi", "info": "Koleksiyonda gösterilenden daha fazla ürün varsa görünür"}, "header": {"content": "Ürün kartı"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "show_vendor": {"label": "Satıcı"}, "show_rating": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Derecelendirmeler i<PERSON> bir uygulam<PERSON> gereklidir. [<PERSON>ha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "columns_desktop": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "description": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "show_description": {"label": "Yönetici panelinden koleksiyon açıklamasını göster"}, "description_style": {"label": "Açıklama stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}, "view_all_style": {"label": "\"<PERSON>ümü<PERSON>ü görü<PERSON><PERSON><PERSON>\" stili", "options__1": {"label": "Bağlantı"}, "options__2": {"label": "<PERSON><PERSON>ş çizgi düğmes<PERSON>"}, "options__3": {"label": "<PERSON>bit dü<PERSON>"}}, "enable_desktop_slider": {"label": "Döngü"}, "full_width": {"label": "<PERSON> g<PERSON> ürü<PERSON>"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "swipe_on_mobile": {"label": "Döngü"}, "enable_quick_buy": {"label": "Hızlı ekle"}, "header_text": {"content": "<PERSON><PERSON>"}, "header_collection": {"content": "Koleksiyon düzeni"}}, "presets": {"name": "Öne çıkan koleksiyon"}}, "footer": {"name": "Altbilgi", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Başlık", "default": "Hızlı bağlantılar"}, "menu": {"label": "<PERSON><PERSON>"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Başlık", "default": "Başlık"}, "subtext": {"label": "Alt metin", "default": "<p><PERSON><PERSON><PERSON><PERSON><PERSON>, mağaza ayrıntılarını ve marka içeriklerini müşterilerinizle paylaşın.</p>"}}}, "brand_information": {"name": "<PERSON><PERSON>i", "settings": {"paragraph": {"content": "<PERSON><PERSON> [tema ayarlar<PERSON>](/editor?context=theme&category=brand%20information) bölümünde yönetin"}, "show_social": {"label": "So<PERSON><PERSON> medya sim<PERSON>eri", "info": "[Sosyal medya hesaplarını yönet](/editor?context=theme&category=social%20media)"}}}}, "settings": {"newsletter_enable": {"label": "E-posta kaydı"}, "newsletter_heading": {"label": "Başlık", "default": "E-posta listemize kaydolun"}, "header__1": {"content": "E-posta kaydı", "info": "<PERSON><PERSON><PERSON> ekleme [mü<PERSON><PERSON>i profilleri](https://help.shopify.com/manual/customers/manage-customers)"}, "show_social": {"label": "So<PERSON><PERSON> medya sim<PERSON>eri", "info": "[Sosyal medya hesaplarını yönet](/editor?context=theme&category=social%20media)"}, "enable_country_selector": {"label": "<PERSON><PERSON><PERSON>/bö<PERSON> se<PERSON>", "info": "[Ülk<PERSON><PERSON>/bölgeleri yönet](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "info": "[<PERSON><PERSON><PERSON> y<PERSON>](/admin/settings/languages)"}, "payment_enable": {"label": "<PERSON><PERSON><PERSON> sim<PERSON>"}, "margin_top": {"label": "Üst kenar b<PERSON>luğ<PERSON>"}, "show_policy": {"label": "Politika bağlantıları", "info": "[Politikaları yönet](/admin/settings/legal)"}, "header__9": {"content": "Yardımcı araçlar"}, "enable_follow_on_shop": {"label": "Shop'ta takip edin", "info": "Shop Pay etkinleştirilmelidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}}}, "header": {"name": "Üstbilgi", "settings": {"logo_position": {"label": "Logo konumu", "options__1": {"label": "Orta sol"}, "options__2": {"label": "Üst sol"}, "options__3": {"label": "Üst orta"}, "options__4": {"label": "<PERSON><PERSON> kısmın ortası"}}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Ayırıcı satır"}, "margin_bottom": {"label": "Alt kenar boşluğu"}, "menu_type_desktop": {"label": "<PERSON><PERSON> türü", "options__1": {"label": "Açılır <PERSON>ü"}, "options__2": {"label": "Mega menü"}, "options__3": {"label": "Çekmece"}}, "mobile_logo_position": {"label": "Mobil logo konumu", "options__1": {"label": "Orta"}, "options__2": {"label": "Sol"}}, "logo_help": {"content": "Logonuzu [tema a<PERSON>](/editor?context=theme&category=logo) bölümünde düzenleyin"}, "sticky_header_type": {"label": "Sabit üstbilgi", "options__1": {"label": "Yok"}, "options__2": {"label": "Yukarı kaydırıldığında"}, "options__3": {"label": "Her zaman"}, "options__4": {"label": "Her zaman, logo boy<PERSON><PERSON><PERSON>"}}, "enable_country_selector": {"label": "<PERSON><PERSON><PERSON>/bö<PERSON> se<PERSON>", "info": "[Ülk<PERSON><PERSON>/bölgeleri yönet](/admin/settings/markets)"}, "enable_language_selector": {"label": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "info": "[<PERSON><PERSON><PERSON> y<PERSON>](/admin/settings/languages)"}, "header__1": {"content": "Renk"}, "menu_color_scheme": {"label": "<PERSON><PERSON> renk <PERSON>"}, "enable_customer_avatar": {"label": "Müşteri hesabı avatarı", "info": "Yalnızca müşteriler Shop ile oturum açtığında görünür [Müşteri hesaplarını yönet](/admin/settings/customer_accounts)"}, "header__utilities": {"content": "Yardımcı araçlar"}}}, "image-banner": {"name": "Görsel banner'ı", "settings": {"image": {"label": "Görsel 1"}, "image_2": {"label": "Görsel 2"}, "stack_images_on_mobile": {"label": "Görselleri üst üste ekle"}, "show_text_box": {"label": "Ka<PERSON>ayıcı"}, "image_overlay_opacity": {"label": "<PERSON><PERSON> <PERSON>şımı opaklığı"}, "show_text_below": {"label": "Ka<PERSON>ayıcı"}, "image_height": {"label": "Yükseklik", "options__1": {"label": "İlk görsele uyarla"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}}, "desktop_content_position": {"options__1": {"label": "Üst Sol"}, "options__2": {"label": "Üst Orta"}, "options__3": {"label": "Üst Sağ"}, "options__4": {"label": "Orta Sol"}, "options__5": {"label": "Orta Kısmın <PERSON>"}, "options__6": {"label": "Orta Sağ"}, "options__7": {"label": "Alt Sol"}, "options__8": {"label": "Alt Orta"}, "options__9": {"label": "Alt Sağ"}, "label": "<PERSON><PERSON>"}, "desktop_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON><PERSON>"}, "mobile_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON><PERSON>"}, "mobile": {"content": "<PERSON><PERSON>"}, "content": {"content": "İçerik"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık", "default": "Görsel banner'ı"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "Müşterilerle şablonlardaki banner görseller veya içerikler hakkında ayrıntıları paylaşın."}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}, "label": "Stil"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Etiket", "info": "Gizlemek için boş bırakın", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link_1": {"label": "Bağlantı"}, "button_style_secondary_1": {"label": "<PERSON><PERSON>ş çizgi stili"}, "button_label_2": {"label": "Etiket", "info": "Gizlemek için boş bırakın", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link_2": {"label": "Bağlantı"}, "button_style_secondary_2": {"label": "<PERSON><PERSON>ş çizgi stili"}, "header_1": {"content": "Düğme 1"}, "header_2": {"content": "Düğme 2"}}}}, "presets": {"name": "Görsel banner'ı"}}, "image-with-text": {"name": "<PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "height": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "label": "Yükseklik", "options__4": {"label": "Büyük"}}, "layout": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Görsel 2"}, "label": "<PERSON><PERSON><PERSON><PERSON>"}, "desktop_image_width": {"options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "label": "Genişlik"}, "desktop_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON><PERSON>"}, "desktop_content_position": {"options__1": {"label": "Üst"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Alt"}, "label": "<PERSON><PERSON>"}, "content_layout": {"options__1": {"label": "Çakışma yok"}, "options__2": {"label": "Çakışma"}, "label": "D<PERSON>zen"}, "mobile_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON>"}, "header": {"content": "İçerik"}, "header_colors": {"content": "Ren<PERSON>r"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık", "default": "<PERSON><PERSON>"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "<p>Metni bir görselle eşleyerek seçtiğiniz ürüne, koleksiyona veya blog gönderisine dikkat çekin. Stok durumu, stil hakkındaki ayrıntıları ekleyin, hatta inceleme sağlayın.</p>"}, "text_style": {"label": "Stil", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}}}}, "button": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label": {"label": "Etiket", "info": "Gizlemek için boş bırakın", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link": {"label": "Bağlantı"}, "outline_button": {"label": "<PERSON><PERSON>ş çizgi stili"}}}, "caption": {"name": "Alt yazı", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "Reklam sloganı ekleyin"}, "text_style": {"label": "Stil", "options__1": {"label": "Alt yazı"}, "options__2": {"label": "Büyük harf"}}, "caption_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}}}}}, "presets": {"name": "<PERSON><PERSON>"}}, "main-article": {"name": "Blog gönderisi", "blocks": {"featured_image": {"name": "<PERSON>ne <PERSON>", "settings": {"image_height": {"label": "Görsel yüksekliği", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}}}}, "title": {"name": "Başlık", "settings": {"blog_show_date": {"label": "<PERSON><PERSON><PERSON>"}, "blog_show_author": {"label": "<PERSON><PERSON>"}}}, "content": {"name": "İçerik"}, "share": {"name": "Paylaş", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "Paylaş"}}}}}, "main-blog": {"name": "Blog gönderileri", "settings": {"show_image": {"label": "<PERSON>ne <PERSON>"}, "show_date": {"label": "<PERSON><PERSON><PERSON>"}, "show_author": {"label": "<PERSON><PERSON>"}, "layout": {"label": "D<PERSON>zen", "options__1": {"label": "Izgara"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "image_height": {"label": "Görsel yüksekliği", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}}}}, "main-cart-footer": {"name": "Alt toplam", "blocks": {"subtotal": {"name": "Alt toplam fiyatı"}, "buttons": {"name": "<PERSON><PERSON><PERSON>"}}}, "main-cart-items": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "main-collection-banner": {"name": "Koleksiyon banner'ı", "settings": {"paragraph": {"content": "Koleksiyon bilgileri [yönetici panelinizde yönetilir](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "show_collection_image": {"label": "G<PERSON><PERSON><PERSON>"}}}, "main-collection-product-grid": {"name": "Ürün ızgarası", "settings": {"products_per_page": {"label": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>"}, "enable_filtering": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Filtreleri [Search & Discovery uygulaması](https://help.shopify.com/manual/online-store/search-and-discovery/filters) ile özelleştirin"}, "enable_sorting": {"label": "Sıralama"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "show_vendor": {"label": "Satıcı"}, "header__1": {"content": "Filtreleme ve sıralama"}, "header__3": {"content": "Ürün kartı"}, "enable_tags": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Filtreleri [Search & Discovery uygulaması](https://help.shopify.com/manual/online-store/search-and-discovery/filters) ile özelleştirin"}, "show_rating": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> derecelendirmeleri i<PERSON>in bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/collection-pages#product-grid-show-product-rating)"}, "columns_desktop": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "columns_mobile": {"label": "<PERSON><PERSON>", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "filter_type": {"label": "<PERSON><PERSON>re düzeni", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Çekmece"}}, "quick_add": {"label": "Hızlı ekleme", "options": {"option_1": "Yok", "option_2": "<PERSON><PERSON>", "option_3": "Toplu"}}}}, "main-list-collections": {"name": "Koleksiyonlar listesi sayfası", "settings": {"title": {"label": "Başlık", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "sort": {"label": "Koleksiyonları sırala", "options__1": {"label": "Alfabetik olarak, A-Z"}, "options__2": {"label": "Alfabetik olarak, Z-A"}, "options__3": {"label": "<PERSON><PERSON><PERSON>, yeniden eskiye"}, "options__4": {"label": "<PERSON><PERSON><PERSON>, eskiden yeniye"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON>, yüksekten düşüğe"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>, dü<PERSON><PERSON>kten yükseğe"}}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "columns_desktop": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "<PERSON><PERSON>", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "main-page": {"name": "Say<PERSON>"}, "main-password-footer": {"name": "<PERSON><PERSON><PERSON>"}, "main-password-header": {"name": "<PERSON><PERSON><PERSON> ü<PERSON>bilgisi", "settings": {"logo_help": {"content": "Logonuzu [tema a<PERSON>](/editor?context=theme&category=logo) bölümünde düzenleyin"}}}, "main-product": {"blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "<PERSON><PERSON> bloku"}, "text_style": {"label": "Stil", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}, "title": {"name": "Başlık"}, "price": {"name": "<PERSON><PERSON><PERSON>"}, "quantity_selector": {"name": "<PERSON><PERSON>"}, "variant_picker": {"name": "Varyasyon <PERSON>ç<PERSON>", "settings": {"picker_type": {"label": "Stil", "options__1": {"label": "Açılır liste"}, "options__2": {"label": "Seçenekler"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> [numune parçalar](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) hakkında daha fazla bilgi edinin.", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Yok"}}}}, "buy_buttons": {"name": "Satın al düğmeleri", "settings": {"show_dynamic_checkout": {"label": "Dinamik ödeme d<PERSON>eri", "info": "Müşteriler tercih ettikleri ödeme seçeneğini görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": " Hediye kartı gönderme seçenekleri", "info": "Müşteriler kişisel mesaj e<PERSON>ebilir ve gönderilme tarihini planlayabilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "<PERSON><PERSON><PERSON> alım stok durumu"}, "description": {"name": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "share": {"name": "Paylaş", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "Paylaş"}}}, "collapsible_tab": {"name": "Daraltılabilir <PERSON>", "settings": {"heading": {"label": "Başlık", "default": "Daraltılabilir <PERSON>"}, "content": {"label": "<PERSON><PERSON><PERSON>r içeriği"}, "page": {"label": "<PERSON><PERSON><PERSON>ı<PERSON> içeriği"}, "icon": {"options__1": {"label": "Hiç<PERSON>i"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Muz"}, "options__4": {"label": "Şişe"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON> balonu"}, "options__8": {"label": "<PERSON><PERSON>"}, "options__9": {"label": "Pan<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON> ürü<PERSON>"}, "options__11": {"label": "Süt ürünü içermez"}, "options__12": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__13": {"label": "Göz"}, "options__14": {"label": "Ateş"}, "options__15": {"label": "Glütensiz"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "Ütü"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Ş<PERSON>şek"}, "options__21": {"label": "<PERSON><PERSON><PERSON>"}, "options__22": {"label": "<PERSON><PERSON>"}, "options__23": {"label": "<PERSON><PERSON> pini"}, "options__24": {"label": "Kabuklu yemişsiz"}, "label": "<PERSON>m<PERSON>", "options__25": {"label": "Pantolon"}, "options__26": {"label": "<PERSON><PERSON>"}, "options__27": {"label": "<PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Uçak"}, "options__30": {"label": "<PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "<PERSON><PERSON>"}, "options__33": {"label": "<PERSON><PERSON>"}, "options__34": {"label": "İade"}, "options__35": {"label": "Cetvel"}, "options__36": {"label": "<PERSON><PERSON>"}, "options__37": {"label": "Gömlek"}, "options__38": {"label": "Ayakkabı"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON>"}, "options__41": {"label": "Yıld<PERSON>z"}, "options__42": {"label": "Kronometre"}, "options__43": {"label": "<PERSON><PERSON><PERSON>"}, "options__44": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pencere", "settings": {"link_label": {"label": "Bağlantı etiketi", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON>r bağ<PERSON>ı metni"}, "page": {"label": "Say<PERSON>"}}}, "rating": {"name": "<PERSON>rün puanı", "settings": {"paragraph": {"content": "<PERSON><PERSON><PERSON><PERSON> derecelendirmeleri i<PERSON>in bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types/product-pages#product-rating-block)"}}}, "complementary_products": {"name": "Tamamlayıcı ürünler", "settings": {"paragraph": {"content": "Tamamlayıcı ürünleri [Search & Discovery uygulamasında](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations) yönetin"}, "heading": {"label": "Başlık", "default": "<PERSON><PERSON><PERSON>"}, "make_collapsible_row": {"label": "Daraltılabilir <PERSON>"}, "icon": {"info": "Daraltılabilir satır seçildiğinde gösterilir"}, "product_list_limit": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "products_per_page": {"label": "<PERSON><PERSON> başına ürün say<PERSON>ı"}, "pagination_style": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options": {"option_1": "Noktalar", "option_2": "<PERSON><PERSON><PERSON>", "option_3": "<PERSON><PERSON><PERSON><PERSON>"}}, "product_card": {"heading": "Ürün kartı"}, "image_ratio": {"label": "Görsel oranı", "options": {"option_1": "Portre", "option_2": "<PERSON><PERSON>"}}, "enable_quick_add": {"label": "Hızlı ekle"}}}, "icon_with_text": {"name": "<PERSON><PERSON> i<PERSON>n simge", "settings": {"layout": {"label": "D<PERSON>zen", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}}, "heading": {"info": "Bu eşlemeyi gizlemek için boş bırakın"}, "icon_1": {"label": "<PERSON>m<PERSON>"}, "image_1": {"label": "G<PERSON><PERSON><PERSON>"}, "heading_1": {"label": "Başlık", "default": "Başlık"}, "icon_2": {"label": "<PERSON>m<PERSON>"}, "image_2": {"label": "G<PERSON><PERSON><PERSON>"}, "heading_2": {"label": "Başlık", "default": "Başlık"}, "icon_3": {"label": "<PERSON>m<PERSON>"}, "image_3": {"label": "G<PERSON><PERSON><PERSON>"}, "heading_3": {"label": "Başlık", "default": "Başlık"}, "pairing_1": {"label": "Eşleme 1", "info": "<PERSON> eşleme için bir simge seçin veya bir gö<PERSON><PERSON> e<PERSON>in"}, "pairing_2": {"label": "Eşleme 2"}, "pairing_3": {"label": "Eşleme 3"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}, "inventory": {"name": "<PERSON><PERSON><PERSON> du<PERSON>", "settings": {"text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}, "inventory_threshold": {"label": "Düşük envanter eşiği"}, "show_inventory_quantity": {"label": "<PERSON><PERSON><PERSON>"}}}}, "settings": {"header": {"content": "<PERSON><PERSON><PERSON>"}, "enable_video_looping": {"label": "Video döngüsü"}, "enable_sticky_info": {"label": "Sabit içerik"}, "hide_variants": {"label": "Varyasyon medyası seçildiğinde diğer varyasyon medyalarını gizle"}, "gallery_layout": {"label": "D<PERSON>zen", "options__1": {"label": "Üst üste"}, "options__2": {"label": "2 sütun"}, "options__3": {"label": "Küçük resimler"}, "options__4": {"label": "Küçük resim döngüsü"}}, "media_size": {"label": "Genişlik", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}}, "mobile_thumbnails": {"label": "<PERSON><PERSON>", "options__1": {"label": "2 sütun"}, "options__2": {"label": "Küçük resimleri göster"}, "options__3": {"label": "Küçük resimleri gizle"}}, "media_position": {"label": "<PERSON><PERSON>", "options__1": {"label": "Sol"}, "options__2": {"label": "Sağ"}}, "image_zoom": {"label": "Yakınlaştırma", "options__1": {"label": "Lightbox'ı aç"}, "options__2": {"label": "Tıkla ve imleci üzerine getir"}, "options__3": {"label": "Yakınlaştırma yok"}}, "constrain_to_viewport": {"label": "Ekran yüksekliğiyle sınırla"}, "media_fit": {"label": "Sığdır", "options__1": {"label": "Orijinal"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}}, "name": "<PERSON><PERSON><PERSON>n bilgi<PERSON>i"}, "main-search": {"name": "<PERSON><PERSON>", "settings": {"image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "show_vendor": {"label": "Satıcı"}, "header__1": {"content": "Ürün kartı"}, "header__2": {"content": "Blog kartı"}, "article_show_date": {"label": "<PERSON><PERSON><PERSON>"}, "article_show_author": {"label": "<PERSON><PERSON>"}, "show_rating": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> derecelendirmeleri i<PERSON>in bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/en/manual/online-store/themes/theme-structure/page-types/search-page)"}, "columns_desktop": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "columns_mobile": {"label": "<PERSON><PERSON>", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multicolumn": {"name": "Çoklu sütun", "settings": {"title": {"label": "Başlık", "default": "Çoklu sütun"}, "image_width": {"label": "Genişlik", "options__1": {"label": "Sütun genişliğinin üçte biri"}, "options__2": {"label": "Sütun genişliğinin yarısı"}, "options__3": {"label": "Sütun genişliğinin tama<PERSON>ı"}}, "image_ratio": {"label": "<PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "column_alignment": {"label": "<PERSON><PERSON><PERSON>", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}}, "background_style": {"label": "İkincil arka plan", "options__1": {"label": "Hiç<PERSON>i"}, "options__2": {"label": "Sütun arka planı olarak göster"}}, "button_label": {"label": "Etiket", "default": "<PERSON><PERSON><PERSON><PERSON>", "info": "Gizlemek için boş bırakın"}, "button_link": {"label": "Bağlantı"}, "swipe_on_mobile": {"label": "Döngü"}, "columns_desktop": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "header_mobile": {"content": "<PERSON><PERSON>"}, "columns_mobile": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "1"}, "options__2": {"label": "2"}}, "header_text": {"content": "Başlık"}, "header_image": {"content": "G<PERSON><PERSON><PERSON>"}, "header_layout": {"content": "D<PERSON>zen"}, "header_button": {"content": "<PERSON><PERSON><PERSON><PERSON>"}}, "blocks": {"column": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "title": {"label": "Başlık", "default": "<PERSON><PERSON><PERSON>"}, "text": {"label": "<PERSON><PERSON>ı<PERSON><PERSON>", "default": "<p>Metni bir görselle eşleyerek seçtiğiniz ürüne, koleksiyona veya blog gönderisine dikkat çekin. Stok durumu, stil hakkındaki ayrıntıları ekleyin, hatta inceleme sağlayın.</p>"}, "link_label": {"label": "Bağlantı etiketi", "info": "Gizlemek için boş bırakın"}, "link": {"label": "Bağlantı"}}}}, "presets": {"name": "Çoklu sütun"}}, "newsletter": {"name": "E-posta kaydı", "settings": {"full_width": {"label": "<PERSON> g<PERSON>"}, "paragraph": {"content": "<PERSON><PERSON><PERSON> ekleme [mü<PERSON><PERSON>i profilleri](https://help.shopify.com/manual/customers/manage-customers)"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık", "default": "E-posta listemize kaydolun"}}}, "paragraph": {"name": "<PERSON><PERSON>", "settings": {"paragraph": {"label": "<PERSON><PERSON>", "default": "<p><PERSON><PERSON> kole<PERSON>yonlar ve özel tekliflerden ilk siz haberdar olun.</p>"}}}, "email_form": {"name": "E-posta formu"}}, "presets": {"name": "E-posta kaydı"}}, "page": {"name": "Say<PERSON>", "settings": {"page": {"label": "Say<PERSON>"}}, "presets": {"name": "Say<PERSON>"}}, "rich-text": {"name": "<PERSON><PERSON> metin", "settings": {"full_width": {"label": "<PERSON> g<PERSON>"}, "desktop_content_position": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "İçerik konumu"}, "content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "İçerik hizalaması"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık", "default": "<PERSON><PERSON><PERSON><PERSON><PERSON> bah<PERSON>"}}}, "text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "<p>Müşterilerinizle markanız hakkında bilgi payla<PERSON>ın. <PERSON>r<PERSON>n açı<PERSON> girin, du<PERSON>ru paylaşın veya mağazanıza gelen müşterileri karşılayın.</p>"}}}, "buttons": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"button_label_1": {"label": "Etiket", "info": "Gizlemek için boş bırakın", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "button_link_1": {"label": "Bağlantı"}, "button_style_secondary_1": {"label": "<PERSON><PERSON>ş çizgi stili"}, "button_label_2": {"label": "Etiket", "info": "Gizlemek için et<PERSON> boş bırakın"}, "button_link_2": {"label": "Bağlantı"}, "button_style_secondary_2": {"label": "<PERSON><PERSON>ş çizgi stili"}, "header_button1": {"content": "Düğme 1"}, "header_button2": {"content": "Düğme 2"}}}, "caption": {"name": "Alt yazı", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "Reklam sloganı ekleyin"}, "text_style": {"label": "Stil", "options__1": {"label": "Alt yazı"}, "options__2": {"label": "Büyük harf"}}, "caption_size": {"label": "<PERSON><PERSON>", "options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}}}}}, "presets": {"name": "<PERSON><PERSON> metin"}}, "apps": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"include_margins": {"label": "B<PERSON>lüm kenar boşluklarını temayla aynı yap"}}, "presets": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Başlık", "default": "Video"}, "cover_image": {"label": "Kapak gö<PERSON>"}, "video_url": {"label": "URL", "info": "YouTube veya Vimeo URL'si kullan"}, "description": {"label": "Video alternatif metni", "info": "Ekran okuyucu kullanan müşteriler için videoyu açıklayın"}, "image_padding": {"label": "<PERSON><PERSON><PERSON><PERSON> do<PERSON><PERSON> e<PERSON>", "info": "Kapak görselinizin kırpılmasını istemiyorsanız görsel dolgusunu seçin."}, "full_width": {"label": "<PERSON> g<PERSON>"}, "video": {"label": "Video"}, "enable_video_looping": {"label": "Video döngüsü"}, "header__1": {"content": "Shopify'da barındırılan videolar"}, "header__2": {"content": "URL'den video ekle"}, "header__3": {"content": "D<PERSON>zen"}, "paragraph": {"content": "Shopify'da barındırılan bir video seçilmediğinde gösterilir"}}, "presets": {"name": "Video"}}, "featured-product": {"name": "<PERSON><PERSON>ü<PERSON>", "blocks": {"text": {"name": "<PERSON><PERSON>", "settings": {"text": {"label": "<PERSON><PERSON>", "default": "<PERSON><PERSON> bloku"}, "text_style": {"label": "Stil", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}, "title": {"name": "Başlık"}, "price": {"name": "<PERSON><PERSON><PERSON>"}, "quantity_selector": {"name": "<PERSON><PERSON>"}, "variant_picker": {"name": "Varyasyon <PERSON>ç<PERSON>", "settings": {"picker_type": {"label": "Stil", "options__1": {"label": "Açılır <PERSON>ü"}, "options__2": {"label": "Seçenekler"}}, "swatch_shape": {"label": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> [numune parçalar](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) hakkında daha fazla bilgi edinin.", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Yok"}}}}, "buy_buttons": {"name": "Satın al düğmeleri", "settings": {"show_dynamic_checkout": {"label": "Dinamik ödeme d<PERSON>eri", "info": "Müşteriler tercih ettikleri ödeme seçeneğini görür. [Daha fazla bilgi edinin](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "<PERSON><PERSON>ı<PERSON><PERSON>"}, "share": {"name": "Paylaş", "settings": {"featured_image_info": {"content": "Sosyal medya gönderilerine bağlantı eklerseniz sayfanın öne çıkan görseli, önizleme görseli olarak gösterilir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "<PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON> ve açıklaması, önizleme görseline dahildir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "<PERSON><PERSON>", "default": "Paylaş"}}}, "rating": {"name": "<PERSON>rün puanı", "settings": {"paragraph": {"content": "<PERSON><PERSON><PERSON><PERSON> derecelendirmeleri i<PERSON>in bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}, "sku": {"name": "SKU", "settings": {"text_style": {"label": "<PERSON><PERSON> stili", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "options__3": {"label": "Büyük harf"}}}}}, "settings": {"product": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "secondary_background": {"label": "İkincil arka plan"}, "header": {"content": "<PERSON><PERSON><PERSON>"}, "enable_video_looping": {"label": "Video döngüsü"}, "hide_variants": {"label": "Masaüstünde seçimi kaldırılmış varyasyonların medyasını gizle"}, "media_position": {"label": "<PERSON><PERSON>", "info": "<PERSON><PERSON>, mobil cihazlar için otomatik olarak optimize edilir.", "options__1": {"label": "Sol"}, "options__2": {"label": "Sağ"}}}, "presets": {"name": "<PERSON><PERSON>ü<PERSON>"}}, "email-signup-banner": {"name": "E-posta kaydı banner'ı", "settings": {"paragraph": {"content": "<PERSON><PERSON><PERSON> ekleme [mü<PERSON><PERSON>i profilleri](https://help.shopify.com/manual/customers/manage-customers)"}, "image": {"label": "Arka plan resmi"}, "show_background_image": {"label": "Arka plan resmini <PERSON>"}, "show_text_box": {"label": "Ka<PERSON>ayıcı"}, "image_overlay_opacity": {"label": "<PERSON><PERSON> <PERSON>şımı opaklığı"}, "show_text_below": {"label": "Görselin altına metin ekle"}, "image_height": {"label": "Yükseklik", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}}, "desktop_content_position": {"options__1": {"label": "Üst Sol"}, "options__2": {"label": "Üst Orta"}, "options__3": {"label": "Üst Sağ"}, "options__4": {"label": "Orta Sol"}, "options__5": {"label": "Orta Kısmın <PERSON>"}, "options__6": {"label": "Orta Sağ"}, "options__7": {"label": "Alt Sol"}, "options__8": {"label": "Alt Orta"}, "options__9": {"label": "Alt Sağ"}, "label": "<PERSON><PERSON>"}, "desktop_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON><PERSON>"}, "header": {"content": "<PERSON><PERSON>"}, "mobile_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON><PERSON>"}, "color_scheme": {"info": "Kapsayıcı gösterildiğinde görünür."}, "content_header": {"content": "İçerik"}}, "blocks": {"heading": {"name": "Başlık", "settings": {"heading": {"label": "Başlık", "default": "Yakında açılıyor"}}}, "paragraph": {"name": "<PERSON><PERSON>", "settings": {"paragraph": {"label": "<PERSON><PERSON>", "default": "<p><PERSON><PERSON>klarımızı ilk siz öğrenin.</p>"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "label": "Stil"}}}, "email_form": {"name": "E-posta formu"}}, "presets": {"name": "E-posta kaydı banner'ı"}}, "slideshow": {"name": "<PERSON><PERSON><PERSON> gö<PERSON>", "settings": {"layout": {"label": "D<PERSON>zen", "options__1": {"label": "<PERSON> g<PERSON>"}, "options__2": {"label": "Say<PERSON>"}}, "slide_height": {"label": "Yükseklik", "options__1": {"label": "İlk görsele uyarla"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}}, "slider_visual": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Noktalar"}, "options__3": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Slaytları otomatik olarak döndür"}, "change_slides_speed": {"label": "Slaytları şu zaman aralığında değiştir:"}, "show_text_below": {"label": "Görselin altına metin ekle"}, "mobile": {"content": "<PERSON><PERSON>"}, "accessibility": {"content": "Erişilebilirlik", "label": "Slayt gösterisi açıklaması", "info": "Ekran okuyucu kullanan müşteriler için slayt gösterisini açıklayın", "default": "Markamız hakkında slayt gösterisi"}}, "blocks": {"slide": {"name": "<PERSON><PERSON><PERSON>", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "heading": {"label": "Başlık", "default": "Görsel slaytı"}, "subheading": {"label": "Alt başlık", "default": "Görsellerle marka öykünüzü anlatın"}, "button_label": {"label": "Etiket", "info": "Gizlemek için boş bırakın", "default": "<PERSON><PERSON><PERSON><PERSON>"}, "link": {"label": "Bağlantı"}, "secondary_style": {"label": "<PERSON><PERSON>ş çizgi stili"}, "box_align": {"label": "İçerik konumu", "options__1": {"label": "Üst sol"}, "options__2": {"label": "Üst orta"}, "options__3": {"label": "Üst sağ"}, "options__4": {"label": "Orta sol"}, "options__5": {"label": "<PERSON><PERSON> kısmın ortası"}, "options__6": {"label": "<PERSON><PERSON>"}, "options__7": {"label": "Alt sol"}, "options__8": {"label": "Alt orta"}, "options__9": {"label": "Alt sağ"}}, "show_text_box": {"label": "Ka<PERSON>ayıcı"}, "text_alignment": {"label": "İçerik hizalaması", "option_1": {"label": "Sol"}, "option_2": {"label": "Orta"}, "option_3": {"label": "Sağ"}}, "image_overlay_opacity": {"label": "<PERSON><PERSON> <PERSON>şımı opaklığı"}, "text_alignment_mobile": {"label": "Mobil içerik hizalaması", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}}, "header_button": {"content": "<PERSON><PERSON><PERSON><PERSON>"}, "header_layout": {"content": "D<PERSON>zen"}, "header_text": {"content": "<PERSON><PERSON>"}, "header_colors": {"content": "Ren<PERSON>r"}}}}, "presets": {"name": "<PERSON><PERSON><PERSON> gö<PERSON>"}}, "collapsible_content": {"name": "Daraltılabilir içerik", "settings": {"caption": {"label": "Alt yazı"}, "heading": {"label": "Başlık", "default": "Daraltılabilir içerik"}, "heading_alignment": {"label": "Başlık hizalaması", "options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}}, "layout": {"label": "Ka<PERSON>ayıcı", "options__1": {"label": "Kapsayıcı yok"}, "options__2": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__3": {"label": "B<PERSON>lüm ka<PERSON>ı"}}, "container_color_scheme": {"label": "Kapsayıcı renk şeması"}, "open_first_collapsible_row": {"label": "İlk satırı aç"}, "header": {"content": "G<PERSON><PERSON><PERSON>"}, "image": {"label": "G<PERSON><PERSON><PERSON>"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Büyük"}}, "desktop_layout": {"label": "<PERSON><PERSON><PERSON><PERSON>", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "layout_header": {"content": "D<PERSON>zen"}, "section_color_scheme": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> renk düzeni"}}, "blocks": {"collapsible_row": {"name": "Daraltılabilir <PERSON>", "settings": {"heading": {"label": "Başlık", "default": "Daraltılabilir <PERSON>"}, "row_content": {"label": "<PERSON><PERSON><PERSON>r içeriği"}, "page": {"label": "<PERSON><PERSON><PERSON>ı<PERSON> içeriği"}, "icon": {"label": "<PERSON>m<PERSON>", "options__1": {"label": "Hiç<PERSON>i"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Muz"}, "options__4": {"label": "Şişe"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON><PERSON>"}, "options__7": {"label": "<PERSON><PERSON><PERSON> balonu"}, "options__8": {"label": "<PERSON><PERSON>"}, "options__9": {"label": "Pan<PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON> ürü<PERSON>"}, "options__11": {"label": "Süt ürünü içermez"}, "options__12": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__13": {"label": "Göz"}, "options__14": {"label": "Ateş"}, "options__15": {"label": "Glütensiz"}, "options__16": {"label": "<PERSON><PERSON><PERSON>"}, "options__17": {"label": "Ütü"}, "options__18": {"label": "<PERSON><PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Ş<PERSON>şek"}, "options__21": {"label": "<PERSON><PERSON><PERSON>"}, "options__22": {"label": "<PERSON><PERSON>"}, "options__23": {"label": "<PERSON><PERSON> pini"}, "options__24": {"label": "Kabuklu yemişsiz"}, "options__25": {"label": "Pantolon"}, "options__26": {"label": "<PERSON><PERSON>"}, "options__27": {"label": "<PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Uçak"}, "options__30": {"label": "<PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "<PERSON><PERSON>"}, "options__33": {"label": "<PERSON><PERSON>"}, "options__34": {"label": "İade"}, "options__35": {"label": "Cetvel"}, "options__36": {"label": "<PERSON><PERSON>"}, "options__37": {"label": "Gömlek"}, "options__38": {"label": "Ayakkabı"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON>"}, "options__41": {"label": "Yıld<PERSON>z"}, "options__42": {"label": "Kronometre"}, "options__43": {"label": "<PERSON><PERSON><PERSON>"}, "options__44": {"label": "<PERSON><PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Daraltılabilir içerik"}}, "main-account": {"name": "<PERSON><PERSON><PERSON>"}, "main-activate-account": {"name": "<PERSON><PERSON><PERSON>"}, "main-addresses": {"name": "<PERSON><PERSON><PERSON>"}, "main-login": {"name": "<PERSON><PERSON><PERSON>", "shop_login_button": {"enable": "Shop ile giriş yapmayı etkinleştirin"}}, "main-order": {"name": "Sipariş"}, "main-register": {"name": "<PERSON><PERSON><PERSON>"}, "main-reset-password": {"name": "<PERSON><PERSON><PERSON>"}, "related-products": {"name": "Alakalı ürünler", "settings": {"heading": {"label": "Başlık"}, "products_to_show": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "columns_desktop": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "paragraph__1": {"content": "<PERSON><PERSON><PERSON><PERSON> [Search & Discovery uygulamasında](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations) yönetilebilir", "default": "<PERSON><PERSON> de hoşunuza gidebilir:"}, "header__2": {"content": "Ürün kartı"}, "image_ratio": {"label": "Görsel oranı", "options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Portre"}, "options__3": {"label": "<PERSON><PERSON>"}}, "show_secondary_image": {"label": "Üstüne gelindiğinde ikinci görseli göster"}, "show_vendor": {"label": "Satıcı"}, "show_rating": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON><PERSON><PERSON> derecelendirmeleri i<PERSON>in bir uygulama gereklidir. [Daha fazla bilgi edinin](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-product-recommendations)"}, "columns_mobile": {"label": "<PERSON><PERSON>", "options__1": {"label": "1"}, "options__2": {"label": "2"}}}}, "multirow": {"name": "Çok satırlı", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "image_height": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Küçük"}, "options__3": {"label": "Orta"}, "options__4": {"label": "Büyük"}, "label": "Yükseklik"}, "desktop_image_width": {"options__1": {"label": "Küçük"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Büyük"}, "label": "Genişlik"}, "text_style": {"options__1": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__2": {"label": "Alt yazı"}, "label": "<PERSON><PERSON> stili"}, "button_style": {"options__1": {"label": "<PERSON>bit dü<PERSON>"}, "options__2": {"label": "<PERSON><PERSON>ş çizgi düğmes<PERSON>"}, "label": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>i"}, "desktop_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON><PERSON>"}, "desktop_content_position": {"options__1": {"label": "Üst"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Alt"}, "label": "<PERSON><PERSON>"}, "image_layout": {"options__1": {"label": "Alternatif (soldan)"}, "options__2": {"label": "Alternatif (sağdan)"}, "options__3": {"label": "Sola hizalanmış"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "label": "<PERSON><PERSON><PERSON><PERSON>"}, "container_color_scheme": {"label": "Kapsayıcı renk şeması"}, "mobile_content_alignment": {"options__1": {"label": "Sol"}, "options__2": {"label": "Orta"}, "options__3": {"label": "Sağ"}, "label": "<PERSON><PERSON>"}, "header": {"content": "G<PERSON><PERSON><PERSON>"}, "header_2": {"content": "İçerik"}, "header_3": {"content": "Ren<PERSON>r"}}, "blocks": {"row": {"name": "Satır", "settings": {"image": {"label": "G<PERSON><PERSON><PERSON>"}, "caption": {"label": "Alt yazı", "default": "Alt yazı"}, "heading": {"label": "Başlık", "default": "Satır"}, "text": {"label": "<PERSON><PERSON>", "default": "<p>Metni bir görselle eşleyerek seçtiğiniz ürüne, koleksiyona veya blog gönderisine dikkat çekin. Stok durumu, stil hakkındaki ayrıntıları ekleyin, hatta inceleme sağlayın.</p>"}, "button_label": {"label": "<PERSON><PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>", "info": "Gizlemek için boş bırakın"}, "button_link": {"label": "<PERSON><PERSON><PERSON><PERSON> bağlantısı"}}}}, "presets": {"name": "Çok satırlı"}}, "quick-order-list": {"name": "Hızlı sipariş listesi", "settings": {"show_image": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "show_sku": {"label": "SKU'lar"}, "variants_per_page": {"label": "<PERSON><PERSON> b<PERSON><PERSON><PERSON>na <PERSON>"}}, "presets": {"name": "Hızlı sipariş listesi"}}}}