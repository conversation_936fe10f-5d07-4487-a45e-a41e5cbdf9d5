/**
 * Luxury Animations & Interactions
 * Etsy-inspired glow effects and smooth interactions
 */

class LuxuryAnimations {
  constructor() {
    this.init();
  }

  init() {
    this.setupGlowEffects();
    this.setupHoverAnimations();
    this.setupScrollAnimations();
    this.setupParallaxEffects();
    this.setupButtonRipples();
  }

  // Glow effects for luxury elements
  setupGlowEffects() {
    const glowElements = document.querySelectorAll('.ftac-btn-luxury, .ftac-card-luxury');
    
    glowElements.forEach(element => {
      element.addEventListener('mouseenter', (e) => {
        this.addGlowEffect(e.target);
      });
      
      element.addEventListener('mouseleave', (e) => {
        this.removeGlowEffect(e.target);
      });
    });
  }

  addGlowEffect(element) {
    element.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    element.style.filter = 'brightness(1.1) saturate(1.2)';
    
    // Add dynamic glow based on element type
    if (element.classList.contains('ftac-btn-primary')) {
      element.style.boxShadow = '0 8px 32px rgba(255, 200, 221, 0.4), 0 0 20px rgba(255, 200, 221, 0.3)';
    } else if (element.classList.contains('ftac-btn-secondary')) {
      element.style.boxShadow = '0 8px 32px rgba(128, 237, 153, 0.4), 0 0 20px rgba(128, 237, 153, 0.3)';
    } else {
      element.style.boxShadow = '0 12px 40px rgba(44, 44, 44, 0.15), 0 0 20px rgba(255, 200, 221, 0.2)';
    }
  }

  removeGlowEffect(element) {
    element.style.filter = '';
    element.style.boxShadow = '';
  }

  // Smooth hover animations
  setupHoverAnimations() {
    const hoverElements = document.querySelectorAll('.ftac-hover-lift');
    
    hoverElements.forEach(element => {
      element.addEventListener('mouseenter', () => {
        element.style.transform = 'translateY(-4px)';
      });
      
      element.addEventListener('mouseleave', () => {
        element.style.transform = 'translateY(0)';
      });
    });
  }

  // Scroll-triggered animations
  setupScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animateOnScroll(entry.target);
        }
      });
    }, observerOptions);

    // Observe elements that should animate on scroll
    const animateElements = document.querySelectorAll('.ftac-card-luxury, .ftac-btn-luxury, .ftac-heading-hero');
    animateElements.forEach(el => observer.observe(el));
  }

  animateOnScroll(element) {
    element.style.opacity = '0';
    element.style.transform = 'translateY(30px)';
    element.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
    
    // Trigger animation
    requestAnimationFrame(() => {
      element.style.opacity = '1';
      element.style.transform = 'translateY(0)';
    });
  }

  // Subtle parallax effects
  setupParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.ftac-parallax');
    
    if (parallaxElements.length === 0) return;

    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      const rate = scrolled * -0.5;
      
      parallaxElements.forEach(element => {
        element.style.transform = `translateY(${rate}px)`;
      });
    });
  }

  // Button ripple effects
  setupButtonRipples() {
    const buttons = document.querySelectorAll('.ftac-btn-luxury');
    
    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        this.createRipple(e);
      });
    });
  }

  createRipple(event) {
    const button = event.currentTarget;
    const circle = document.createElement('span');
    const diameter = Math.max(button.clientWidth, button.clientHeight);
    const radius = diameter / 2;

    circle.style.width = circle.style.height = `${diameter}px`;
    circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
    circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
    circle.classList.add('ripple');

    const ripple = button.getElementsByClassName('ripple')[0];
    if (ripple) {
      ripple.remove();
    }

    button.appendChild(circle);
  }

  // Floating animation for special elements
  setupFloatingAnimation() {
    const floatingElements = document.querySelectorAll('.ftac-floating');
    
    floatingElements.forEach((element, index) => {
      element.style.animation = `ftacFloat 3s ease-in-out infinite`;
      element.style.animationDelay = `${index * 0.5}s`;
    });
  }

  // Typewriter effect for special text
  typeWriter(element, text, speed = 50) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
      if (i < text.length) {
        element.innerHTML += text.charAt(i);
        i++;
        setTimeout(type, speed);
      }
    }
    
    type();
  }

  // Smooth scroll to element
  smoothScrollTo(targetId, offset = 0) {
    const target = document.getElementById(targetId);
    if (!target) return;

    const targetPosition = target.offsetTop - offset;
    
    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });
  }

  // Add sparkle effect to elements
  addSparkleEffect(element) {
    const sparkle = document.createElement('div');
    sparkle.className = 'ftac-sparkle';
    sparkle.style.position = 'absolute';
    sparkle.style.width = '4px';
    sparkle.style.height = '4px';
    sparkle.style.background = 'var(--ftac-rose-gold)';
    sparkle.style.borderRadius = '50%';
    sparkle.style.pointerEvents = 'none';
    sparkle.style.animation = 'ftacSparkle 1s ease-out forwards';
    
    const rect = element.getBoundingClientRect();
    sparkle.style.left = `${Math.random() * rect.width}px`;
    sparkle.style.top = `${Math.random() * rect.height}px`;
    
    element.style.position = 'relative';
    element.appendChild(sparkle);
    
    setTimeout(() => {
      sparkle.remove();
    }, 1000);
  }

  // Initialize luxury loading animation
  showLuxuryLoader() {
    const loader = document.createElement('div');
    loader.className = 'ftac-luxury-loader';
    loader.innerHTML = `
      <div class="ftac-spinner-luxury"></div>
      <p style="margin-top: 16px; font-family: var(--ftac-font-script); color: var(--ftac-rose-gold);">
        Creating magic...
      </p>
    `;
    
    loader.style.position = 'fixed';
    loader.style.top = '0';
    loader.style.left = '0';
    loader.style.width = '100%';
    loader.style.height = '100%';
    loader.style.background = 'rgba(255, 248, 231, 0.95)';
    loader.style.display = 'flex';
    loader.style.flexDirection = 'column';
    loader.style.alignItems = 'center';
    loader.style.justifyContent = 'center';
    loader.style.zIndex = '9999';
    
    document.body.appendChild(loader);
    
    return loader;
  }

  hideLuxuryLoader(loader) {
    if (loader) {
      loader.style.opacity = '0';
      loader.style.transition = 'opacity 0.5s ease';
      setTimeout(() => {
        loader.remove();
      }, 500);
    }
  }
}

// CSS animations to be injected
const luxuryAnimationCSS = `
  .ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
  }

  @keyframes ripple-animation {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }

  @keyframes ftacFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes ftacSparkle {
    0% { 
      opacity: 1; 
      transform: scale(0) rotate(0deg); 
    }
    50% { 
      opacity: 1; 
      transform: scale(1) rotate(180deg); 
    }
    100% { 
      opacity: 0; 
      transform: scale(0) rotate(360deg); 
    }
  }

  .ftac-fade-in {
    animation: ftacFadeIn 0.6s ease-out;
  }

  @keyframes ftacFadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = luxuryAnimationCSS;
document.head.appendChild(style);

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new LuxuryAnimations();
});

// Export for use in other scripts
window.LuxuryAnimations = LuxuryAnimations;
