{% comment %}
  FTAC Admin Panel Section
  
  This section provides an admin interface for <PERSON> to manage:
  - Product bundle configurations
  - Access code generation and management
  - Customer access tracking
  - Order history integration
  
  This should only be accessible to admin users.
{% endcomment %}

{% comment %} Check if current user is admin {% endcomment %}
{% assign is_admin = false %}
{% if customer and customer.email == '<EMAIL>' %}
  {% assign is_admin = true %}
{% endif %}

{% if is_admin %}
<section class="ftac-admin-panel">
  <div class="ftac-admin-panel__container">
    <div class="ftac-admin-panel__header">
      <h1 class="ftac-admin-panel__title">FTAC Admin Panel</h1>
      <p class="ftac-admin-panel__subtitle">
        Manage product bundles, access codes, and customer access
      </p>
    </div>

    <!-- Admin Navigation Tabs -->
    <div class="ftac-admin-panel__tabs">
      <button class="ftac-admin-panel__tab ftac-admin-panel__tab--active" data-tab="products">
        Product Management
      </button>
      <button class="ftac-admin-panel__tab" data-tab="access-codes">
        Access Codes
      </button>
      <button class="ftac-admin-panel__tab" data-tab="customers">
        Customer Access
      </button>
      <button class="ftac-admin-panel__tab" data-tab="orders">
        Order Integration
      </button>
    </div>

    <!-- Product Management Tab -->
    <div class="ftac-admin-panel__content" id="products-tab">
      <div class="ftac-admin-panel__section">
        <h2 class="ftac-admin-panel__section-title">Product Bundle Configuration</h2>
        
        <div class="ftac-admin-panel__product-list">
          <!-- Future Tech Foundations -->
          <div class="ftac-admin-panel__product-card">
            <div class="ftac-admin-panel__product-header">
              <h3>Future Tech Foundations</h3>
              <span class="ftac-admin-panel__product-type">Basic Bundle</span>
              <span class="ftac-admin-panel__product-price">$14.99</span>
            </div>
            <div class="ftac-admin-panel__product-config">
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" checked disabled> App Access
                </label>
              </div>
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" checked disabled> PDF Downloads (3 files)
                </label>
              </div>
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" disabled> Physical Items
                </label>
              </div>
            </div>
            <div class="ftac-admin-panel__product-actions">
              <button class="ftac-admin-panel__btn ftac-admin-panel__btn--secondary">
                Edit Configuration
              </button>
              <button class="ftac-admin-panel__btn ftac-admin-panel__btn--primary">
                Generate Access Code
              </button>
            </div>
          </div>

          <!-- Advanced Tech Mastery -->
          <div class="ftac-admin-panel__product-card">
            <div class="ftac-admin-panel__product-header">
              <h3>Advanced Tech Mastery</h3>
              <span class="ftac-admin-panel__product-type">Premium Bundle</span>
              <span class="ftac-admin-panel__product-price">$29.99</span>
            </div>
            <div class="ftac-admin-panel__product-config">
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" checked disabled> App Access
                </label>
              </div>
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" checked disabled> PDF Downloads (5 files)
                </label>
              </div>
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" checked disabled> Physical Items (2 items)
                </label>
              </div>
            </div>
            <div class="ftac-admin-panel__product-actions">
              <button class="ftac-admin-panel__btn ftac-admin-panel__btn--secondary">
                Edit Configuration
              </button>
              <button class="ftac-admin-panel__btn ftac-admin-panel__btn--primary">
                Generate Access Code
              </button>
            </div>
          </div>

          <!-- Test Product -->
          <div class="ftac-admin-panel__product-card">
            <div class="ftac-admin-panel__product-header">
              <h3>Test Product</h3>
              <span class="ftac-admin-panel__product-type">Basic Bundle</span>
              <span class="ftac-admin-panel__product-price">$14.99</span>
            </div>
            <div class="ftac-admin-panel__product-config">
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" checked disabled> App Access
                </label>
              </div>
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" disabled> PDF Downloads
                </label>
              </div>
              <div class="ftac-admin-panel__config-item">
                <label>
                  <input type="checkbox" disabled> Physical Items
                </label>
              </div>
            </div>
            <div class="ftac-admin-panel__product-actions">
              <button class="ftac-admin-panel__btn ftac-admin-panel__btn--secondary">
                Edit Configuration
              </button>
              <button class="ftac-admin-panel__btn ftac-admin-panel__btn--primary">
                Generate Access Code
              </button>
            </div>
          </div>
        </div>

        <div class="ftac-admin-panel__add-product">
          <button class="ftac-admin-panel__btn ftac-admin-panel__btn--primary">
            + Add New Product Bundle
          </button>
        </div>
      </div>
    </div>

    <!-- Access Codes Tab -->
    <div class="ftac-admin-panel__content" id="access-codes-tab" style="display: none;">
      <div class="ftac-admin-panel__section">
        <h2 class="ftac-admin-panel__section-title">Access Code Management</h2>
        
        <div class="ftac-admin-panel__access-code-generator">
          <h3>Generate New Access Code</h3>
          <form class="ftac-admin-panel__form">
            <div class="ftac-admin-panel__form-group">
              <label for="product-select">Product:</label>
              <select id="product-select" class="ftac-admin-panel__select">
                <option value="future-tech-foundations">Future Tech Foundations</option>
                <option value="advanced-tech-mastery">Advanced Tech Mastery</option>
                <option value="tech-leadership-suite">Tech Leadership Suite</option>
                <option value="test-product">Test Product</option>
              </select>
            </div>
            <div class="ftac-admin-panel__form-group">
              <label for="customer-email">Customer Email:</label>
              <input type="email" id="customer-email" class="ftac-admin-panel__input" placeholder="<EMAIL>">
            </div>
            <div class="ftac-admin-panel__form-group">
              <label for="etsy-order-id">Etsy Order ID:</label>
              <input type="text" id="etsy-order-id" class="ftac-admin-panel__input" placeholder="ETY123456789">
            </div>
            <button type="submit" class="ftac-admin-panel__btn ftac-admin-panel__btn--primary">
              Generate Access Code
            </button>
          </form>
        </div>

        <div class="ftac-admin-panel__access-code-list">
          <h3>Recent Access Codes</h3>
          <div class="ftac-admin-panel__code-item">
            <div class="ftac-admin-panel__code-info">
              <code>FTAC-DEMO-TEST-1234</code>
              <span>Future Tech Foundations • <EMAIL></span>
            </div>
            <div class="ftac-admin-panel__code-status">
              <span class="ftac-admin-panel__status ftac-admin-panel__status--active">Active</span>
            </div>
          </div>
          <div class="ftac-admin-panel__code-item">
            <div class="ftac-admin-panel__code-info">
              <code>FTAC-DEMO-MAST-5678</code>
              <span>Advanced Tech Mastery • <EMAIL></span>
            </div>
            <div class="ftac-admin-panel__code-status">
              <span class="ftac-admin-panel__status ftac-admin-panel__status--active">Active</span>
            </div>
          </div>
          <div class="ftac-admin-panel__code-item">
            <div class="ftac-admin-panel__code-info">
              <code>FTAC-TEST-PROD-1499</code>
              <span>Test Product • <EMAIL></span>
            </div>
            <div class="ftac-admin-panel__code-status">
              <span class="ftac-admin-panel__status ftac-admin-panel__status--unused">Unused</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Customer Access Tab -->
    <div class="ftac-admin-panel__content" id="customers-tab" style="display: none;">
      <div class="ftac-admin-panel__section">
        <h2 class="ftac-admin-panel__section-title">Customer Access Overview</h2>
        
        <div class="ftac-admin-panel__customer-search">
          <input type="email" placeholder="Search by customer email..." class="ftac-admin-panel__search-input">
          <button class="ftac-admin-panel__btn ftac-admin-panel__btn--secondary">Search</button>
        </div>

        <div class="ftac-admin-panel__customer-list">
          <div class="ftac-admin-panel__customer-item">
            <div class="ftac-admin-panel__customer-info">
              <h4><EMAIL></h4>
              <p>2 products unlocked • Last active: Today</p>
            </div>
            <div class="ftac-admin-panel__customer-products">
              <span class="ftac-admin-panel__product-badge">Future Tech Foundations</span>
              <span class="ftac-admin-panel__product-badge">Advanced Tech Mastery</span>
            </div>
          </div>
          <div class="ftac-admin-panel__customer-item">
            <div class="ftac-admin-panel__customer-info">
              <h4><EMAIL></h4>
              <p>1 product unlocked • Last active: 2 days ago</p>
            </div>
            <div class="ftac-admin-panel__customer-products">
              <span class="ftac-admin-panel__product-badge">Test Product</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Order Integration Tab -->
    <div class="ftac-admin-panel__content" id="orders-tab" style="display: none;">
      <div class="ftac-admin-panel__section">
        <h2 class="ftac-admin-panel__section-title">Order Integration Status</h2>
        
        <div class="ftac-admin-panel__integration-status">
          <div class="ftac-admin-panel__status-item">
            <h4>Etsy Webhook Integration</h4>
            <span class="ftac-admin-panel__status ftac-admin-panel__status--pending">Setup Required</span>
            <p>Configure webhook to automatically process new orders</p>
          </div>
          <div class="ftac-admin-panel__status-item">
            <h4>Email Automation</h4>
            <span class="ftac-admin-panel__status ftac-admin-panel__status--pending">Setup Required</span>
            <p>Automatically send access codes to customers after purchase</p>
          </div>
          <div class="ftac-admin-panel__status-item">
            <h4>Customer Sync</h4>
            <span class="ftac-admin-panel__status ftac-admin-panel__status--active">Active</span>
            <p>Customer accounts are synced with access codes</p>
          </div>
        </div>

        <div class="ftac-admin-panel__manual-order">
          <h3>Manual Order Processing</h3>
          <p>Use this form to manually process orders until automation is set up:</p>
          <form class="ftac-admin-panel__form">
            <div class="ftac-admin-panel__form-row">
              <div class="ftac-admin-panel__form-group">
                <label for="order-email">Customer Email:</label>
                <input type="email" id="order-email" class="ftac-admin-panel__input">
              </div>
              <div class="ftac-admin-panel__form-group">
                <label for="order-product">Product Purchased:</label>
                <select id="order-product" class="ftac-admin-panel__select">
                  <option value="future-tech-foundations">Future Tech Foundations</option>
                  <option value="advanced-tech-mastery">Advanced Tech Mastery</option>
                  <option value="tech-leadership-suite">Tech Leadership Suite</option>
                  <option value="test-product">Test Product</option>
                </select>
              </div>
            </div>
            <div class="ftac-admin-panel__form-row">
              <div class="ftac-admin-panel__form-group">
                <label for="order-etsy-id">Etsy Order ID:</label>
                <input type="text" id="order-etsy-id" class="ftac-admin-panel__input">
              </div>
              <div class="ftac-admin-panel__form-group">
                <label for="order-amount">Order Amount:</label>
                <input type="number" id="order-amount" class="ftac-admin-panel__input" step="0.01">
              </div>
            </div>
            <button type="submit" class="ftac-admin-panel__btn ftac-admin-panel__btn--primary">
              Process Order & Send Access Code
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>

{% else %}
  <!-- Not authorized -->
  <section class="ftac-admin-panel">
    <div class="ftac-admin-panel__container">
      <div style="text-align: center; padding: var(--ftac-space-12);">
        <h1 style="font-family: var(--ftac-font-primary); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-4);">
          Access Denied
        </h1>
        <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); opacity: 0.8;">
          This area is restricted to administrators only.
        </p>
      </div>
    </div>
  </section>
{% endif %}

<style>
  .ftac-admin-panel {
    background-color: var(--ftac-warm-cream);
    min-height: 100vh;
    padding: var(--ftac-space-8) 0;
  }

  .ftac-admin-panel__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
  }

  .ftac-admin-panel__header {
    text-align: center;
    margin-bottom: var(--ftac-space-8);
  }

  .ftac-admin-panel__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-4xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }

  .ftac-admin-panel__subtitle {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-charcoal);
    opacity: 0.8;
  }

  .ftac-admin-panel__tabs {
    display: flex;
    gap: var(--ftac-space-2);
    margin-bottom: var(--ftac-space-8);
    border-bottom: 2px solid rgba(var(--ftac-charcoal-rgb), 0.1);
  }

  .ftac-admin-panel__tab {
    background: none;
    border: none;
    padding: var(--ftac-space-4) var(--ftac-space-6);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
    opacity: 0.6;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
  }

  .ftac-admin-panel__tab:hover {
    opacity: 0.8;
  }

  .ftac-admin-panel__tab--active {
    opacity: 1;
    border-bottom-color: var(--ftac-academy-blue);
    color: var(--ftac-academy-blue);
  }

  .ftac-admin-panel__section {
    background: white;
    border-radius: var(--ftac-radius-xl);
    padding: var(--ftac-space-8);
    margin-bottom: var(--ftac-space-6);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  .ftac-admin-panel__section-title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-2xl);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-6);
  }

  .ftac-admin-panel__product-list {
    display: grid;
    gap: var(--ftac-space-6);
    margin-bottom: var(--ftac-space-8);
  }

  .ftac-admin-panel__product-card {
    border: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
    border-radius: var(--ftac-radius-lg);
    padding: var(--ftac-space-6);
    background: rgba(var(--ftac-warm-cream-rgb), 0.3);
  }

  .ftac-admin-panel__product-header {
    display: flex;
    align-items: center;
    gap: var(--ftac-space-4);
    margin-bottom: var(--ftac-space-4);
  }

  .ftac-admin-panel__product-header h3 {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    flex-grow: 1;
  }

  .ftac-admin-panel__product-type {
    background: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-1) var(--ftac-space-3);
    border-radius: var(--ftac-radius-full);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xs);
    font-weight: var(--ftac-font-medium);
    text-transform: uppercase;
  }

  .ftac-admin-panel__product-price {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-academy-blue);
  }

  .ftac-admin-panel__product-config {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--ftac-space-3);
    margin-bottom: var(--ftac-space-6);
  }

  .ftac-admin-panel__config-item label {
    display: flex;
    align-items: center;
    gap: var(--ftac-space-2);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    color: var(--ftac-charcoal);
    cursor: pointer;
  }

  .ftac-admin-panel__product-actions {
    display: flex;
    gap: var(--ftac-space-3);
  }

  .ftac-admin-panel__btn {
    padding: var(--ftac-space-3) var(--ftac-space-6);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-sm);
    font-weight: var(--ftac-font-medium);
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
  }

  .ftac-admin-panel__btn--primary {
    background: var(--ftac-academy-blue);
    color: white;
  }

  .ftac-admin-panel__btn--primary:hover {
    background: rgba(var(--ftac-academy-blue-rgb), 0.9);
  }

  .ftac-admin-panel__btn--secondary {
    background: rgba(var(--ftac-charcoal-rgb), 0.1);
    color: var(--ftac-charcoal);
  }

  .ftac-admin-panel__btn--secondary:hover {
    background: rgba(var(--ftac-charcoal-rgb), 0.15);
  }

  .ftac-admin-panel__form {
    display: grid;
    gap: var(--ftac-space-4);
  }

  .ftac-admin-panel__form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--ftac-space-4);
  }

  .ftac-admin-panel__form-group {
    display: flex;
    flex-direction: column;
    gap: var(--ftac-space-2);
  }

  .ftac-admin-panel__form-group label {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-sm);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
  }

  .ftac-admin-panel__input,
  .ftac-admin-panel__select {
    padding: var(--ftac-space-3);
    border: 1px solid rgba(var(--ftac-charcoal-rgb), 0.2);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
  }

  .ftac-admin-panel__input:focus,
  .ftac-admin-panel__select:focus {
    outline: none;
    border-color: var(--ftac-academy-blue);
    box-shadow: 0 0 0 3px rgba(var(--ftac-academy-blue-rgb), 0.1);
  }

  @media (max-width: 767px) {
    .ftac-admin-panel__tabs {
      flex-wrap: wrap;
    }

    .ftac-admin-panel__form-row {
      grid-template-columns: 1fr;
    }

    .ftac-admin-panel__product-actions {
      flex-direction: column;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Tab functionality
  const tabs = document.querySelectorAll('.ftac-admin-panel__tab');
  const contents = document.querySelectorAll('.ftac-admin-panel__content');

  tabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const targetTab = this.dataset.tab;

      // Remove active class from all tabs
      tabs.forEach(t => t.classList.remove('ftac-admin-panel__tab--active'));
      // Add active class to clicked tab
      this.classList.add('ftac-admin-panel__tab--active');

      // Hide all content
      contents.forEach(content => content.style.display = 'none');
      // Show target content
      const targetContent = document.getElementById(targetTab + '-tab');
      if (targetContent) {
        targetContent.style.display = 'block';
      }
    });
  });

  // Access code generation
  const accessCodeForm = document.querySelector('#access-codes-tab form');
  if (accessCodeForm) {
    accessCodeForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const product = document.getElementById('product-select').value;
      const email = document.getElementById('customer-email').value;
      const etsy_order = document.getElementById('etsy-order-id').value;

      if (!product || !email || !etsy_order) {
        alert('Please fill in all fields');
        return;
      }

      // Generate access code (demo implementation)
      const productCodes = {
        'future-tech-foundations': 'FOUN',
        'advanced-tech-mastery': 'MAST',
        'tech-leadership-suite': 'LEAD',
        'test-product': 'TEST'
      };

      const productCode = productCodes[product] || 'UNKN';
      const randomPart1 = Math.random().toString(36).substr(2, 4).toUpperCase();
      const randomPart2 = Math.random().toString(36).substr(2, 4).toUpperCase();
      const accessCode = `FTAC-${productCode}-${randomPart1}-${randomPart2}`;

      alert(`Access code generated: ${accessCode}\n\nThis would be automatically sent to ${email} and stored in the system.`);

      // Reset form
      this.reset();
    });
  }

  // Manual order processing
  const orderForm = document.querySelector('#orders-tab form');
  if (orderForm) {
    orderForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const email = document.getElementById('order-email').value;
      const product = document.getElementById('order-product').value;
      const etsy_id = document.getElementById('order-etsy-id').value;
      const amount = document.getElementById('order-amount').value;

      if (!email || !product || !etsy_id || !amount) {
        alert('Please fill in all fields');
        return;
      }

      // Process order (demo implementation)
      alert(`Order processed successfully!\n\nCustomer: ${email}\nProduct: ${product}\nEtsy Order: ${etsy_id}\nAmount: $${amount}\n\nAccess code has been generated and sent to the customer.`);

      // Reset form
      this.reset();
    });
  }

  // Customer search
  const searchInput = document.querySelector('.ftac-admin-panel__search-input');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      const customerItems = document.querySelectorAll('.ftac-admin-panel__customer-item');

      customerItems.forEach(item => {
        const email = item.querySelector('h4').textContent.toLowerCase();
        if (email.includes(searchTerm)) {
          item.style.display = 'block';
        } else {
          item.style.display = 'none';
        }
      });
    });
  }
});
</script>

{% schema %}
{
  "name": "FTAC Admin Panel",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Admin Panel Settings"
    },
    {
      "type": "text",
      "id": "admin_email",
      "label": "Admin Email",
      "default": "<EMAIL>",
      "info": "Only this email address can access the admin panel"
    }
  ],
  "presets": [
    {
      "name": "FTAC Admin Panel"
    }
  ]
}
{% endschema %}
