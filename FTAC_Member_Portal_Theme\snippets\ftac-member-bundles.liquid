{% comment %}
  FTAC Member Products Display

  This snippet displays the customer's accessible products based on
  product-specific access codes stored in customer metafields.

  Usage:
  {% render 'ftac-member-bundles', customer: customer %}
{% endcomment %}

{% assign accessible_products = '' %}
{% assign has_products = false %}

{% comment %} Get accessible products from customer metafields {% endcomment %}
{% render 'ftac-product-access', action: 'list_products', customer: customer %}
{% assign accessible_products = ftac_accessible_products %}

{% comment %} For demo purposes, also check for session storage access {% endcomment %}
{% assign demo_session_products = '' %}

{% if accessible_products != '' %}
  {% assign has_products = true %}
{% endif %}

{% comment %} Check if we have demo session products via JavaScript {% endcomment %}
<script>
  // Check session storage for demo access and add to page if found
  document.addEventListener('DOMContentLoaded', function() {
    const storedAccess = JSON.parse(sessionStorage.getItem('ftac_demo_access') || '{}');
    const hasSessionProducts = Object.keys(storedAccess).length > 0;

    // If we have session products but no server-side products, we need to show them
    if (hasSessionProducts) {
      const noProductsSection = document.querySelector('.ftac-member-dashboard__no-products');
      const bundlesContainer = document.querySelector('.ftac-member-dashboard__bundles');

      if (noProductsSection && bundlesContainer) {
        // Hide the no products section
        noProductsSection.style.display = 'none';

        // Add session products to the page
        Object.keys(storedAccess).forEach(productHandle => {
          const accessData = storedAccess[productHandle];
          addProductToPage(productHandle, accessData, bundlesContainer);
        });
      }
    }
  });

  function addProductToPage(productHandle, accessData, container) {
    const productInfo = getProductInfo(productHandle);
    const bundleConfig = getBundleConfig(productHandle);

    const productHTML = `
      <div class="ftac-member-dashboard__bundle" style="background: white; border-radius: var(--ftac-radius-xl); padding: var(--ftac-space-6); margin-bottom: var(--ftac-space-6); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);">
        <div class="ftac-member-dashboard__bundle-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--ftac-space-6);">
          <div>
            <h3 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-xl); font-weight: var(--ftac-font-semibold); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-2);">
              ${productInfo.name}
            </h3>
            <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); opacity: 0.8; margin-bottom: var(--ftac-space-2);">
              ${productInfo.description}
            </p>
            <div style="display: flex; align-items: center; gap: var(--ftac-space-3);">
              <span style="background: ${bundleConfig.typeColor}; color: white; padding: var(--ftac-space-1) var(--ftac-space-3); border-radius: var(--ftac-radius-full); font-family: var(--ftac-font-primary); font-size: var(--ftac-text-xs); font-weight: var(--ftac-font-medium); text-transform: uppercase;">
                ${bundleConfig.type}
              </span>
              <span style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-lg); font-weight: var(--ftac-font-semibold); color: var(--ftac-academy-blue);">
                ${productInfo.price}
              </span>
            </div>
          </div>
          <div style="text-align: right;">
            <p style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); color: var(--ftac-charcoal); opacity: 0.6; margin-bottom: var(--ftac-space-1);">
              Unlocked
            </p>
            <p style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); color: var(--ftac-sage-green); font-weight: var(--ftac-font-medium);">
              ${accessData.granted_date}
            </p>
          </div>
        </div>

        <div style="background-color: rgba(var(--ftac-warm-cream-rgb), 0.5); padding: var(--ftac-space-4); border-radius: var(--ftac-radius-lg); margin-bottom: var(--ftac-space-6);">
          <h4 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-base); font-weight: var(--ftac-font-semibold); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-3);">
            What's Included:
          </h4>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--ftac-space-3);">
            ${bundleConfig.includes.map(item => `
              <div style="display: flex; align-items: center; gap: var(--ftac-space-2);">
                <svg style="width: 16px; height: 16px; color: var(--ftac-academy-blue);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  ${item.icon}
                </svg>
                <span style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); color: var(--ftac-charcoal);">
                  ${item.name}
                </span>
                <span style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-xs); color: var(--ftac-charcoal); opacity: 0.6;">
                  ${item.size}
                </span>
              </div>
            `).join('')}
          </div>
        </div>

        <div style="display: flex; gap: var(--ftac-space-3);">
          ${bundleConfig.includes.some(item => item.type === 'app') ? `
            <a href="${bundleConfig.appUrl}" style="background: var(--ftac-academy-blue); color: white; padding: var(--ftac-space-3) var(--ftac-space-6); border-radius: var(--ftac-radius-lg); text-decoration: none; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium);">
              Access App
            </a>
          ` : ''}
          <button style="background: rgba(var(--ftac-charcoal-rgb), 0.1); color: var(--ftac-charcoal); padding: var(--ftac-space-3) var(--ftac-space-6); border: none; border-radius: var(--ftac-radius-lg); font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); cursor: pointer;">
            Download Resources
          </button>
        </div>
      </div>
    `;

    container.insertAdjacentHTML('beforeend', productHTML);
  }

  function getProductInfo(handle) {
    const products = {
      'future-tech-foundations': {
        name: 'Future Tech Foundations',
        description: 'Essential tools and knowledge to start your tech journey',
        price: '$14.99'
      },
      'advanced-tech-mastery': {
        name: 'Advanced Tech Mastery',
        description: 'Complete system for tech success and innovation',
        price: '$29.99'
      },
      'tech-leadership-suite': {
        name: 'Tech Leadership Suite',
        description: 'Everything you need to lead and innovate in tech',
        price: '$49.99'
      },
      'test-product': {
        name: 'Test Product',
        description: 'Demo product for testing the access system',
        price: '$14.99'
      }
    };
    return products[handle] || { name: 'Premium Content', description: 'Specialized learning resources', price: 'Premium' };
  }

  function getBundleConfig(handle) {
    const configs = {
      'future-tech-foundations': {
        type: 'Basic',
        typeColor: 'var(--ftac-academy-blue)',
        appUrl: '/apps/tech-foundations',
        includes: [
          { type: 'app', name: 'App Access', size: 'Available', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>' },
          { type: 'pdf', name: 'PDF Downloads', size: '3 files', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>' }
        ]
      },
      'advanced-tech-mastery': {
        type: 'Premium',
        typeColor: 'var(--ftac-sage-green)',
        appUrl: '/apps/tech-mastery',
        includes: [
          { type: 'app', name: 'App Access', size: 'Available', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>' },
          { type: 'pdf', name: 'PDF Downloads', size: '5 files', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>' },
          { type: 'pod', name: 'Physical Items', size: '2 items', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>' }
        ]
      },
      'tech-leadership-suite': {
        type: 'Enterprise',
        typeColor: 'var(--ftac-dusty-rose)',
        appUrl: '/apps/leadership-suite',
        includes: [
          { type: 'app', name: 'App Access', size: 'Available', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>' },
          { type: 'pdf', name: 'PDF Downloads', size: '8 files', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>' },
          { type: 'pod', name: 'Physical Items', size: '5 items', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>' }
        ]
      },
      'test-product': {
        type: 'Basic',
        typeColor: 'var(--ftac-academy-blue)',
        appUrl: '/apps/test-product',
        includes: [
          { type: 'app', name: 'App Access', size: 'Available', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>' }
        ]
      }
    };
    return configs[handle] || { type: 'Basic', typeColor: 'var(--ftac-academy-blue)', includes: [] };
  }
</script>

{% if has_products %}
  {% assign product_list = accessible_products | split: ',' %}

  {% for product_handle in product_list %}
    {% unless product_handle == blank %}
      {% comment %} Get access code for this product {% endcomment %}
      {% render 'ftac-product-access', action: 'get_access_code', customer: customer, product_handle: product_handle %}
      {% assign product_access_code = ftac_access_code %}

      {% comment %} Get bundle configuration {% endcomment %}
      {% render 'ftac-bundle-config', action: 'get_config', product_handle: product_handle %}
      {% render 'ftac-bundle-config', action: 'get_bundle_display', product_handle: product_handle %}

      {% comment %} Get product-specific information {% endcomment %}
      {% case product_handle %}
        {% when 'future-tech-foundations' %}
          {% assign product_name = 'Future Tech Foundations' %}
          {% assign product_description = 'Essential tools and knowledge to start your tech journey' %}
          {% assign product_price = '$14.99' %}

        {% when 'advanced-tech-mastery' %}
          {% assign product_name = 'Advanced Tech Mastery' %}
          {% assign product_description = 'Complete system for tech success and innovation' %}
          {% assign product_price = '$29.99' %}

        {% when 'tech-leadership-suite' %}
          {% assign product_name = 'Tech Leadership Suite' %}
          {% assign product_description = 'Everything you need to lead and innovate in tech' %}
          {% assign product_price = '$49.99' %}

        {% when 'test-product' %}
          {% assign product_name = 'Test Product' %}
          {% assign product_description = 'Demo product for testing the access system' %}
          {% assign product_price = '$14.99' %}

        {% else %}
          {% assign product_name = 'Premium Content' %}
          {% assign product_description = 'Specialized learning resources' %}
          {% assign product_price = 'Premium' %}
      {% endcase %}

      {% render 'ftac-bundle-display',
         bundle_id: product_handle,
         bundle_name: product_name,
         bundle_description: product_description,
         bundle_type: ftac_bundle_type,
         bundle_price: product_price,
         purchase_date: customer.created_at,
         resources: ftac_bundle_display,
         includes_app: ftac_includes_app,
         includes_pdf: ftac_includes_pdf,
         includes_pod: ftac_includes_pod,
         access_code: product_access_code
      %}
    {% endunless %}
  {% endfor %}

  <!-- Add More Content Option -->
  <div class="ftac-member-dashboard__add-more" style="background: rgba(var(--ftac-warm-cream-rgb), 0.3); border: 2px dashed rgba(var(--ftac-academy-blue-rgb), 0.3); border-radius: var(--ftac-radius-lg); padding: var(--ftac-space-6); text-align: center; margin-top: var(--ftac-space-6);">
    <svg style="width: 32px; height: 32px; color: var(--ftac-academy-blue); margin-bottom: var(--ftac-space-3);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    <h4 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-lg); font-weight: var(--ftac-font-medium); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-3);">
      Have Another Access Code?
    </h4>
    <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); opacity: 0.8; margin-bottom: var(--ftac-space-4);">
      Unlock additional content with more access codes from your purchases.
    </p>

    <button class="ftac-show-add-form" style="background: var(--ftac-academy-blue); color: white; padding: var(--ftac-space-3) var(--ftac-space-6); border: none; border-radius: var(--ftac-radius-lg); font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); cursor: pointer;">
      Add Access Code
    </button>

    <div class="ftac-add-form" style="display: none; margin-top: var(--ftac-space-4); max-width: 400px; margin-left: auto; margin-right: auto;">
      <form class="ftac-additional-access-form" style="display: flex; flex-direction: column; gap: var(--ftac-space-3);">
        <div style="position: relative;">
          <input
            type="text"
            class="additional-access-code"
            placeholder="FTAC-XXXX-YYYY-ZZZZ"
            style="width: 100%; padding: var(--ftac-space-3); border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.2); border-radius: var(--ftac-radius-lg); font-family: 'Courier New', monospace; font-size: var(--ftac-text-sm); text-align: center; letter-spacing: 1px; background: white;"
            maxlength="19"
          >
          <div class="ftac-additional-loading" style="display: none; position: absolute; right: var(--ftac-space-3); top: 50%; transform: translateY(-50%);">
            <div style="width: 16px; height: 16px; border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.3); border-top: 2px solid var(--ftac-academy-blue); border-radius: 50%; animation: spin 1s linear infinite;"></div>
          </div>
        </div>
        <div style="display: flex; gap: var(--ftac-space-2);">
          <button
            type="submit"
            style="flex: 1; background: var(--ftac-academy-blue); color: white; padding: var(--ftac-space-3) var(--ftac-space-4); border: none; border-radius: var(--ftac-radius-lg); font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); cursor: pointer; font-size: var(--ftac-text-sm);"
          >
            Unlock
          </button>
          <button
            type="button"
            class="ftac-cancel-add"
            style="background: rgba(var(--ftac-charcoal-rgb), 0.1); color: var(--ftac-charcoal); padding: var(--ftac-space-3) var(--ftac-space-4); border: none; border-radius: var(--ftac-radius-lg); font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); cursor: pointer; font-size: var(--ftac-text-sm);"
          >
            Cancel
          </button>
        </div>
      </form>

      <div class="ftac-additional-message" style="margin-top: var(--ftac-space-3); padding: var(--ftac-space-3); border-radius: var(--ftac-radius-lg); display: none;">
        <p style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); margin: 0;"></p>
      </div>
    </div>
  </div>

{% else %}
  <!-- No products unlocked yet -->
  <div class="ftac-member-dashboard__no-products">
    <div style="text-align: center; padding: var(--ftac-space-8); background-color: rgba(var(--ftac-warm-cream-rgb), 0.3); border-radius: var(--ftac-radius-lg);">
      <svg style="width: 48px; height: 48px; color: var(--ftac-academy-blue); margin-bottom: var(--ftac-space-4);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"></path>
      </svg>
      <h3 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-xl); font-weight: var(--ftac-font-semibold); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-4);">
        Ready to Unlock Your Content?
      </h3>
      <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); opacity: 0.8; margin-bottom: var(--ftac-space-6);">
        Enter the access code from your purchase to unlock your premium content and start learning.
      </p>

      <!-- Integrated Access Code Input -->
      <div style="max-width: 400px; margin: 0 auto var(--ftac-space-6) auto;">
        <form class="ftac-integrated-access-form" style="display: flex; flex-direction: column; gap: var(--ftac-space-3);">
          <div style="position: relative;">
            <input
              type="text"
              id="integrated-access-code"
              placeholder="FTAC-XXXX-YYYY-ZZZZ"
              style="width: 100%; padding: var(--ftac-space-4); border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.2); border-radius: var(--ftac-radius-lg); font-family: 'Courier New', monospace; font-size: var(--ftac-text-base); text-align: center; letter-spacing: 1px; background: white;"
              maxlength="19"
            >
            <div class="ftac-integrated-access-loading" style="display: none; position: absolute; right: var(--ftac-space-3); top: 50%; transform: translateY(-50%);">
              <div style="width: 20px; height: 20px; border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.3); border-top: 2px solid var(--ftac-academy-blue); border-radius: 50%; animation: spin 1s linear infinite;"></div>
            </div>
          </div>
          <button
            type="submit"
            style="background: var(--ftac-academy-blue); color: white; padding: var(--ftac-space-4) var(--ftac-space-6); border: none; border-radius: var(--ftac-radius-lg); font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); cursor: pointer; transition: all 0.2s ease;"
            onmouseover="this.style.background='rgba(var(--ftac-academy-blue-rgb), 0.9)'"
            onmouseout="this.style.background='var(--ftac-academy-blue)'"
          >
            Unlock Content
          </button>
        </form>

        <div class="ftac-integrated-access-message" style="margin-top: var(--ftac-space-4); padding: var(--ftac-space-3); border-radius: var(--ftac-radius-lg); display: none;">
          <p style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); margin: 0;"></p>
        </div>
      </div>

      <div style="display: flex; flex-direction: column; gap: var(--ftac-space-3); align-items: center;">
        <p style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); color: var(--ftac-charcoal); opacity: 0.6; margin: 0;">
          Don't have an access code yet?
        </p>
        <a href="/collections/all" style="background-color: rgba(var(--ftac-academy-blue-rgb), 0.1); color: var(--ftac-academy-blue); padding: var(--ftac-space-3) var(--ftac-space-6); border-radius: var(--ftac-radius-lg); text-decoration: none; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium);">
          Browse Shop
        </a>
        <a href="/pages/contact-support" style="color: var(--ftac-academy-blue); text-decoration: none; font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm);">
          Need help? Contact Sarah →
        </a>
      </div>
    </div>
  </div>
{% endif %}

<style>
@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

.ftac-integrated-access-form input:focus {
  outline: none;
  border-color: var(--ftac-academy-blue);
  box-shadow: 0 0 0 3px rgba(var(--ftac-academy-blue-rgb), 0.1);
}

.ftac-integrated-access-message.success {
  background-color: rgba(var(--ftac-sage-green-rgb), 0.1);
  border: 1px solid rgba(var(--ftac-sage-green-rgb), 0.3);
  color: var(--ftac-sage-green);
}

.ftac-integrated-access-message.error,
.ftac-additional-message.error {
  background-color: rgba(var(--ftac-dusty-rose-rgb), 0.1);
  border: 1px solid rgba(var(--ftac-dusty-rose-rgb), 0.3);
  color: var(--ftac-dusty-rose);
}

.ftac-additional-message.success {
  background-color: rgba(var(--ftac-sage-green-rgb), 0.1);
  border: 1px solid rgba(var(--ftac-sage-green-rgb), 0.3);
  color: var(--ftac-sage-green);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.querySelector('.ftac-integrated-access-form');
  const input = document.getElementById('integrated-access-code');
  const loading = document.querySelector('.ftac-integrated-access-loading');
  const message = document.querySelector('.ftac-integrated-access-message');

  if (!form || !input) return;

  // Format input as user types
  input.addEventListener('input', function() {
    let value = this.value.replace(/[^A-Z0-9]/g, '').toUpperCase();

    // Add dashes at appropriate positions
    if (value.length > 4) {
      value = value.substring(0, 4) + '-' + value.substring(4);
    }
    if (value.length > 9) {
      value = value.substring(0, 9) + '-' + value.substring(9);
    }
    if (value.length > 14) {
      value = value.substring(0, 14) + '-' + value.substring(14);
    }

    this.value = value;
  });

  // Handle additional access code form
  const showAddButton = document.querySelector('.ftac-show-add-form');
  const addForm = document.querySelector('.ftac-add-form');
  const additionalForm = document.querySelector('.ftac-additional-access-form');
  const additionalInput = document.querySelector('.additional-access-code');
  const additionalLoading = document.querySelector('.ftac-additional-loading');
  const additionalMessage = document.querySelector('.ftac-additional-message');
  const cancelButton = document.querySelector('.ftac-cancel-add');

  // Debug logging to help troubleshoot
  console.log('FTAC Debug - Add Access Code Elements:', {
    showAddButton: !!showAddButton,
    addForm: !!addForm,
    additionalForm: !!additionalForm,
    additionalInput: !!additionalInput,
    additionalLoading: !!additionalLoading,
    additionalMessage: !!additionalMessage,
    cancelButton: !!cancelButton
  });

  if (showAddButton && addForm) {
    showAddButton.addEventListener('click', function() {
      addForm.style.display = 'block';
      showAddButton.style.display = 'none';
      // Safely focus the input if it exists
      if (additionalInput) {
        additionalInput.focus();
      }
    });

    if (cancelButton) {
      cancelButton.addEventListener('click', function() {
        addForm.style.display = 'none';
        showAddButton.style.display = 'block';
        // Safely clear the input if it exists
        if (additionalInput) {
          additionalInput.value = '';
        }
        // Safely hide the message if it exists
        if (additionalMessage) {
          additionalMessage.style.display = 'none';
        }
      });
    }
  }

  // Format additional input
  if (additionalInput) {
    additionalInput.addEventListener('input', function() {
      let value = this.value.replace(/[^A-Z0-9]/g, '').toUpperCase();

      if (value.length > 4) {
        value = value.substring(0, 4) + '-' + value.substring(4);
      }
      if (value.length > 9) {
        value = value.substring(0, 9) + '-' + value.substring(9);
      }
      if (value.length > 14) {
        value = value.substring(0, 14) + '-' + value.substring(14);
      }

      this.value = value;
    });
  }

  // Handle additional form submission
  if (additionalForm && additionalInput) {
    additionalForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const accessCode = additionalInput.value.trim();

      // Validate format
      const codePattern = /^FTAC-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
      if (!codePattern.test(accessCode)) {
        showAdditionalMessage('Please enter a valid access code in the format FTAC-XXXX-YYYY-ZZZZ', 'error');
        return;
      }

      // Show loading
      if (additionalLoading) {
        additionalLoading.style.display = 'block';
      }
      if (additionalInput) {
        additionalInput.disabled = true;
      }

      // Simulate validation
      setTimeout(() => {
        const demoAccessCodes = {
          'FTAC-DEMO-TEST-1234': 'future-tech-foundations',
          'FTAC-DEMO-MAST-5678': 'advanced-tech-mastery',
          'FTAC-TEST-FOUN-9012': 'future-tech-foundations',
          'FTAC-TEST-LEAD-3456': 'tech-leadership-suite',
          'FTAC-TEST-PROD-1499': 'test-product'
        };

        if (additionalLoading) {
          additionalLoading.style.display = 'none';
        }
        if (additionalInput) {
          additionalInput.disabled = false;
        }

        if (demoAccessCodes[accessCode]) {
          const productName = getProductName(demoAccessCodes[accessCode]);
          showAdditionalMessage(`Success! ${productName} has been unlocked. Refreshing page...`, 'success');

          // Store the access in session storage
          let storedAccess = JSON.parse(sessionStorage.getItem('ftac_demo_access') || '{}');
          storedAccess[demoAccessCodes[accessCode]] = {
            access_code: accessCode,
            granted_date: new Date().toISOString().split('T')[0]
          };
          sessionStorage.setItem('ftac_demo_access', JSON.stringify(storedAccess));

          // Refresh page to show new content
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          showAdditionalMessage('Invalid access code. Please check your code and try again.', 'error');
        }
      }, 1500);
    });
  }

  function showAdditionalMessage(text, type) {
    if (additionalMessage) {
      additionalMessage.querySelector('p').textContent = text;
      additionalMessage.className = 'ftac-additional-message ' + type;
      additionalMessage.style.display = 'block';

      if (type === 'error') {
        setTimeout(() => {
          additionalMessage.style.display = 'none';
        }, 5000);
      }
    }
  }

  // Handle form submission
  if (form) {
    form.addEventListener('submit', function(e) {
    e.preventDefault();

    const accessCode = input.value.trim();

    // Validate format
    const codePattern = /^FTAC-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
    if (!codePattern.test(accessCode)) {
      showMessage('Please enter a valid access code in the format FTAC-XXXX-YYYY-ZZZZ', 'error');
      return;
    }

    // Show loading
    loading.style.display = 'block';
    input.disabled = true;

    // Simulate validation (in real implementation, this would be an AJAX call)
    setTimeout(() => {
      const demoAccessCodes = {
        'FTAC-DEMO-TEST-1234': 'future-tech-foundations',
        'FTAC-DEMO-MAST-5678': 'advanced-tech-mastery',
        'FTAC-TEST-FOUN-9012': 'future-tech-foundations',
        'FTAC-TEST-LEAD-3456': 'tech-leadership-suite',
        'FTAC-TEST-PROD-1499': 'test-product'
      };

      loading.style.display = 'none';
      input.disabled = false;

      if (demoAccessCodes[accessCode]) {
        const productName = getProductName(demoAccessCodes[accessCode]);
        showMessage(`Success! ${productName} has been unlocked. Refreshing page...`, 'success');

        // Store the access in session storage for demo purposes
        let storedAccess = JSON.parse(sessionStorage.getItem('ftac_demo_access') || '{}');
        storedAccess[demoAccessCodes[accessCode]] = {
          access_code: accessCode,
          granted_date: new Date().toISOString().split('T')[0]
        };
        sessionStorage.setItem('ftac_demo_access', JSON.stringify(storedAccess));

        // Refresh page to show new content
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        showMessage('Invalid access code. Please check your code and try again.', 'error');
      }
    }, 1500);
  });

  function showMessage(text, type) {
    message.querySelector('p').textContent = text;
    message.className = 'ftac-integrated-access-message ' + type;
    message.style.display = 'block';

    if (type === 'error') {
      setTimeout(() => {
        message.style.display = 'none';
      }, 5000);
    }
  }

  function getProductName(handle) {
    const names = {
      'future-tech-foundations': 'Future Tech Foundations',
      'advanced-tech-mastery': 'Advanced Tech Mastery',
      'tech-leadership-suite': 'Tech Leadership Suite',
      'test-product': 'Test Product'
    };
    return names[handle] || 'Premium Content';
  }
});
</script>
