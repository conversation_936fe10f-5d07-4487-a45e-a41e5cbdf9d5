{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-contact-hero {
    padding: var(--ftac-space-20) 0;
    background: linear-gradient(135deg, var(--ftac-warm-cream) 0%, rgba(var(--ftac-warm-cream-rgb), 0.3) 100%);
  }
  
  .ftac-contact-hero__container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
    text-align: center;
  }
  
  .ftac-contact-hero__headline {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-4xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    line-height: 1.1;
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-contact-hero__headline-highlight {
    color: var(--ftac-academy-blue);
  }
  
  .ftac-contact-hero__description {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-xl);
    color: var(--ftac-charcoal);
    line-height: 1.6;
    margin-bottom: var(--ftac-space-10);
    opacity: 0.9;
  }
  
  .ftac-contact-hero__promises {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ftac-space-6);
    margin-bottom: var(--ftac-space-10);
  }
  
  .ftac-contact-hero__promise {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ftac-space-3);
    padding: var(--ftac-space-4) var(--ftac-space-6);
    background-color: white;
    border-radius: var(--ftac-radius-lg);
    box-shadow: var(--ftac-shadow-sm);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
  }
  
  .ftac-contact-hero__promise-icon {
    width: 24px;
    height: 24px;
    color: var(--ftac-learning-green);
  }
  
  .ftac-contact-hero__contact-methods {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ftac-space-6);
  }
  
  .ftac-contact-hero__method {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ftac-space-4);
    padding: var(--ftac-space-6);
    background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green));
    color: white;
    border-radius: var(--ftac-radius-xl);
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: var(--ftac-shadow-lg);
  }
  
  .ftac-contact-hero__method:hover {
    transform: translateY(-2px);
    box-shadow: var(--ftac-shadow-xl);
  }
  
  .ftac-contact-hero__method-icon {
    width: 32px;
    height: 32px;
  }
  
  .ftac-contact-hero__method-content {
    text-align: left;
  }
  
  .ftac-contact-hero__method-title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    margin-bottom: var(--ftac-space-1);
  }
  
  .ftac-contact-hero__method-description {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    opacity: 0.9;
  }
  
  /* Desktop Layout */
  @media (min-width: 768px) {
    .ftac-contact-hero__promises {
      grid-template-columns: repeat(3, 1fr);
    }
    
    .ftac-contact-hero__contact-methods {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (min-width: 1024px) {
    .ftac-contact-hero__headline {
      font-size: var(--ftac-text-5xl);
    }
  }
</style>

<section class="ftac-contact-hero">
  <div class="ftac-contact-hero__container">
    <h1 class="ftac-contact-hero__headline">
      Get <span class="ftac-contact-hero__headline-highlight">Personal Support</span> from Sarah
    </h1>
    
    <p class="ftac-contact-hero__description">
      {{ section.settings.description | default: "Have a question? Need help with your learning journey? I'm here to support you every step of the way. Your success is my priority." }}
    </p>
    
    <div class="ftac-contact-hero__promises">
      <div class="ftac-contact-hero__promise">
        <svg class="ftac-contact-hero__promise-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>Response within 3 hours</span>
      </div>
      <div class="ftac-contact-hero__promise">
        <svg class="ftac-contact-hero__promise-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
        <span>Personal attention</span>
      </div>
      <div class="ftac-contact-hero__promise">
        <svg class="ftac-contact-hero__promise-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span>Solutions guaranteed</span>
      </div>
    </div>
    
    <div class="ftac-contact-hero__contact-methods">
      <a href="#contact-form" class="ftac-contact-hero__method">
        <svg class="ftac-contact-hero__method-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
        <div class="ftac-contact-hero__method-content">
          <div class="ftac-contact-hero__method-title">Send a Message</div>
          <div class="ftac-contact-hero__method-description">Fill out the form below for detailed help</div>
        </div>
      </a>
      
      <a href="mailto:{{ section.settings.email | default: '<EMAIL>' }}" class="ftac-contact-hero__method">
        <svg class="ftac-contact-hero__method-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
        </svg>
        <div class="ftac-contact-hero__method-content">
          <div class="ftac-contact-hero__method-title">Email Directly</div>
          <div class="ftac-contact-hero__method-description">{{ section.settings.email | default: '<EMAIL>' }}</div>
        </div>
      </a>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "FTAC Contact Hero",
  "settings": [
    {
      "type": "textarea",
      "id": "description",
      "label": "Description Text",
      "default": "Have a question? Need help with your learning journey? I'm here to support you every step of the way. Your success is my priority."
    },
    {
      "type": "text",
      "id": "email",
      "label": "Support Email",
      "default": "<EMAIL>"
    }
  ],
  "presets": [
    {
      "name": "FTAC Contact Hero"
    }
  ]
}
{% endschema %}
