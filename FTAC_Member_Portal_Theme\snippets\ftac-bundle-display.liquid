{% comment %}
  FTAC Bundle Display Component

  Displays an individual bundle with its resources and download options.

  Parameters:
  - bundle_id: Unique identifier for the bundle (product handle)
  - bundle_name: Display name of the bundle
  - bundle_description: Short description
  - bundle_type: Type of bundle (basic, premium, enterprise)
  - bundle_price: Price of the bundle
  - purchase_date: When the bundle was purchased
  - resources: Formatted display of what's included
  - includes_app: Whether bundle includes app access
  - includes_pdf: Whether bundle includes PDF downloads
  - includes_pod: Whether bundle includes physical items
  - access_code: The access code for this product (optional)
{% endcomment %}

{% assign bundle_id = bundle_id | default: 'unknown' %}
{% assign bundle_name = bundle_name | default: 'Learning Bundle' %}
{% assign bundle_description = bundle_description | default: 'Premium learning resources' %}
{% assign bundle_type = bundle_type | default: 'basic' %}
{% assign bundle_price = bundle_price | default: 'Premium' %}
{% assign purchase_date = purchase_date | default: 'Recently' %}
{% assign resources = resources | default: 'Resources,App Access' %}
{% assign includes_app = includes_app | default: 'false' %}
{% assign includes_pdf = includes_pdf | default: 'false' %}
{% assign includes_pod = includes_pod | default: 'false' %}
{% assign access_code = access_code | default: '' %}

<div class="ftac-member-dashboard__bundle" data-bundle-id="{{ bundle_id }}">
  <div class="ftac-member-dashboard__bundle-header">
    <div>
      <div style="display: flex; align-items: center; gap: var(--ftac-space-3); margin-bottom: var(--ftac-space-2);">
        <h3 class="ftac-member-dashboard__bundle-title">{{ bundle_name }}</h3>
        <span style="background-color: var(--ftac-academy-blue); color: white; padding: var(--ftac-space-1) var(--ftac-space-3); border-radius: var(--ftac-radius-full); font-family: var(--ftac-font-primary); font-size: var(--ftac-text-xs); font-weight: var(--ftac-font-medium); text-transform: uppercase;">
          {{ bundle_type }}
        </span>
        <span style="color: var(--ftac-academy-blue); font-family: var(--ftac-font-primary); font-size: var(--ftac-text-sm); font-weight: var(--ftac-font-semibold);">
          {{ bundle_price }}
        </span>
      </div>
      <p class="ftac-member-dashboard__bundle-date">
        Purchased: {{ purchase_date | date: "%B %d, %Y" }}
      </p>
    </div>
    <div class="ftac-member-dashboard__bundle-badge">Active</div>
  </div>
  
  <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); opacity: 0.8; margin-bottom: var(--ftac-space-4);">
    {{ bundle_description }}
  </p>

  {% if access_code != '' %}
    <div style="background-color: rgba(var(--ftac-academy-blue-rgb), 0.1); padding: var(--ftac-space-3); border-radius: var(--ftac-radius-lg); margin-bottom: var(--ftac-space-6);">
      <div style="display: flex; align-items: center; gap: var(--ftac-space-2); margin-bottom: var(--ftac-space-2);">
        <svg style="width: 16px; height: 16px; color: var(--ftac-academy-blue);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
        </svg>
        <span style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-sm); font-weight: var(--ftac-font-medium); color: var(--ftac-academy-blue);">
          Access Code
        </span>
      </div>
      <code style="font-family: 'Courier New', monospace; font-size: var(--ftac-text-sm); color: var(--ftac-charcoal); background-color: white; padding: var(--ftac-space-2); border-radius: var(--ftac-radius-md); letter-spacing: 1px;">
        {{ access_code }}
      </code>
    </div>
  {% endif %}

  <!-- Bundle Configuration Display -->
  <div style="background-color: rgba(var(--ftac-warm-cream-rgb), 0.5); padding: var(--ftac-space-4); border-radius: var(--ftac-radius-lg); margin-bottom: var(--ftac-space-6);">
    <h4 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-base); font-weight: var(--ftac-font-semibold); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-3);">
      What's Included:
    </h4>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--ftac-space-3);">

      {% if includes_app == 'true' %}
        <div class="ftac-member-dashboard__resource">
          <svg class="ftac-member-dashboard__resource-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
          </svg>
          <span class="ftac-member-dashboard__resource-name">App Access</span>
          <span class="ftac-member-dashboard__resource-size">Available</span>
        </div>
      {% endif %}

      {% if includes_pdf == 'true' %}
        {% comment %} Get PDF count for this product {% endcomment %}
        {% render 'ftac-bundle-config', action: 'get_pdfs', product_handle: bundle_id %}
        {% assign pdf_count = ftac_pdf_list | split: '|' | size %}
        <div class="ftac-member-dashboard__resource">
          <svg class="ftac-member-dashboard__resource-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <span class="ftac-member-dashboard__resource-name">PDF Downloads</span>
          <span class="ftac-member-dashboard__resource-size">{{ pdf_count }} files</span>
        </div>
      {% endif %}

      {% if includes_pod == 'true' %}
        {% comment %} Get P.O.D. count for this product {% endcomment %}
        {% render 'ftac-bundle-config', action: 'get_pod_items', product_handle: bundle_id %}
        {% assign pod_count = ftac_pod_list | split: '|' | size %}
        <div class="ftac-member-dashboard__resource">
          <svg class="ftac-member-dashboard__resource-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
          </svg>
          <span class="ftac-member-dashboard__resource-name">Physical Items</span>
          <span class="ftac-member-dashboard__resource-size">{{ pod_count }} items</span>
        </div>
      {% endif %}

    </div>

    {% comment %} Show formatted bundle display if available {% endcomment %}
    {% if resources != '' %}
      <div style="margin-top: var(--ftac-space-4); padding-top: var(--ftac-space-4); border-top: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);">
        <p style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); color: var(--ftac-charcoal); opacity: 0.8;">
          {{ resources }}
        </p>
      </div>
    {% endif %}
  </div>
  
  <div class="ftac-member-dashboard__bundle-actions">
    <a href="#" class="ftac-member-dashboard__action-button ftac-member-dashboard__action-button--primary" onclick="downloadBundle('{{ bundle_id }}')">
      <svg class="ftac-member-dashboard__action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      Download All
    </a>
    
    <a href="/pages/contact-support" class="ftac-member-dashboard__action-button ftac-member-dashboard__action-button--secondary">
      <svg class="ftac-member-dashboard__action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      Get Help
    </a>
  </div>
</div>

<script>
function downloadBundle(bundleId) {
  // Show loading state
  event.target.innerHTML = '<div style="width: 16px; height: 16px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 8px;"></div>Preparing Download...';
  event.target.style.pointerEvents = 'none';
  
  // TODO: Implement actual download logic
  // This would integrate with Shopify's file serving or custom download system
  
  // For now, simulate download preparation
  setTimeout(function() {
    // Reset button
    event.target.innerHTML = '<svg class="ftac-member-dashboard__action-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>Download All';
    event.target.style.pointerEvents = 'auto';
    
    // Show download instructions
    alert('Download functionality will be implemented with secure file serving. For now, please contact Sarah for your files.');
  }, 2000);
}
</script>

<style>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
