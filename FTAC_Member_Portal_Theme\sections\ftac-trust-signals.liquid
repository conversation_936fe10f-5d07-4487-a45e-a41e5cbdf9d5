{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}
{{ 'luxury-components.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  /* Trust Signals Section */
  .ftac-trust-signals {
    background: linear-gradient(135deg, var(--ftac-cream-base) 0%, #FFFEF7 50%, var(--ftac-cream-base) 100%);
    position: relative;
    overflow: hidden;
  }

  .ftac-trust-signals::before {
    content: '';
    position: absolute;
    top: 20%;
    right: 10%;
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, var(--ftac-sage-accent), #70E0A4);
    border-radius: 50%;
    opacity: 0.03;
    animation: ftacFloat 18s ease-in-out infinite;
  }

  @keyframes ftacFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(8deg); }
  }

  .ftac-trust-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 2;
  }

  .ftac-trust-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 2rem;
    border: 1px solid rgba(255, 200, 221, 0.2);
    box-shadow: 0 15px 35px rgba(255, 200, 221, 0.1);
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
  }

  .ftac-trust-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(255, 200, 221, 0.2);
    border-color: var(--ftac-blush-primary);
  }

  .ftac-trust-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(255, 200, 221, 0.05), transparent);
    transform: rotate(45deg);
    animation: shimmer 5s ease-in-out infinite;
  }

  @keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
  }

  .ftac-trust-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
    position: relative;
    z-index: 2;
  }

  .ftac-trust-icon.security { color: var(--ftac-sage-accent); }
  .ftac-trust-icon.guarantee { color: var(--ftac-blush-primary); }
  .ftac-trust-icon.support { color: var(--ftac-peach-glow); }
  .ftac-trust-icon.quality { color: var(--ftac-dusty-trust); }

  .ftac-trust-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
  }

  .ftac-trust-description {
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.8;
    line-height: 1.6;
    position: relative;
    z-index: 2;
  }

  .ftac-trust-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-family: 'Open Sans', sans-serif;
    font-size: 0.85rem;
    font-weight: 600;
    margin-top: 1rem;
    box-shadow: 0 4px 15px rgba(255, 200, 221, 0.3);
    position: relative;
    z-index: 2;
  }

  /* Testimonials Section */
  .ftac-testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
  }

  .ftac-testimonial-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 2rem;
    border: 1px solid rgba(255, 200, 221, 0.2);
    box-shadow: 0 15px 35px rgba(255, 200, 221, 0.1);
    transition: all 0.4s ease;
    position: relative;
  }

  .ftac-testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 200, 221, 0.2);
  }

  .ftac-testimonial-quote {
    font-family: 'Open Sans', sans-serif;
    font-size: 1.1rem;
    color: var(--ftac-charcoal-text);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-style: italic;
  }

  .ftac-testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .ftac-testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: 1.2rem;
  }

  .ftac-testimonial-info {
    flex: 1;
  }

  .ftac-testimonial-name {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 0.25rem;
  }

  .ftac-testimonial-role {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: var(--ftac-blush-primary);
    font-weight: 600;
  }

  .ftac-testimonial-stars {
    color: var(--ftac-peach-glow);
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  /* Security Badges */
  .ftac-security-badges {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-top: 3rem;
    flex-wrap: wrap;
  }

  .ftac-security-badge {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1rem 1.5rem;
    border: 1px solid rgba(255, 200, 221, 0.2);
    box-shadow: 0 8px 20px rgba(255, 200, 221, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
  }

  .ftac-security-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(255, 200, 221, 0.2);
  }

  .ftac-security-icon {
    font-size: 1.5rem;
    color: var(--ftac-sage-accent);
  }

  .ftac-security-text {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--ftac-charcoal-text);
  }

  /* Responsive Design */
  @media screen and (max-width: 768px) {
    .ftac-trust-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
    
    .ftac-testimonials-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
    
    .ftac-security-badges {
      gap: 1rem;
    }
    
    .ftac-trust-card,
    .ftac-testimonial-card {
      padding: 1.5rem;
    }
  }
{%- endstyle -%}

<div class="ftac-trust-signals section-{{ section.id }}-padding">
  <div class="page-width">
    {% if section.settings.heading != blank %}
      <div class="title-wrapper center">
        <h2 class="title title--primary">{{ section.settings.heading }}</h2>
        {% if section.settings.subheading != blank %}
          <p class="subtitle">{{ section.settings.subheading }}</p>
        {% endif %}
      </div>
    {% endif %}

    <div class="ftac-trust-grid">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'trust_signal' %}
            <div class="ftac-trust-card" {{ block.shopify_attributes }}>
              <span class="ftac-trust-icon {{ block.settings.icon_type }}">{{ block.settings.icon | default: "🛡️" }}</span>
              <h3 class="ftac-trust-title">{{ block.settings.title | default: "Secure & Safe" }}</h3>
              <p class="ftac-trust-description">{{ block.settings.description | default: "Your information is protected with industry-standard security." }}</p>
              {% if block.settings.badge_text != blank %}
                <span class="ftac-trust-badge">{{ block.settings.badge_text }}</span>
              {% endif %}
            </div>
        {% endcase %}
      {% endfor %}
    </div>

    {% if section.settings.show_testimonials %}
      <div class="ftac-testimonials-grid">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'testimonial' %}
              <div class="ftac-testimonial-card" {{ block.shopify_attributes }}>
                <div class="ftac-testimonial-stars">★★★★★</div>
                <p class="ftac-testimonial-quote">"{{ block.settings.quote | default: "Amazing experience! Highly recommend." }}"</p>
                <div class="ftac-testimonial-author">
                  <div class="ftac-testimonial-avatar">{{ block.settings.name | default: "A" | slice: 0 }}</div>
                  <div class="ftac-testimonial-info">
                    <div class="ftac-testimonial-name">{{ block.settings.name | default: "Anonymous" }}</div>
                    <div class="ftac-testimonial-role">{{ block.settings.role | default: "Verified Customer" }}</div>
                  </div>
                </div>
              </div>
          {% endcase %}
        {% endfor %}
      </div>
    {% endif %}

    {% if section.settings.show_security_badges %}
      <div class="ftac-security-badges">
        <div class="ftac-security-badge">
          <span class="ftac-security-icon">🔒</span>
          <span class="ftac-security-text">SSL Secured</span>
        </div>
        <div class="ftac-security-badge">
          <span class="ftac-security-icon">💳</span>
          <span class="ftac-security-text">Secure Payments</span>
        </div>
        <div class="ftac-security-badge">
          <span class="ftac-security-icon">🛡️</span>
          <span class="ftac-security-text">Privacy Protected</span>
        </div>
        <div class="ftac-security-badge">
          <span class="ftac-security-icon">✅</span>
          <span class="ftac-security-text">Verified Business</span>
        </div>
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Trust Signals",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Why Choose Us?",
      "label": "Heading"
    },
    {
      "type": "text",
      "id": "subheading",
      "default": "Join thousands of happy customers who trust us",
      "label": "Subheading"
    },
    {
      "type": "checkbox",
      "id": "show_testimonials",
      "default": true,
      "label": "Show testimonials"
    },
    {
      "type": "checkbox",
      "id": "show_security_badges",
      "default": true,
      "label": "Show security badges"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 60
    }
  ],
  "blocks": [
    {
      "type": "trust_signal",
      "name": "Trust Signal",
      "settings": [
        {
          "type": "text",
          "id": "icon",
          "default": "🛡️",
          "label": "Icon (emoji)"
        },
        {
          "type": "select",
          "id": "icon_type",
          "default": "security",
          "label": "Icon color",
          "options": [
            {"value": "security", "label": "Green (Security)"},
            {"value": "guarantee", "label": "Pink (Guarantee)"},
            {"value": "support", "label": "Peach (Support)"},
            {"value": "quality", "label": "Blue (Quality)"}
          ]
        },
        {
          "type": "text",
          "id": "title",
          "default": "Secure & Safe",
          "label": "Title"
        },
        {
          "type": "textarea",
          "id": "description",
          "default": "Your information is protected with industry-standard security.",
          "label": "Description"
        },
        {
          "type": "text",
          "id": "badge_text",
          "label": "Badge text (optional)"
        }
      ]
    },
    {
      "type": "testimonial",
      "name": "Testimonial",
      "settings": [
        {
          "type": "textarea",
          "id": "quote",
          "default": "Amazing experience! Highly recommend.",
          "label": "Quote"
        },
        {
          "type": "text",
          "id": "name",
          "default": "Sarah Johnson",
          "label": "Customer name"
        },
        {
          "type": "text",
          "id": "role",
          "default": "Verified Customer",
          "label": "Customer role/title"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Trust Signals",
      "blocks": [
        {
          "type": "trust_signal",
          "settings": {
            "icon": "🛡️",
            "icon_type": "security",
            "title": "100% Secure",
            "description": "Your payment information is encrypted and secure. We never store your credit card details.",
            "badge_text": "SSL Protected"
          }
        },
        {
          "type": "trust_signal",
          "settings": {
            "icon": "💖",
            "icon_type": "guarantee",
            "title": "30-Day Guarantee",
            "description": "Love it or get your money back. No questions asked, no hassle returns.",
            "badge_text": "Risk-Free"
          }
        },
        {
          "type": "trust_signal",
          "settings": {
            "icon": "🎯",
            "icon_type": "support",
            "title": "Expert Support",
            "description": "Get help when you need it. Our friendly team is here to support your success.",
            "badge_text": "24/7 Available"
          }
        },
        {
          "type": "testimonial",
          "settings": {
            "quote": "This completely transformed my creative business! The quality is outstanding and the support is incredible.",
            "name": "Emma Rodriguez",
            "role": "Creative Entrepreneur"
          }
        },
        {
          "type": "testimonial",
          "settings": {
            "quote": "I was skeptical at first, but this exceeded all my expectations. Worth every penny!",
            "name": "Michael Chen",
            "role": "Digital Artist"
          }
        }
      ]
    }
  ]
}
{% endschema %}
