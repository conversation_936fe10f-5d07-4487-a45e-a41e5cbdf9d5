.blog-articles {
  display: grid;
  grid-gap: 1rem;
  column-gap: var(--grid-mobile-horizontal-spacing);
  row-gap: var(--grid-mobile-vertical-spacing);
}

.blog-articles .card-wrapper {
  width: 100%;
}

@media screen and (min-width: 750px) {
  .blog-articles {
    grid-template-columns: 1fr 1fr;
    column-gap: var(--grid-desktop-horizontal-spacing);
    row-gap: var(--grid-desktop-vertical-spacing);
  }

  .blog-articles--collage > *:nth-child(3n + 1),
  .blog-articles--collage > *:nth-child(3n + 2):last-child {
    grid-column: span 2;
    text-align: center;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .card,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .card {
    text-align: center;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--small .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--small .ratio::before {
    padding-bottom: 22rem;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--medium .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--medium .ratio::before {
    padding-bottom: 44rem;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--large .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--large .ratio::before {
    padding-bottom: 66rem;
  }
}

@media screen and (min-width: 990px) {
  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--small .ratio .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--small .ratio .ratio::before {
    padding-bottom: 27.5rem;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--medium .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--medium .ratio::before {
    padding-bottom: 55rem;
  }

  .blog-articles--collage > *:nth-child(3n + 1) .article-card__image--large .ratio::before,
  .blog-articles--collage > *:nth-child(3n + 2):last-child .article-card__image--large .ratio::before {
    padding-bottom: 82.5rem;
  }
}
