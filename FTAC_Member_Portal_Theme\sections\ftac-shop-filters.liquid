{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

{% comment %} Check product count to determine if filters should be shown {% endcomment %}
{% assign total_products = collections.all.products.size %}
{% assign show_filters = false %}
{% if total_products >= 4 %}
  {% assign show_filters = true %}
{% endif %}

<style>
  .ftac-shop-filters {
    background-color: white;
    padding: var(--ftac-space-8) 0;
    border-bottom: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
  }
  
  .ftac-shop-filters__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
  }
  
  .ftac-shop-filters__content {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ftac-space-4);
    align-items: center;
  }
  
  .ftac-shop-filters__label {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
  }
  
  .ftac-shop-filters__buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--ftac-space-3);
  }
  
  .ftac-shop-filters__button {
    padding: var(--ftac-space-2) var(--ftac-space-4);
    border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.3);
    background-color: white;
    color: var(--ftac-academy-blue);
    border-radius: var(--ftac-radius-full);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    font-weight: var(--ftac-font-medium);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
  }
  
  .ftac-shop-filters__button:hover,
  .ftac-shop-filters__button.active {
    background-color: var(--ftac-academy-blue);
    color: white;
    border-color: var(--ftac-academy-blue);
  }
  
  .ftac-shop-filters__clear {
    margin-left: auto;
    background-color: transparent;
    color: var(--ftac-charcoal);
    border: none;
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s ease;
  }
  
  .ftac-shop-filters__clear:hover {
    opacity: 1;
    text-decoration: underline;
  }
  
  @media (max-width: 768px) {
    .ftac-shop-filters__content {
      flex-direction: column;
      align-items: stretch;
    }
    
    .ftac-shop-filters__clear {
      margin-left: 0;
      text-align: center;
    }
  }
</style>

{% if show_filters %}
<section class="ftac-shop-filters">
  <div class="ftac-shop-filters__container">
    <div class="ftac-shop-filters__content">
      <span class="ftac-shop-filters__label">Filter by:</span>

      <div class="ftac-shop-filters__buttons">
        <button class="ftac-shop-filters__button active" onclick="filterProducts('all')" data-filter="all">
          All Products
        </button>
        <button class="ftac-shop-filters__button" onclick="filterProducts('basic')" data-filter="basic">
          Basic Bundles
        </button>
        <button class="ftac-shop-filters__button" onclick="filterProducts('premium')" data-filter="premium">
          Premium Bundles
        </button>
        <button class="ftac-shop-filters__button" onclick="filterProducts('enterprise')" data-filter="enterprise">
          Enterprise Bundles
        </button>
        <button class="ftac-shop-filters__button" onclick="filterProducts('course')" data-filter="course">
          Individual Courses
        </button>
      </div>

      <button class="ftac-shop-filters__clear" onclick="clearFilters()">
        Clear All Filters
      </button>
    </div>
  </div>
</section>
{% else %}
<!-- Coming Soon Message when few products -->
<section class="ftac-shop-filters">
  <div class="ftac-shop-filters__container">
    <div style="text-align: center; padding: var(--ftac-space-8) 0;">
      <h3 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-xl); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-4);">
        More Products Coming Soon!
      </h3>
      <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); opacity: 0.8; margin-bottom: var(--ftac-space-6);">
        We're currently featuring our premium bundles. More individual courses and specialized content will be available soon.
      </p>
      <div style="display: flex; justify-content: center; gap: var(--ftac-space-4); flex-wrap: wrap;">
        <a href="/pages/contact-support" style="background: var(--ftac-academy-blue); color: white; padding: var(--ftac-space-3) var(--ftac-space-6); border-radius: var(--ftac-radius-lg); text-decoration: none; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium);">
          Request Custom Content
        </a>
        <a href="https://www.etsy.com/shop/FutureTechAcademy" target="_blank" rel="noopener" style="background: rgba(var(--ftac-academy-blue-rgb), 0.1); color: var(--ftac-academy-blue); padding: var(--ftac-space-3) var(--ftac-space-6); border-radius: var(--ftac-radius-lg); text-decoration: none; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium);">
          Browse Etsy Store
        </a>
      </div>
    </div>
  </div>
</section>
{% endif %}

<script>
function filterProducts(category) {
  // Update active button
  document.querySelectorAll('.ftac-shop-filters__button').forEach(btn => {
    btn.classList.remove('active');
  });
  document.querySelector(`[data-filter="${category}"]`).classList.add('active');
  
  // Filter products
  const cards = document.querySelectorAll('.ftac-shop-products__card');
  let visibleCount = 0;
  
  cards.forEach(card => {
    const title = card.dataset.productTitle;
    let shouldShow = false;
    
    if (category === 'all') {
      shouldShow = true;
    } else if (category === 'basic' && (title.includes('basic') || title.includes('starter'))) {
      shouldShow = true;
    } else if (category === 'premium' && title.includes('premium')) {
      shouldShow = true;
    } else if (category === 'enterprise' && title.includes('enterprise')) {
      shouldShow = true;
    } else if (category === 'course' && (title.includes('course') || title.includes('class'))) {
      shouldShow = true;
    }
    
    if (shouldShow) {
      card.style.display = 'block';
      visibleCount++;
    } else {
      card.style.display = 'none';
    }
  });
  
  // Update count
  const countElement = document.getElementById('product-count');
  if (countElement) {
    countElement.textContent = visibleCount;
  }
}

function clearFilters() {
  filterProducts('all');
}
</script>

{% schema %}
{
  "name": "FTAC Shop Filters",
  "settings": [
    {
      "type": "header",
      "content": "Filter Settings"
    },
    {
      "type": "paragraph",
      "content": "Simple category filters for products. Filters are based on product titles containing keywords."
    }
  ],
  "presets": [
    {
      "name": "FTAC Shop Filters"
    }
  ]
}
{% endschema %}
