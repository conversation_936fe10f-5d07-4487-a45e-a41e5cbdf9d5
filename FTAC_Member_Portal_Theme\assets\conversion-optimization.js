/**
 * FTAC Conversion Optimization Scripts
 * Implements dynamic pricing, countdown timers, user behavior tracking, and psychological triggers
 */

class FTACConversionOptimizer {
  constructor() {
    this.init();
    this.setupEventListeners();
    this.startBehaviorTracking();
  }

  init() {
    console.log('🎯 FTAC Conversion Optimizer initialized');
    this.initDynamicPricing();
    this.initCountdownTimers();
    this.initExitIntentPopup();
    this.initScrollDepthTracking();
    this.initSocialProofNotifications();
  }

  // Dynamic Pricing System
  initDynamicPricing() {
    const priceElements = document.querySelectorAll('[data-dynamic-price]');
    
    priceElements.forEach(element => {
      const basePrice = parseFloat(element.dataset.basePrice);
      const discountPercent = this.calculateDynamicDiscount();
      const finalPrice = basePrice * (1 - discountPercent / 100);
      
      // Update price display
      element.textContent = this.formatPrice(finalPrice);
      
      // Add discount badge if applicable
      if (discountPercent > 0) {
        this.addDiscountBadge(element, discountPercent);
      }
    });
  }

  calculateDynamicDiscount() {
    const hour = new Date().getHours();
    const dayOfWeek = new Date().getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    
    // Time-based pricing
    if (hour >= 22 || hour <= 6) return 15; // Night owl discount
    if (hour >= 9 && hour <= 11) return 10; // Morning bird discount
    if (isWeekend) return 12; // Weekend special
    
    return 0;
  }

  formatPrice(price) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  addDiscountBadge(element, discount) {
    const badge = document.createElement('span');
    badge.className = 'ftac-dynamic-discount-badge';
    badge.textContent = `${discount}% OFF`;
    badge.style.cssText = `
      background: linear-gradient(135deg, var(--ftac-sage-accent), #70E0A4);
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 15px;
      font-size: 0.75rem;
      font-weight: 600;
      margin-left: 0.5rem;
      animation: pulse 2s infinite;
    `;
    element.parentNode.insertBefore(badge, element.nextSibling);
  }

  // Countdown Timer System
  initCountdownTimers() {
    const timers = document.querySelectorAll('[data-countdown]');
    
    timers.forEach(timer => {
      const endTime = new Date(timer.dataset.countdown).getTime();
      this.updateCountdown(timer, endTime);
      
      // Update every second
      setInterval(() => {
        this.updateCountdown(timer, endTime);
      }, 1000);
    });
  }

  updateCountdown(element, endTime) {
    const now = new Date().getTime();
    const distance = endTime - now;
    
    if (distance > 0) {
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);
      
      element.innerHTML = `
        <span class="countdown-item">
          <span class="countdown-number">${days.toString().padStart(2, '0')}</span>
          <span class="countdown-label">Days</span>
        </span>
        <span class="countdown-item">
          <span class="countdown-number">${hours.toString().padStart(2, '0')}</span>
          <span class="countdown-label">Hours</span>
        </span>
        <span class="countdown-item">
          <span class="countdown-number">${minutes.toString().padStart(2, '0')}</span>
          <span class="countdown-label">Minutes</span>
        </span>
        <span class="countdown-item">
          <span class="countdown-number">${seconds.toString().padStart(2, '0')}</span>
          <span class="countdown-label">Seconds</span>
        </span>
      `;
    } else {
      element.innerHTML = '<span class="countdown-expired">Offer Expired</span>';
    }
  }

  // Exit Intent Popup
  initExitIntentPopup() {
    let hasShown = false;
    
    document.addEventListener('mouseleave', (e) => {
      if (e.clientY <= 0 && !hasShown) {
        this.showExitIntentPopup();
        hasShown = true;
      }
    });
  }

  showExitIntentPopup() {
    const popup = document.createElement('div');
    popup.className = 'ftac-exit-intent-popup';
    popup.innerHTML = `
      <div class="ftac-popup-overlay">
        <div class="ftac-popup-content">
          <button class="ftac-popup-close">&times;</button>
          <h3>Wait! Don't Miss Out! 🎨</h3>
          <p>Get 20% off your first purchase with code <strong>WELCOME20</strong></p>
          <div class="ftac-popup-timer" data-countdown="${new Date(Date.now() + 10 * 60 * 1000).toISOString()}"></div>
          <button class="ftac-popup-cta">Claim My Discount</button>
        </div>
      </div>
    `;
    
    popup.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10000;
      animation: fadeIn 0.3s ease;
    `;
    
    document.body.appendChild(popup);
    
    // Close popup functionality
    popup.querySelector('.ftac-popup-close').addEventListener('click', () => {
      popup.remove();
    });
    
    popup.querySelector('.ftac-popup-overlay').addEventListener('click', (e) => {
      if (e.target === e.currentTarget) {
        popup.remove();
      }
    });
    
    // Initialize countdown for popup
    const popupTimer = popup.querySelector('[data-countdown]');
    if (popupTimer) {
      const endTime = new Date(popupTimer.dataset.countdown).getTime();
      const updatePopupTimer = () => {
        this.updateCountdown(popupTimer, endTime);
      };
      updatePopupTimer();
      setInterval(updatePopupTimer, 1000);
    }
  }

  // Scroll Depth Tracking
  initScrollDepthTracking() {
    const milestones = [25, 50, 75, 90, 100];
    const reached = new Set();
    
    const trackScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      );
      
      milestones.forEach(milestone => {
        if (scrollPercent >= milestone && !reached.has(milestone)) {
          reached.add(milestone);
          this.trackEvent('scroll_depth', { depth: milestone });
          
          // Trigger conversion actions at key milestones
          if (milestone === 50) {
            this.showScrollMilestoneOffer();
          }
        }
      });
    };
    
    window.addEventListener('scroll', this.throttle(trackScroll, 100));
  }

  showScrollMilestoneOffer() {
    // Show a subtle offer when user reaches 50% scroll
    const offer = document.createElement('div');
    offer.className = 'ftac-scroll-offer';
    offer.innerHTML = `
      <div class="ftac-scroll-offer-content">
        <span class="ftac-scroll-offer-text">🎯 Still browsing? Get 15% off with code BROWSE15</span>
        <button class="ftac-scroll-offer-close">&times;</button>
      </div>
    `;
    
    offer.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
      color: white;
      padding: 1rem;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(255, 200, 221, 0.3);
      z-index: 1000;
      animation: slideInRight 0.5s ease;
      max-width: 300px;
    `;
    
    document.body.appendChild(offer);
    
    // Auto-remove after 8 seconds
    setTimeout(() => {
      if (offer.parentNode) {
        offer.remove();
      }
    }, 8000);
    
    // Close button
    offer.querySelector('.ftac-scroll-offer-close').addEventListener('click', () => {
      offer.remove();
    });
  }

  // Social Proof Notifications
  initSocialProofNotifications() {
    const notifications = [
      { name: 'Sarah from California', action: 'purchased the Creative Bundle', time: '2 minutes ago' },
      { name: 'Mike from New York', action: 'joined the membership', time: '5 minutes ago' },
      { name: 'Emma from London', action: 'downloaded the starter pack', time: '8 minutes ago' },
      { name: 'Alex from Toronto', action: 'upgraded to premium', time: '12 minutes ago' },
      { name: 'Lisa from Sydney', action: 'completed a course', time: '15 minutes ago' }
    ];
    
    let currentIndex = 0;
    
    const showNotification = () => {
      const notification = notifications[currentIndex];
      const element = this.createSocialProofNotification(notification);
      
      document.body.appendChild(element);
      
      // Show animation
      setTimeout(() => {
        element.classList.add('show');
      }, 100);
      
      // Hide after 4 seconds
      setTimeout(() => {
        element.classList.remove('show');
        setTimeout(() => {
          if (element.parentNode) {
            element.remove();
          }
        }, 500);
      }, 4000);
      
      currentIndex = (currentIndex + 1) % notifications.length;
    };
    
    // Show first notification after 5 seconds
    setTimeout(showNotification, 5000);
    
    // Show subsequent notifications every 12 seconds
    setInterval(showNotification, 12000);
  }

  createSocialProofNotification(data) {
    const notification = document.createElement('div');
    notification.className = 'ftac-social-notification';
    notification.innerHTML = `
      <div class="ftac-notification-avatar">${data.name.charAt(0)}</div>
      <div class="ftac-notification-content">
        <div class="ftac-notification-name">${data.name}</div>
        <div class="ftac-notification-action">${data.action}</div>
        <div class="ftac-notification-time">${data.time}</div>
      </div>
    `;
    
    notification.style.cssText = `
      position: fixed;
      bottom: 20px;
      left: 20px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(15px);
      border-radius: 15px;
      padding: 1rem;
      border: 1px solid rgba(255, 200, 221, 0.3);
      box-shadow: 0 10px 30px rgba(255, 200, 221, 0.3);
      z-index: 1000;
      transform: translateX(-120%);
      transition: transform 0.5s ease;
      max-width: 300px;
      display: flex;
      align-items: center;
      gap: 1rem;
    `;
    
    return notification;
  }

  // Event Tracking
  trackEvent(eventName, properties = {}) {
    // Track to analytics (Google Analytics, Facebook Pixel, etc.)
    if (typeof gtag !== 'undefined') {
      gtag('event', eventName, properties);
    }
    
    if (typeof fbq !== 'undefined') {
      fbq('track', eventName, properties);
    }
    
    console.log('📊 Event tracked:', eventName, properties);
  }

  // User Behavior Tracking
  startBehaviorTracking() {
    // Track time on page
    const startTime = Date.now();
    
    window.addEventListener('beforeunload', () => {
      const timeOnPage = Math.round((Date.now() - startTime) / 1000);
      this.trackEvent('time_on_page', { seconds: timeOnPage });
    });
    
    // Track button clicks
    document.addEventListener('click', (e) => {
      if (e.target.matches('.btn, .button, [data-track-click]')) {
        this.trackEvent('button_click', {
          button_text: e.target.textContent.trim(),
          button_class: e.target.className
        });
      }
    });
    
    // Track form interactions
    document.addEventListener('focus', (e) => {
      if (e.target.matches('input, textarea, select')) {
        this.trackEvent('form_interaction', {
          field_type: e.target.type || e.target.tagName.toLowerCase(),
          field_name: e.target.name || e.target.id
        });
      }
    }, true);
  }

  // Event Listeners
  setupEventListeners() {
    // Add to cart tracking
    document.addEventListener('click', (e) => {
      if (e.target.matches('[data-add-to-cart], .btn[name="add"]')) {
        this.trackEvent('add_to_cart', {
          product_id: e.target.dataset.productId,
          variant_id: e.target.dataset.variantId
        });
      }
    });
    
    // Newsletter signup tracking
    document.addEventListener('submit', (e) => {
      if (e.target.matches('[data-newsletter-form]')) {
        this.trackEvent('newsletter_signup');
      }
    });
  }

  // Utility Functions
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new FTACConversionOptimizer();
});

// Add CSS for animations and components
const style = document.createElement('style');
style.textContent = `
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
  }
  
  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }
  
  .ftac-social-notification.show {
    transform: translateX(0) !important;
  }
  
  .ftac-popup-overlay {
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
  
  .ftac-popup-content {
    background: white;
    border-radius: 25px;
    padding: 2rem;
    max-width: 400px;
    text-align: center;
    position: relative;
  }
  
  .ftac-popup-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
  }
  
  .ftac-popup-cta {
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    border: none;
    border-radius: 25px;
    padding: 1rem 2rem;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1rem;
  }
`;
document.head.appendChild(style);
