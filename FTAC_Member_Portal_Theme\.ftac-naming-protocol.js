/**
 * FTAC Shopify Theme Naming Protocol Enforcement
 * Validates section and template naming compliance
 * 
 * RULES:
 * - Max 32 characters (filename without extension)
 * - Only lowercase letters, numbers, hyphens (a-z, 0-9, -)
 * - No spaces, underscores, camelCase, special chars
 * - Filename = liquid reference = schema name (exact match)
 */

class FTACNamingValidator {
  constructor() {
    this.MAX_LENGTH = 32;
    this.VALID_PATTERN = /^[a-z0-9-]+$/;
    this.violations = [];
  }

  /**
   * Validate a single filename
   * @param {string} filename - The filename to validate (without extension)
   * @returns {object} Validation result
   */
  validateFilename(filename) {
    const result = {
      filename,
      isValid: true,
      violations: []
    };

    // Check length
    if (filename.length > this.MAX_LENGTH) {
      result.isValid = false;
      result.violations.push(`Exceeds ${this.MAX_LENGTH} character limit (${filename.length} chars)`);
    }

    // Check character pattern
    if (!this.VALID_PATTERN.test(filename)) {
      result.isValid = false;
      result.violations.push('Contains invalid characters (only a-z, 0-9, - allowed)');
    }

    // Check for common mistakes
    if (filename.includes('_')) {
      result.isValid = false;
      result.violations.push('Contains underscores (use hyphens instead)');
    }

    if (filename.includes(' ')) {
      result.isValid = false;
      result.violations.push('Contains spaces (use hyphens instead)');
    }

    if (/[A-Z]/.test(filename)) {
      result.isValid = false;
      result.violations.push('Contains uppercase letters (use lowercase only)');
    }

    return result;
  }

  /**
   * Validate multiple filenames
   * @param {string[]} filenames - Array of filenames to validate
   * @returns {object} Validation summary
   */
  validateBatch(filenames) {
    const results = filenames.map(filename => this.validateFilename(filename));
    const violations = results.filter(result => !result.isValid);
    
    return {
      total: filenames.length,
      valid: results.length - violations.length,
      violations: violations.length,
      details: violations
    };
  }

  /**
   * Generate compliant filename suggestion
   * @param {string} filename - Original filename
   * @returns {string} Suggested compliant filename
   */
  suggestCompliantName(filename) {
    let suggestion = filename
      .toLowerCase()                    // Convert to lowercase
      .replace(/[^a-z0-9-]/g, '-')     // Replace invalid chars with hyphens
      .replace(/-+/g, '-')             // Remove duplicate hyphens
      .replace(/^-|-$/g, '');          // Remove leading/trailing hyphens

    // Truncate if too long
    if (suggestion.length > this.MAX_LENGTH) {
      suggestion = suggestion.substring(0, this.MAX_LENGTH).replace(/-$/, '');
    }

    return suggestion;
  }

  /**
   * Pre-commit validation hook
   * @param {string[]} filenames - Files being committed
   * @returns {boolean} Whether commit should proceed
   */
  preCommitValidation(filenames) {
    const liquidFiles = filenames.filter(f => f.endsWith('.liquid') || f.endsWith('.json'));
    const sectionFiles = liquidFiles.filter(f => f.includes('/sections/') || f.includes('/templates/'));
    
    if (sectionFiles.length === 0) return true;

    const filenamesToCheck = sectionFiles.map(f => {
      const basename = f.split('/').pop();
      return basename.replace(/\.(liquid|json)$/, '');
    });

    const validation = this.validateBatch(filenamesToCheck);
    
    if (validation.violations > 0) {
      console.error('🚨 FTAC NAMING PROTOCOL VIOLATION DETECTED:');
      validation.details.forEach(violation => {
        console.error(`❌ ${violation.filename}: ${violation.violations.join(', ')}`);
        console.error(`✅ Suggested: ${this.suggestCompliantName(violation.filename)}`);
      });
      console.error('\n📋 PROTOCOL: Max 32 chars, lowercase + hyphens only');
      return false;
    }

    console.log('✅ All filenames comply with FTAC naming protocol');
    return true;
  }
}

// Export for use in build scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = FTACNamingValidator;
}

// Browser/global usage
if (typeof window !== 'undefined') {
  window.FTACNamingValidator = FTACNamingValidator;
}

// Example usage and current theme validation
const validator = new FTACNamingValidator();

// Current FTAC theme files (sample)
const currentFiles = [
  'ftac-hero',
  'ftac-member-dashboard', 
  'ftac-luxury-pricing',
  'main-collection-product-grid',
  'cart-notification-product',
  'ftac-urgency-scarcity'
];

console.log('🔍 FTAC Theme Naming Compliance Check:');
const validation = validator.validateBatch(currentFiles);
console.log(`✅ ${validation.valid}/${validation.total} files compliant`);

if (validation.violations > 0) {
  console.log('❌ Violations found:', validation.details);
} else {
  console.log('🎉 All files comply with FTAC naming protocol!');
}
