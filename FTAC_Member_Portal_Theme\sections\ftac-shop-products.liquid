{{ 'base.css' | asset_url | stylesheet_tag }}
{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-shop-products {
    padding: var(--ftac-space-16) 0;
    background-color: #fafafa;
  }
  
  .ftac-shop-products__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
  }
  
  .ftac-shop-products__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--ftac-space-8);
    flex-wrap: wrap;
    gap: var(--ftac-space-4);
  }
  
  .ftac-shop-products__count {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-charcoal);
  }
  
  .ftac-shop-products__sort {
    display: flex;
    align-items: center;
    gap: var(--ftac-space-3);
  }
  
  .ftac-shop-products__sort-label {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
  }
  
  .ftac-shop-products__sort-select {
    padding: var(--ftac-space-2) var(--ftac-space-4);
    border: 2px solid rgba(var(--ftac-charcoal-rgb), 0.2);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-secondary);
    background-color: white;
    color: var(--ftac-charcoal);
  }
  
  .ftac-shop-products__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--ftac-space-8);
    margin-bottom: var(--ftac-space-12);
  }
  
  .ftac-shop-products__card {
    background-color: white;
    border-radius: var(--ftac-radius-xl);
    overflow: hidden;
    box-shadow: var(--ftac-shadow-lg);
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }
  
  .ftac-shop-products__card:hover {
    transform: translateY(-4px);
    box-shadow: var(--ftac-shadow-xl);
    border-color: var(--ftac-academy-blue);
  }
  
  .ftac-shop-products__image {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--ftac-text-4xl);
  }
  
  .ftac-shop-products__content {
    padding: var(--ftac-space-6);
  }
  
  .ftac-shop-products__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-3);
    line-height: 1.3;
  }
  
  .ftac-shop-products__description {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
    opacity: 0.8;
    margin-bottom: var(--ftac-space-4);
    line-height: 1.5;
  }
  
  .ftac-shop-products__price {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-2xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-academy-blue);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-shop-products__button {
    width: 100%;
    background-color: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-4);
    border: none;
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-semibold);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: block;
    text-align: center;
  }
  
  .ftac-shop-products__button:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
  }
  
  .ftac-shop-products__empty {
    text-align: center;
    padding: var(--ftac-space-16);
    color: var(--ftac-charcoal);
  }
  
  .ftac-shop-products__empty-title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-2xl);
    font-weight: var(--ftac-font-semibold);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-shop-products__empty-text {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    opacity: 0.7;
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-shop-products__empty-button {
    background-color: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-4) var(--ftac-space-8);
    border: none;
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-semibold);
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s ease;
  }
  
  .ftac-shop-products__empty-button:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
  }
  
  @media (max-width: 768px) {
    .ftac-shop-products__header {
      flex-direction: column;
      align-items: stretch;
    }
    
    .ftac-shop-products__grid {
      grid-template-columns: 1fr;
    }
  }
</style>

<section class="ftac-shop-products">
  <div class="ftac-shop-products__container">
    <div class="ftac-shop-products__header">
      <div class="ftac-shop-products__count">
        <span id="product-count">{{ collections.all.products_count }}</span> Products Available
      </div>
      
      {% if section.settings.enable_sorting %}
      <div class="ftac-shop-products__sort">
        <label class="ftac-shop-products__sort-label" for="sort-select">Sort by:</label>
        <select class="ftac-shop-products__sort-select" id="sort-select" onchange="sortProducts()">
          <option value="title-ascending">Name A-Z</option>
          <option value="title-descending">Name Z-A</option>
          <option value="price-ascending">Price Low to High</option>
          <option value="price-descending">Price High to Low</option>
          <option value="created-descending">Newest First</option>
        </select>
      </div>
      {% endif %}
    </div>
    
    <div class="ftac-shop-products__grid" id="products-grid">
      {% assign products = collections.all.products %}
      {% if products.size > 0 %}
        {% for product in products limit: section.settings.products_per_page %}
          <div class="ftac-shop-products__card" data-product-title="{{ product.title | downcase }}" data-product-price="{{ product.price }}">
            <div class="ftac-shop-products__image">
              {% if product.featured_image %}
                <img src="{{ product.featured_image | img_url: '400x200' }}" alt="{{ product.title }}" style="width: 100%; height: 100%; object-fit: cover;">
              {% else %}
                📚
              {% endif %}
            </div>
            
            <div class="ftac-shop-products__content">
              <h3 class="ftac-shop-products__title">{{ product.title }}</h3>
              <p class="ftac-shop-products__description">
                {{ product.description | strip_html | truncate: 120 }}
              </p>
              <div class="ftac-shop-products__price">
                {{ product.price | money }}
              </div>
              <a href="{{ product.url }}" class="ftac-shop-products__button">
                View Details
              </a>
            </div>
          </div>
        {% endfor %}
      {% else %}
        <div class="ftac-shop-products__empty">
          <h3 class="ftac-shop-products__empty-title">No Products Yet</h3>
          <p class="ftac-shop-products__empty-text">
            We're preparing amazing learning bundles for you. Check back soon!
          </p>
          <a href="/pages/contact-support" class="ftac-shop-products__empty-button">
            Get Notified When Available
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</section>

<script>
// Search functionality
document.addEventListener('shopSearch', function(e) {
  const query = e.detail.query.toLowerCase();
  const cards = document.querySelectorAll('.ftac-shop-products__card');
  let visibleCount = 0;
  
  cards.forEach(card => {
    const title = card.dataset.productTitle;
    if (title.includes(query)) {
      card.style.display = 'block';
      visibleCount++;
    } else {
      card.style.display = 'none';
    }
  });
  
  document.getElementById('product-count').textContent = visibleCount;
});

// Sort functionality
function sortProducts() {
  const sortValue = document.getElementById('sort-select').value;
  const grid = document.getElementById('products-grid');
  const cards = Array.from(grid.querySelectorAll('.ftac-shop-products__card'));
  
  cards.sort((a, b) => {
    switch(sortValue) {
      case 'title-ascending':
        return a.dataset.productTitle.localeCompare(b.dataset.productTitle);
      case 'title-descending':
        return b.dataset.productTitle.localeCompare(a.dataset.productTitle);
      case 'price-ascending':
        return parseInt(a.dataset.productPrice) - parseInt(b.dataset.productPrice);
      case 'price-descending':
        return parseInt(b.dataset.productPrice) - parseInt(a.dataset.productPrice);
      default:
        return 0;
    }
  });
  
  // Re-append sorted cards
  cards.forEach(card => grid.appendChild(card));
}
</script>

{% schema %}
{
  "name": "FTAC Shop Products",
  "settings": [
    {
      "type": "range",
      "id": "products_per_page",
      "min": 6,
      "max": 24,
      "step": 6,
      "default": 12,
      "label": "Products per page"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "Enable sorting"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "Enable filtering"
    }
  ],
  "presets": [
    {
      "name": "FTAC Shop Products"
    }
  ]
}
{% endschema %}
