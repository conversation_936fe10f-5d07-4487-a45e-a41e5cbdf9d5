{{ 'video-section.css' | asset_url | stylesheet_tag }}
{{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign video_id = section.settings.video.id | default: section.settings.video_url.id
  assign video_alt = section.settings.video.alt | default: section.settings.description
  assign alt = 'sections.video.load_video' | t: description: video_alt | escape
  assign poster = section.settings.video.preview_image | default: section.settings.cover_image

  if section.settings.video != null
    assign ratio_diff = section.settings.video.aspect_ratio | minus: poster.aspect_ratio | abs
    if ratio_diff < 0.01 and ratio_diff > 0
      assign fix_ratio = true
    endif
  endif
-%}

{%- capture sizes -%}
  {% if section.settings.full_width -%}
    100vw
  {%- else -%}
    (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 }}px, (min-width: 750px)
    calc(100vw - 10rem), 100vw
  {%- endif %}
{%- endcapture -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="video-section isolate{% unless section.settings.full_width %} page-width{% endunless %} section-{{ section.id }}-padding">
    <div
      {% if section.settings.full_width %}
        class="page-width"
      {% endif %}
    >
      {%- unless section.settings.heading == blank -%}
        <div class="title-wrapper title-wrapper--no-top-margin{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}">
          <h2 class="title inline-richtext {{ section.settings.heading_size }}">{{ section.settings.heading }}</h2>
        </div>
      {%- endunless -%}
    </div>
    <deferred-media
      class="video-section__media deferred-media gradient global-media-settings{% if section.settings.full_width %} global-media-settings--full-width{% endif %}{% if fix_ratio %} media-fit-cover{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
      data-media-id="{{ video_id }}"
      {% if poster != null %}
        style="--ratio-percent: {{ 1 | divided_by: poster.aspect_ratio | times: 100 }}%;"
      {% endif %}
    >
      <button
        id="Deferred-Poster-Modal-{{ video_id }}"
        class="video-section__poster media deferred-media__poster media--landscape"
        type="button"
        aria-label="{{ alt }}"
      >
        {%- if poster != null -%}
          {{
            poster
            | image_url: width: 3840
            | image_tag: sizes: sizes, widths: '375, 750, 1100, 1500, 1780, 2000, 3000, 3840', alt: alt
          }}
        {%- else -%}
          {{ 'hero-apparel-3' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
        {%- endif -%}
        <span class="deferred-media__poster-button motion-reduce">
          <span class="svg-wrapper">
            {{- 'icon-play.svg' | inline_asset_content -}}
          </span>
        </span>
      </button>
      <template>
        {%- if section.settings.video == null and section.settings.video_url != null -%}
          {%- liquid
            assign loop = ''
            if section.settings.enable_video_looping
              assign loop = '&loop=1&playlist=' | append: video_id
            endif
          -%}
          {%- if section.settings.video_url.type == 'youtube' -%}
            <iframe
              src="https://www.youtube.com/embed/{{ video_id }}?enablejsapi=1&autoplay=1{{ loop }}"
              class="js-youtube"
              allow="autoplay; encrypted-media"
              allowfullscreen
              title="{{ section.settings.description | escape }}"
            ></iframe>
          {%- else -%}
            <iframe
              src="https://player.vimeo.com/video/{{ video_id }}?autoplay=1{{ loop }}"
              class="js-vimeo"
              allow="autoplay; encrypted-media"
              allowfullscreen
              title="{{ section.settings.description | escape }}"
            ></iframe>
          {%- endif -%}
        {%- else -%}
          {{
            section.settings.video
            | video_tag:
              image_size: '1100x',
              autoplay: true,
              loop: section.settings.enable_video_looping,
              controls: true,
              muted: false
          }}
        {%- endif -%}
      </template>
    </deferred-media>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.video.name",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "t:sections.video.settings.heading.default",
      "label": "t:sections.video.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "t:sections.video.settings.enable_video_looping.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:sections.video.settings.header__1.content"
    },
    {
      "type": "video",
      "id": "video",
      "label": "t:sections.video.settings.video.label"
    },
    {
      "type": "header",
      "content": "t:sections.video.settings.header__2.content"
    },
    {
      "type": "paragraph",
      "content": "t:sections.video.settings.paragraph.content"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": ["youtube", "vimeo"],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "label": "t:sections.video.settings.video_url.label",
      "info": "t:sections.video.settings.video_url.info"
    },
    {
      "type": "image_picker",
      "id": "cover_image",
      "label": "t:sections.video.settings.cover_image.label"
    },
    {
      "type": "text",
      "id": "description",
      "label": "t:sections.video.settings.description.label",
      "info": "t:sections.video.settings.description.info"
    },
    {
      "type": "header",
      "content": "t:sections.video.settings.header__3.content"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.video.settings.full_width.label",
      "default": false
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "t:sections.video.presets.name"
    }
  ]
}
{% endschema %}
