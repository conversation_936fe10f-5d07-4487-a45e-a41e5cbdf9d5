{% comment %}
  FTAC Member Account Dashboard
  
  This template replaces the default Shopify customer account page
  with our custom member dashboard experience.
{% endcomment %}

{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

{% comment %} Check if customer is logged in {% endcomment %}
{% unless customer %}
  <script>
    window.location.href = '/pages/member-access';
  </script>
{% endunless %}

{% comment %} All logged-in customers can access the dashboard {% endcomment %}
{% comment %} Product-specific access will be checked within individual products {% endcomment %}
{% comment %} Show member dashboard for all logged-in customers {% endcomment %}
<div class="ftac-member-account">
  {% section 'ftac-member-dashboard' %}
    
    {% comment %} Account Management Section {% endcomment %}
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 24px 80px;">
      <div style="background-color: white; padding: 40px; border-radius: 16px; box-shadow: var(--ftac-shadow-lg); margin-top: 40px;">
        <h2 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-2xl); font-weight: var(--ftac-font-bold); color: var(--ftac-charcoal); margin-bottom: 24px; display: flex; align-items: center; gap: 12px;">
          <svg style="width: 28px; height: 28px; color: var(--ftac-academy-blue);" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          Account Information
        </h2>
        
        <div style="display: grid; grid-template-columns: 1fr; gap: 24px;">
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px;">
            <div>
              <label style="display: block; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); color: var(--ftac-charcoal); margin-bottom: 8px;">
                Name
              </label>
              <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); background-color: rgba(var(--ftac-warm-cream-rgb), 0.3); padding: 12px; border-radius: 8px; margin: 0;">
                {{ customer.first_name }} {{ customer.last_name }}
              </p>
            </div>
            
            <div>
              <label style="display: block; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); color: var(--ftac-charcoal); margin-bottom: 8px;">
                Email Address
              </label>
              <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); background-color: rgba(var(--ftac-warm-cream-rgb), 0.3); padding: 12px; border-radius: 8px; margin: 0;">
                {{ customer.email }}
              </p>
            </div>
            
            <div>
              <label style="display: block; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); color: var(--ftac-charcoal); margin-bottom: 8px;">
                Product Access Codes
              </label>
              <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); background-color: rgba(var(--ftac-warm-cream-rgb), 0.3); padding: 12px; border-radius: 8px; margin: 0;">
                {% assign product_codes = customer.metafields.ftac.product_access_codes %}
                {% if product_codes %}
                  {{ product_codes | size }} products unlocked
                {% else %}
                  No products unlocked yet
                {% endif %}
              </p>
            </div>
            
            <div>
              <label style="display: block; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); color: var(--ftac-charcoal); margin-bottom: 8px;">
                Member Since
              </label>
              <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); background-color: rgba(var(--ftac-warm-cream-rgb), 0.3); padding: 12px; border-radius: 8px; margin: 0;">
                {{ customer.created_at | date: "%B %d, %Y" }}
              </p>
            </div>
          </div>
          
          <div style="display: flex; flex-direction: column; gap: 16px; padding-top: 24px; border-top: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);">
            <h3 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-lg); font-weight: var(--ftac-font-semibold); color: var(--ftac-charcoal); margin: 0;">
              Account Actions
            </h3>
            
            <div style="display: flex; flex-wrap: wrap; gap: 16px;">
              <a href="/pages/contact-support" style="background-color: var(--ftac-academy-blue); color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); transition: all 0.3s ease;">
                Contact Support
              </a>
              
              <a href="{{ routes.account_logout_url }}" style="background-color: white; color: var(--ftac-academy-blue); border: 2px solid var(--ftac-academy-blue); padding: 10px 24px; border-radius: 8px; text-decoration: none; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium); transition: all 0.3s ease;">
                Sign Out
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
</div>

<style>
  .ftac-member-account a:hover {
    transform: translateY(-1px);
    box-shadow: var(--ftac-shadow-md);
  }
  
  .ftac-member-account a[style*="border: 2px"]:hover {
    background-color: var(--ftac-academy-blue);
    color: white;
  }
</style>
