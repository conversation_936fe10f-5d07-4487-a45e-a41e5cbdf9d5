{% comment %}
  FTAC Product Bundle Configuration System
  
  This snippet manages product bundle configurations including:
  - App access permissions
  - PDF download links
  - Print-on-Demand (P.O.D.) physical items
  - Bundle type and pricing information
  
  Parameters:
  - action: 'get_config', 'get_bundle_display', 'check_includes'
  - product_handle: Product handle to get configuration for
  - bundle_type: Type of bundle (basic, premium, enterprise)
  - check_type: What to check for ('app', 'pdf', 'pod')
  
  Usage:
  {% render 'ftac-bundle-config', action: 'get_config', product_handle: 'future-tech-foundations' %}
  {% assign bundle_config = ftac_bundle_config %}
{% endcomment %}

{% assign action = action | default: 'get_config' %}
{% assign product_handle = product_handle %}
{% assign bundle_type = bundle_type %}
{% assign check_type = check_type %}

{% comment %} Initialize global result variables {% endcomment %}
{% assign ftac_bundle_config = '' %}
{% assign ftac_bundle_type = '' %}
{% assign ftac_includes_app = false %}
{% assign ftac_includes_pdf = false %}
{% assign ftac_includes_pod = false %}
{% assign ftac_bundle_display = '' %}

{% comment %}
  Product Bundle Configuration Database
  
  Format: product_handle:bundle_type:includes_app:includes_pdf:includes_pod:app_url:pdf_count:pod_items
  
  Bundle Types:
  - basic: App access + PDFs
  - premium: App access + PDFs + some P.O.D. items
  - enterprise: App access + PDFs + all P.O.D. items + extras
{% endcomment %}

{% assign bundle_configurations = 'future-tech-foundations:basic:true:true:false:/apps/tech-foundations:3:0,advanced-tech-mastery:premium:true:true:true:/apps/tech-mastery:5:2,tech-leadership-suite:enterprise:true:true:true:/apps/leadership-suite:8:5,test-product:basic:true:false:false:/apps/test-app:0:0,custom-bundle:premium:true:true:true:/apps/custom:4:3' %}

{% comment %}
  PDF Resource Configuration
  
  Format: product_handle:pdf_name:pdf_url:file_size
{% endcomment %}

{% assign pdf_resources = 'future-tech-foundations:Tech Foundations Guide:/downloads/foundations-guide.pdf:2.5MB,future-tech-foundations:Quick Start Checklist:/downloads/foundations-checklist.pdf:1.2MB,future-tech-foundations:Resource Library:/downloads/foundations-resources.pdf:4.1MB,advanced-tech-mastery:Advanced Techniques Manual:/downloads/mastery-manual.pdf:6.8MB,advanced-tech-mastery:Case Studies Collection:/downloads/mastery-cases.pdf:3.4MB,advanced-tech-mastery:Implementation Templates:/downloads/mastery-templates.pdf:2.9MB,advanced-tech-mastery:Troubleshooting Guide:/downloads/mastery-troubleshooting.pdf:1.8MB,advanced-tech-mastery:Best Practices Handbook:/downloads/mastery-practices.pdf:4.2MB' %}

{% comment %}
  P.O.D. (Print-on-Demand) Items Configuration
  
  Format: product_handle:item_name:item_type:etsy_url
{% endcomment %}

{% assign pod_items = 'advanced-tech-mastery:Tech Mastery Notebook:notebook:https://www.etsy.com/listing/notebook-tech-mastery,advanced-tech-mastery:Reference Cards Set:cards:https://www.etsy.com/listing/cards-tech-ref,tech-leadership-suite:Leadership Planner:planner:https://www.etsy.com/listing/planner-leadership,tech-leadership-suite:Team Building Cards:cards:https://www.etsy.com/listing/cards-team-building,tech-leadership-suite:Strategy Workbook:workbook:https://www.etsy.com/listing/workbook-strategy,tech-leadership-suite:Goal Setting Journal:journal:https://www.etsy.com/listing/journal-goals,tech-leadership-suite:Meeting Templates:templates:https://www.etsy.com/listing/templates-meetings' %}

{% case action %}
  {% when 'get_config' %}
    {% comment %} Get complete bundle configuration for a product {% endcomment %}
    {% if product_handle %}
      {% assign configs = bundle_configurations | split: ',' %}
      {% for config in configs %}
        {% assign config_parts = config | split: ':' %}
        {% if config_parts[0] == product_handle %}
          {% assign ftac_bundle_type = config_parts[1] %}
          {% assign ftac_includes_app = config_parts[2] %}
          {% assign ftac_includes_pdf = config_parts[3] %}
          {% assign ftac_includes_pod = config_parts[4] %}
          {% assign ftac_app_url = config_parts[5] %}
          {% assign ftac_pdf_count = config_parts[6] %}
          {% assign ftac_pod_count = config_parts[7] %}
          {% break %}
        {% endif %}
      {% endfor %}
    {% endif %}

  {% when 'get_bundle_display' %}
    {% comment %} Get formatted display information for bundle {% endcomment %}
    {% if product_handle %}
      {% render 'ftac-bundle-config', action: 'get_config', product_handle: product_handle %}
      
      {% assign display_parts = '' %}
      
      {% if ftac_includes_app == 'true' %}
        {% assign display_parts = display_parts | append: '✓ App Access' %}
      {% endif %}
      
      {% if ftac_includes_pdf == 'true' %}
        {% if display_parts != '' %}
          {% assign display_parts = display_parts | append: ' • ' %}
        {% endif %}
        {% assign display_parts = display_parts | append: '✓ PDF Downloads (' | append: ftac_pdf_count | append: ')' %}
      {% endif %}
      
      {% if ftac_includes_pod == 'true' %}
        {% if display_parts != '' %}
          {% assign display_parts = display_parts | append: ' • ' %}
        {% endif %}
        {% assign display_parts = display_parts | append: '✓ Physical Items (' | append: ftac_pod_count | append: ')' %}
      {% endif %}
      
      {% assign ftac_bundle_display = display_parts %}
    {% endif %}

  {% when 'check_includes' %}
    {% comment %} Check if bundle includes specific type {% endcomment %}
    {% if product_handle and check_type %}
      {% render 'ftac-bundle-config', action: 'get_config', product_handle: product_handle %}
      
      {% case check_type %}
        {% when 'app' %}
          {% assign ftac_check_result = ftac_includes_app %}
        {% when 'pdf' %}
          {% assign ftac_check_result = ftac_includes_pdf %}
        {% when 'pod' %}
          {% assign ftac_check_result = ftac_includes_pod %}
      {% endcase %}
    {% endif %}

  {% when 'get_pdfs' %}
    {% comment %} Get PDF resources for a product {% endcomment %}
    {% if product_handle %}
      {% assign pdf_list = '' %}
      {% assign pdfs = pdf_resources | split: ',' %}
      {% for pdf in pdfs %}
        {% assign pdf_parts = pdf | split: ':' %}
        {% if pdf_parts[0] == product_handle %}
          {% if pdf_list != '' %}
            {% assign pdf_list = pdf_list | append: '|' %}
          {% endif %}
          {% assign pdf_list = pdf_list | append: pdf_parts[1] | append: ':' | append: pdf_parts[2] | append: ':' | append: pdf_parts[3] %}
        {% endif %}
      {% endfor %}
      {% assign ftac_pdf_list = pdf_list %}
    {% endif %}

  {% when 'get_pod_items' %}
    {% comment %} Get P.O.D. items for a product {% endcomment %}
    {% if product_handle %}
      {% assign pod_list = '' %}
      {% assign pods = pod_items | split: ',' %}
      {% for pod in pods %}
        {% assign pod_parts = pod | split: ':' %}
        {% if pod_parts[0] == product_handle %}
          {% if pod_list != '' %}
            {% assign pod_list = pod_list | append: '|' %}
          {% endif %}
          {% assign pod_list = pod_list | append: pod_parts[1] | append: ':' | append: pod_parts[2] | append: ':' | append: pod_parts[3] %}
        {% endif %}
      {% endfor %}
      {% assign ftac_pod_list = pod_list %}
    {% endif %}

  {% when 'get_app_info' %}
    {% comment %} Get app access information {% endcomment %}
    {% if product_handle %}
      {% render 'ftac-bundle-config', action: 'get_config', product_handle: product_handle %}
      {% if ftac_includes_app == 'true' %}
        {% assign ftac_app_access = true %}
        {% assign ftac_app_url = ftac_app_url %}
      {% else %}
        {% assign ftac_app_access = false %}
      {% endif %}
    {% endif %}

  {% else %}
    {% comment %} Default: get basic config {% endcomment %}
    {% render 'ftac-bundle-config', action: 'get_config', product_handle: product_handle %}
{% endcase %}

{% comment %}
  Bundle Type Descriptions:
  
  Basic Bundle:
  - App access to core features
  - Essential PDF guides and checklists
  - No physical items
  
  Premium Bundle:
  - Full app access with advanced features
  - Complete PDF library
  - Select physical items (notebooks, cards)
  
  Enterprise Bundle:
  - Full app access with premium features
  - Complete PDF library with bonus content
  - All physical items including planners and workbooks
  - Priority support
{% endcomment %}

{% comment %}
  Demo Product Configurations:
  
  future-tech-foundations (Basic - $14.99):
  - ✓ App Access (Tech Foundations App)
  - ✓ 3 PDF Downloads
  - ✗ Physical Items
  
  advanced-tech-mastery (Premium - $29.99):
  - ✓ App Access (Tech Mastery App)
  - ✓ 5 PDF Downloads
  - ✓ 2 Physical Items
  
  tech-leadership-suite (Enterprise - $49.99):
  - ✓ App Access (Leadership Suite App)
  - ✓ 8 PDF Downloads
  - ✓ 5 Physical Items
  
  test-product (Basic - $14.99):
  - ✓ App Access (Test App)
  - ✗ PDF Downloads
  - ✗ Physical Items
{% endcomment %}
