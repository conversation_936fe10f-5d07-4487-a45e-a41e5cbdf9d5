{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'section-related-products.css' | asset_url | stylesheet_tag }}

{% if section.settings.image_shape == 'blob' %}
  {{ 'mask-blobs.css' | asset_url | stylesheet_tag }}
{%- endif -%}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <product-recommendations
    class="related-products page-width section-{{ section.id }}-padding isolate{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
    data-url="{{ routes.product_recommendations_url }}?limit={{ section.settings.products_to_show }}"
    data-section-id="{{ section.id }}"
    data-product-id="{{ product.id }}"
  >
    {% if recommendations.performed and recommendations.products_count > 0 %}
      <h2 class="related-products__heading inline-richtext {{ section.settings.heading_size }}">
        {{ section.settings.heading }}
      </h2>
      <ul
        class="grid product-grid grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down"
        role="list"
      >
        {% assign skip_card_product_styles = false %}
        {% for recommendation in recommendations.products %}
          <li class="grid__item">
            {% render 'card-product',
              card_product: recommendation,
              media_aspect_ratio: section.settings.image_ratio,
              image_shape: section.settings.image_shape,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              skip_styles: skip_card_product_styles
            %}
          </li>
          {%- assign skip_card_product_styles = true -%}
        {% endfor %}
      </ul>
    {% endif %}
  </product-recommendations>
  {% if section.settings.image_shape == 'arch' %}
    {{ 'mask-arch.svg' | inline_asset_content }}
  {%- endif -%}
</div>

{% schema %}
{
  "name": "t:sections.related-products.name",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.related-products.settings.paragraph__1.content"
    },
    {
      "type": "inline_richtext",
      "id": "heading",
      "default": "t:sections.related-products.settings.paragraph__1.default",
      "label": "t:sections.related-products.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 10,
      "step": 1,
      "default": 4,
      "label": "t:sections.related-products.settings.products_to_show.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "t:sections.related-products.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.related-products.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.related-products.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.related-products.settings.columns_mobile.options__2.label"
        }
      ]    
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.related-products.settings.header__2.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.related-products.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.related-products.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.related-products.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.related-products.settings.image_ratio.label"
    },
    {
      "type": "select",
      "id": "image_shape",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.image_shape.options__1.label"
        },
        {
          "value": "arch",
          "label": "t:sections.all.image_shape.options__2.label"
        },
        {
          "value": "blob",
          "label": "t:sections.all.image_shape.options__3.label"
        },
        {
          "value": "chevronleft",
          "label": "t:sections.all.image_shape.options__4.label"
        },
        {
          "value": "chevronright",
          "label": "t:sections.all.image_shape.options__5.label"
        },
        {
          "value": "diamond",
          "label": "t:sections.all.image_shape.options__6.label"
        },
        {
          "value": "parallelogram",
          "label": "t:sections.all.image_shape.options__7.label"
        },
        {
          "value": "round",
          "label": "t:sections.all.image_shape.options__8.label"
        }
      ],
      "default": "default",
      "label": "t:sections.all.image_shape.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.related-products.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.related-products.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.related-products.settings.show_rating.label",
      "info": "t:sections.related-products.settings.show_rating.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
