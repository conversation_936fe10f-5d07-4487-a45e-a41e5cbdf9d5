/**
 * FTAC Product Guard JavaScript
 * 
 * Handles access code validation and form submission for product guards.
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize all product guard forms
  const guardForms = document.querySelectorAll('.ftac-product-guard__form');
  
  guardForms.forEach(function(form) {
    initializeGuardForm(form);
  });
});

function initializeGuardForm(form) {
  const input = form.querySelector('.ftac-product-guard__input');
  const submitBtn = form.querySelector('.ftac-product-guard__submit');
  const errorDiv = form.querySelector('.ftac-product-guard__error');
  const loadingDiv = form.querySelector('.ftac-product-guard__loading');
  
  // Format access code input as user types
  if (input) {
    input.addEventListener('input', function(e) {
      formatAccessCode(e.target);
    });
    
    input.addEventListener('paste', function(e) {
      setTimeout(() => formatAccessCode(e.target), 10);
    });
  }
  
  // Handle form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    handleAccessCodeSubmission(form);
  });
}

function formatAccessCode(input) {
  let value = input.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  
  // Format as FTAC-XXXX-YYYY-ZZZZ
  if (value.length > 0) {
    if (!value.startsWith('FTAC')) {
      if (value.length <= 4) {
        value = 'FTAC';
      } else {
        value = 'FTAC' + value.substring(4);
      }
    }
    
    // Add dashes at appropriate positions
    let formatted = value.substring(0, 4); // FTAC
    if (value.length > 4) {
      formatted += '-' + value.substring(4, 8);
    }
    if (value.length > 8) {
      formatted += '-' + value.substring(8, 12);
    }
    if (value.length > 12) {
      formatted += '-' + value.substring(12, 16);
    }
    
    input.value = formatted;
  }
}

function handleAccessCodeSubmission(form) {
  const formData = new FormData(form);
  const accessCode = formData.get('access_code');
  const productHandle = formData.get('product_handle');
  const customerId = formData.get('customer_id');
  
  const submitBtn = form.querySelector('.ftac-product-guard__submit');
  const errorDiv = form.querySelector('.ftac-product-guard__error');
  const loadingDiv = form.querySelector('.ftac-product-guard__loading');
  
  // Validate access code format
  const accessCodePattern = /^FTAC-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
  if (!accessCodePattern.test(accessCode)) {
    showError(errorDiv, 'Please enter a valid access code in the format FTAC-XXXX-YYYY-ZZZZ');
    return;
  }
  
  // Show loading state
  showLoading(form, true);
  hideError(errorDiv);
  
  // Simulate API call for access code validation
  // In a real implementation, this would make a request to a Shopify app or webhook
  validateAccessCode(accessCode, productHandle, customerId)
    .then(function(result) {
      showLoading(form, false);
      
      if (result.success) {
        // Access granted - reload page to show content
        showSuccess(form, result.message || 'Access granted! Reloading page...');
        setTimeout(function() {
          window.location.reload();
        }, 1500);
      } else {
        showError(errorDiv, result.message || 'Invalid access code. Please check and try again.');
      }
    })
    .catch(function(error) {
      showLoading(form, false);
      showError(errorDiv, 'Something went wrong. Please try again or contact support.');
      console.error('Access code validation error:', error);
    });
}

function validateAccessCode(accessCode, productHandle, customerId) {
  // This is a mock implementation
  // In production, this would integrate with Shopify's customer metafields API
  // or a custom app that handles access code validation
  
  return new Promise(function(resolve) {
    setTimeout(function() {
      // Mock validation logic
      const validCodes = {
        'FTAC-FOUN-DEMO-1234': 'future-tech-foundations',
        'FTAC-MAST-DEMO-5678': 'advanced-tech-mastery',
        'FTAC-LEAD-DEMO-9012': 'tech-leadership-suite'
      };
      
      const expectedProduct = validCodes[accessCode];
      
      if (expectedProduct && expectedProduct === productHandle) {
        // Store access in localStorage for demo purposes
        // In production, this would update customer metafields
        const accessData = JSON.parse(localStorage.getItem('ftac_product_access') || '{}');
        accessData[productHandle] = {
          access_code: accessCode,
          granted_date: new Date().toISOString(),
          customer_id: customerId
        };
        localStorage.setItem('ftac_product_access', JSON.stringify(accessData));
        
        resolve({
          success: true,
          message: 'Access granted! You now have access to this content.'
        });
      } else if (expectedProduct) {
        resolve({
          success: false,
          message: 'This access code is for a different product.'
        });
      } else {
        resolve({
          success: false,
          message: 'Invalid access code. Please check your code and try again.'
        });
      }
    }, 1500); // Simulate network delay
  });
}

function showLoading(form, show) {
  const submitBtn = form.querySelector('.ftac-product-guard__submit');
  const loadingDiv = form.querySelector('.ftac-product-guard__loading');
  
  if (show) {
    submitBtn.disabled = true;
    submitBtn.textContent = 'Verifying...';
    loadingDiv.style.display = 'flex';
  } else {
    submitBtn.disabled = false;
    submitBtn.textContent = 'Unlock';
    loadingDiv.style.display = 'none';
  }
}

function showError(errorDiv, message) {
  errorDiv.textContent = message;
  errorDiv.style.display = 'block';
}

function hideError(errorDiv) {
  errorDiv.style.display = 'none';
}

function showSuccess(form, message) {
  const errorDiv = form.querySelector('.ftac-product-guard__error');
  errorDiv.style.background = '#f0fdf4';
  errorDiv.style.borderColor = '#bbf7d0';
  errorDiv.style.color = '#166534';
  errorDiv.textContent = message;
  errorDiv.style.display = 'block';
}

// Utility function to check if user has access to a product (for demo purposes)
function hasProductAccess(productHandle) {
  const accessData = JSON.parse(localStorage.getItem('ftac_product_access') || '{}');
  return accessData.hasOwnProperty(productHandle);
}

// Export for use in other scripts
window.FTACProductGuard = {
  hasProductAccess: hasProductAccess,
  validateAccessCode: validateAccessCode
};
