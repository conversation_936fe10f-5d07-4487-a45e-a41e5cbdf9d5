{% comment %}
  FTAC Product Access Guard
  
  This snippet provides a simple way to protect product content with
  access code requirements. It can be easily added to any product template
  to implement product-level access control.
  
  Usage:
  {% render 'ftac-product-guard', product: product, content_type: 'premium' %}
  
  Parameters:
  - product: The product object to check access for
  - content_type: Type of content being protected (optional, for messaging)
  - show_teaser: Whether to show a content teaser (default: false)
  - teaser_content: HTML content to show as teaser (optional)
{% endcomment %}

{% assign product = product %}
{% assign content_type = content_type | default: 'premium content' %}
{% assign show_teaser = show_teaser | default: false %}
{% assign teaser_content = teaser_content | default: '' %}

{% comment %} Check if customer is logged in {% endcomment %}
{% unless customer %}
  {% comment %} Show login requirement {% endcomment %}
  <div class="ftac-product-guard ftac-product-guard--login-required">
    <div class="ftac-product-guard__overlay">
      <div class="ftac-product-guard__content">
        <div class="ftac-product-guard__icon">
          <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <h3 class="ftac-product-guard__title">Sign In Required</h3>
        <p class="ftac-product-guard__message">
          This {{ content_type }} requires a Future Tech Academy Club account. 
          Sign in to unlock with your access code.
        </p>
        <a href="/pages/member-access" class="ftac-product-guard__button">
          Sign In to Continue
        </a>
      </div>
    </div>
  </div>
{% else %}
  {% comment %} Customer is logged in - check product access {% endcomment %}
  {% render 'ftac-product-access', action: 'check_access', customer: customer, product_handle: product.handle %}
  {% assign has_access = ftac_access_result %}
  
  {% if has_access %}
    {% comment %} Customer has access - content will be shown normally {% endcomment %}
    {% comment %} This guard doesn't block anything when access is granted {% endcomment %}
  {% else %}
    {% comment %} Customer needs access code {% endcomment %}
    <div class="ftac-product-guard ftac-product-guard--access-required">
      {% if show_teaser and teaser_content != '' %}
        {% comment %} Show teaser content {% endcomment %}
        <div class="ftac-product-guard__teaser">
          {{ teaser_content }}
        </div>
      {% endif %}
      
      <div class="ftac-product-guard__overlay">
        <div class="ftac-product-guard__content">
          <div class="ftac-product-guard__icon">
            <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"></path>
            </svg>
          </div>
          <h3 class="ftac-product-guard__title">Access Code Required</h3>
          <p class="ftac-product-guard__message">
            Enter your access code to unlock this {{ content_type }}.
          </p>
          
          {% comment %} Compact access code entry form {% endcomment %}
          <form class="ftac-product-guard__form" id="guard-form-{{ product.handle }}">
            <input type="hidden" name="product_handle" value="{{ product.handle }}">
            <input type="hidden" name="customer_id" value="{{ customer.id }}">
            
            <div class="ftac-product-guard__input-group">
              <input 
                type="text" 
                name="access_code" 
                class="ftac-product-guard__input" 
                placeholder="FTAC-XXXX-YYYY-ZZZZ"
                pattern="[Ff][Tt][Aa][Cc]-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}"
                maxlength="19"
                required
              >
              <button type="submit" class="ftac-product-guard__submit">
                Unlock
              </button>
            </div>
            
            <div class="ftac-product-guard__error" style="display: none;"></div>
            <div class="ftac-product-guard__loading" style="display: none;">
              <div class="ftac-product-guard__spinner"></div>
              <span>Verifying...</span>
            </div>
          </form>
          
          <p class="ftac-product-guard__help">
            <a href="/pages/contact-support">Need help finding your access code?</a>
          </p>
        </div>
      </div>
    </div>
  {% endif %}
{% endunless %}

{{ 'ftac-product-guard.js' | asset_url | script_tag }}

<style>
  .ftac-product-guard {
    position: relative;
    min-height: 200px;
  }
  
  .ftac-product-guard__teaser {
    filter: blur(3px);
    pointer-events: none;
    user-select: none;
  }
  
  .ftac-product-guard__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
  
  .ftac-product-guard__content {
    background: white;
    padding: var(--ftac-space-8);
    border-radius: var(--ftac-radius-xl);
    box-shadow: var(--ftac-shadow-xl);
    text-align: center;
    max-width: 400px;
    border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.1);
  }
  
  .ftac-product-guard__icon {
    width: 60px;
    height: 60px;
    background: rgba(var(--ftac-academy-blue-rgb), 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--ftac-space-4);
    color: var(--ftac-academy-blue);
  }
  
  .ftac-product-guard__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-3);
  }
  
  .ftac-product-guard__message {
    font-family: var(--ftac-font-secondary);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-6);
    line-height: 1.5;
  }
  
  .ftac-product-guard__button {
    display: inline-block;
    background: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-3) var(--ftac-space-6);
    border-radius: var(--ftac-radius-lg);
    text-decoration: none;
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-medium);
    transition: all 0.2s ease;
  }
  
  .ftac-product-guard__button:hover {
    background: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
  }
  
  .ftac-product-guard__form {
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-product-guard__input-group {
    display: flex;
    gap: var(--ftac-space-2);
    margin-bottom: var(--ftac-space-3);
  }
  
  .ftac-product-guard__input {
    flex: 1;
    padding: var(--ftac-space-3);
    border: 2px solid rgba(var(--ftac-charcoal-rgb), 0.2);
    border-radius: var(--ftac-radius-lg);
    font-family: 'Courier New', monospace;
    font-size: var(--ftac-text-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
    text-align: center;
  }
  
  .ftac-product-guard__input:focus {
    outline: none;
    border-color: var(--ftac-academy-blue);
  }
  
  .ftac-product-guard__submit {
    background: var(--ftac-academy-blue);
    color: white;
    border: none;
    padding: var(--ftac-space-3) var(--ftac-space-4);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-medium);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .ftac-product-guard__submit:hover {
    background: rgba(var(--ftac-academy-blue-rgb), 0.9);
  }
  
  .ftac-product-guard__submit:disabled {
    background: rgba(var(--ftac-charcoal-rgb), 0.3);
    cursor: not-allowed;
  }
  
  .ftac-product-guard__error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: var(--ftac-space-2);
    border-radius: var(--ftac-radius-md);
    font-size: var(--ftac-text-sm);
    margin-bottom: var(--ftac-space-3);
  }
  
  .ftac-product-guard__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ftac-space-2);
    color: var(--ftac-academy-blue);
    font-size: var(--ftac-text-sm);
  }
  
  .ftac-product-guard__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.2);
    border-top: 2px solid var(--ftac-academy-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .ftac-product-guard__help {
    font-size: var(--ftac-text-sm);
    color: var(--ftac-charcoal);
    opacity: 0.7;
  }
  
  .ftac-product-guard__help a {
    color: var(--ftac-academy-blue);
    text-decoration: none;
  }
  
  .ftac-product-guard__help a:hover {
    text-decoration: underline;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
