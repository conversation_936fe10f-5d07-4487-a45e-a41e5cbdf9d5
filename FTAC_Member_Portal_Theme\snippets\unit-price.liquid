{%- doc -%}
  Renders the unit price, including its measurement.

  @param {object} price - The unit price (money or string).
  @param {object} measurement - The unit_price_measurement object.
  @param {boolean} [hide_accessibility_label] - Whether to hide the accessibility label (default: false).

  @example
  {% render 'unit-price', price: variant.unit_price, measurement: variant.unit_price_measurement %}

  @example
  {% render 'unit-price', price: line_item.unit_price | money_with_currency, measurement: line_item.unit_price_measurement }
{%- enddoc -%}
<small class="unit-price caption">
  {%- unless hide_accessibility_label -%}
    <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
  {%- endunless -%}
  <span class="price-item price-item--last">
    {{ price | unit_price_with_measurement: measurement }}
  </span>
</small>