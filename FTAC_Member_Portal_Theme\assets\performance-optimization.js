/**
 * Performance Optimization for Luxury Theme
 * Lazy loading, critical CSS, and performance monitoring
 */

class PerformanceOptimizer {
  constructor() {
    this.init();
  }

  init() {
    this.setupLazyLoading();
    this.setupCriticalCSS();
    this.setupImageOptimization();
    this.setupFontOptimization();
    this.setupPerformanceMonitoring();
    this.setupResourceHints();
  }

  // Lazy loading for images and luxury components
  setupLazyLoading() {
    // Native lazy loading for images
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            img.classList.add('lazy-loaded');
            observer.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      images.forEach(img => imageObserver.observe(img));
    } else {
      // Fallback for older browsers
      images.forEach(img => {
        img.src = img.dataset.src;
        img.classList.remove('lazy');
      });
    }

    // Lazy load luxury components
    this.lazyLoadComponents();
  }

  lazyLoadComponents() {
    const luxuryComponents = document.querySelectorAll('.ftac-card-luxury, .ftac-btn-luxury');
    
    const componentObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('ftac-fade-in');
          componentObserver.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: '20px 0px',
      threshold: 0.1
    });

    luxuryComponents.forEach(component => {
      component.style.opacity = '0';
      componentObserver.observe(component);
    });
  }

  // Critical CSS management
  setupCriticalCSS() {
    // Load non-critical CSS asynchronously
    const nonCriticalCSS = [
      '/assets/luxury-components.css',
      '/assets/section-*.css'
    ];

    nonCriticalCSS.forEach(href => {
      this.loadCSSAsync(href);
    });
  }

  loadCSSAsync(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = 'print';
    link.onload = function() {
      this.media = 'all';
    };
    document.head.appendChild(link);
  }

  // Image optimization
  setupImageOptimization() {
    // WebP support detection
    this.supportsWebP().then(supported => {
      if (supported) {
        document.documentElement.classList.add('webp');
      }
    });

    // Responsive image loading
    this.setupResponsiveImages();
  }

  supportsWebP() {
    return new Promise(resolve => {
      const webP = new Image();
      webP.onload = webP.onerror = () => {
        resolve(webP.height === 2);
      };
      webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    });
  }

  setupResponsiveImages() {
    const images = document.querySelectorAll('img[data-sizes]');
    
    images.forEach(img => {
      const sizes = img.dataset.sizes.split(',');
      const currentWidth = window.innerWidth;
      
      let selectedSize = sizes[0];
      for (let size of sizes) {
        const [width, url] = size.split(':');
        if (currentWidth >= parseInt(width)) {
          selectedSize = url;
        }
      }
      
      if (img.dataset.src) {
        img.dataset.src = selectedSize;
      } else {
        img.src = selectedSize;
      }
    });
  }

  // Font optimization
  setupFontOptimization() {
    // Preload critical fonts
    const criticalFonts = [
      'https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&display=swap',
      'https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&display=swap'
    ];

    criticalFonts.forEach(fontUrl => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = fontUrl;
      document.head.appendChild(link);
    });

    // Font display optimization
    this.optimizeFontDisplay();
  }

  optimizeFontDisplay() {
    const style = document.createElement('style');
    style.textContent = `
      @font-face {
        font-family: 'Playfair Display';
        font-display: swap;
      }
      @font-face {
        font-family: 'Open Sans';
        font-display: swap;
      }
      @font-face {
        font-family: 'Dancing Script';
        font-display: swap;
      }
    `;
    document.head.appendChild(style);
  }

  // Performance monitoring
  setupPerformanceMonitoring() {
    // Core Web Vitals monitoring
    this.measureCoreWebVitals();
    
    // Custom performance metrics
    this.measureCustomMetrics();
  }

  measureCoreWebVitals() {
    // Largest Contentful Paint (LCP)
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      console.log('LCP:', lastEntry.startTime);
      this.reportMetric('LCP', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay (FID)
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      entries.forEach(entry => {
        console.log('FID:', entry.processingStart - entry.startTime);
        this.reportMetric('FID', entry.processingStart - entry.startTime);
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift (CLS)
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      console.log('CLS:', clsValue);
      this.reportMetric('CLS', clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }

  measureCustomMetrics() {
    // Time to Interactive (TTI)
    window.addEventListener('load', () => {
      setTimeout(() => {
        const tti = performance.now();
        console.log('TTI:', tti);
        this.reportMetric('TTI', tti);
      }, 0);
    });

    // Luxury component load time
    const luxuryComponentsLoaded = performance.mark('luxury-components-start');
    document.addEventListener('DOMContentLoaded', () => {
      performance.mark('luxury-components-end');
      performance.measure('luxury-components-load', 'luxury-components-start', 'luxury-components-end');
      
      const measure = performance.getEntriesByName('luxury-components-load')[0];
      console.log('Luxury Components Load Time:', measure.duration);
      this.reportMetric('LuxuryComponentsLoad', measure.duration);
    });
  }

  reportMetric(name, value) {
    // Send metrics to analytics (implement based on your analytics provider)
    if (window.gtag) {
      gtag('event', 'performance_metric', {
        metric_name: name,
        metric_value: Math.round(value),
        custom_parameter: 'luxury_theme'
      });
    }
    
    // Store in localStorage for debugging
    const metrics = JSON.parse(localStorage.getItem('ftac_performance_metrics') || '{}');
    metrics[name] = value;
    metrics.timestamp = Date.now();
    localStorage.setItem('ftac_performance_metrics', JSON.stringify(metrics));
  }

  // Resource hints
  setupResourceHints() {
    // DNS prefetch for external resources
    const dnsPrefetchDomains = [
      'fonts.googleapis.com',
      'fonts.gstatic.com',
      'cdn.shopify.com'
    ];

    dnsPrefetchDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = `//${domain}`;
      document.head.appendChild(link);
    });

    // Preconnect to critical resources
    const preconnectDomains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ];

    preconnectDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = domain;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });
  }

  // Debounce function for performance
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Throttle function for scroll events
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Memory usage monitoring
  monitorMemoryUsage() {
    if ('memory' in performance) {
      const memInfo = performance.memory;
      console.log('Memory Usage:', {
        used: Math.round(memInfo.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memInfo.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memInfo.jsHeapSizeLimit / 1048576) + ' MB'
      });
    }
  }

  // Clean up unused resources
  cleanup() {
    // Remove unused event listeners
    // Clear unnecessary intervals
    // Garbage collect large objects
    
    // Monitor memory after cleanup
    setTimeout(() => {
      this.monitorMemoryUsage();
    }, 1000);
  }
}

// Initialize performance optimizer
document.addEventListener('DOMContentLoaded', () => {
  window.performanceOptimizer = new PerformanceOptimizer();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (window.performanceOptimizer) {
    window.performanceOptimizer.cleanup();
  }
});

// Export for use in other scripts
window.PerformanceOptimizer = PerformanceOptimizer;
