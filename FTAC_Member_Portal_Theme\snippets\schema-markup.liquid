{% comment %}
  Sc<PERSON>a <PERSON>up for SEO
  Implements structured data for products, reviews, business information, and more
{% endcomment %}

{% liquid
  assign current_page_type = request.page_type
  assign current_product = product
  assign current_collection = collection
%}

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Organization",
      "@id": "{{ shop.url }}#organization",
      "name": "{{ shop.name | escape }}",
      "url": "{{ shop.url }}",
      "logo": {
        "@type": "ImageObject",
        "url": "{{ 'logo.png' | asset_url }}",
        "width": 200,
        "height": 60
      },
      "description": "{{ shop.description | escape }}",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-FTAC-HELP",
        "contactType": "Customer Service",
        "availableLanguage": "English"
      },
      "sameAs": [
        "https://www.facebook.com/futuretechacademyclub",
        "https://www.instagram.com/futuretechacademyclub",
        "https://www.twitter.com/ftacclub",
        "https://www.pinterest.com/futuretechacademyclub"
      ]
    },
    {
      "@type": "WebSite",
      "@id": "{{ shop.url }}#website",
      "url": "{{ shop.url }}",
      "name": "{{ shop.name | escape }}",
      "description": "{{ shop.description | escape }}",
      "publisher": {
        "@id": "{{ shop.url }}#organization"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": {
          "@type": "EntryPoint",
          "urlTemplate": "{{ shop.url }}/search?q={search_term_string}"
        },
        "query-input": "required name=search_term_string"
      }
    }
    {% if current_page_type == 'product' and current_product %}
      ,{
        "@type": "Product",
        "@id": "{{ shop.url }}{{ current_product.url }}#product",
        "name": "{{ current_product.title | escape }}",
        "description": "{{ current_product.description | strip_html | truncate: 300 | escape }}",
        "image": [
          {% for image in current_product.images limit: 5 %}
            "{{ image | image_url: width: 800 }}"{% unless forloop.last %},{% endunless %}
          {% endfor %}
        ],
        "brand": {
          "@type": "Brand",
          "name": "{{ current_product.vendor | default: shop.name | escape }}"
        },
        "manufacturer": {
          "@type": "Organization",
          "name": "{{ current_product.vendor | default: shop.name | escape }}"
        },
        "sku": "{{ current_product.selected_or_first_available_variant.sku }}",
        "gtin": "{{ current_product.selected_or_first_available_variant.barcode }}",
        "category": "{{ current_product.type | escape }}",
        "offers": {
          "@type": "Offer",
          "url": "{{ shop.url }}{{ current_product.url }}",
          "priceCurrency": "{{ cart.currency.iso_code }}",
          "price": "{{ current_product.selected_or_first_available_variant.price | money_without_currency }}",
          {% if current_product.selected_or_first_available_variant.compare_at_price > current_product.selected_or_first_available_variant.price %}
          "priceValidUntil": "{{ 'now' | date: '%s' | plus: 2592000 | date: '%Y-%m-%d' }}",
          {% endif %}
          "availability": "{% if current_product.selected_or_first_available_variant.available %}https://schema.org/InStock{% else %}https://schema.org/OutOfStock{% endif %}",
          "seller": {
            "@id": "{{ shop.url }}#organization"
          },
          "hasMerchantReturnPolicy": {
            "@type": "MerchantReturnPolicy",
            "applicableCountry": "US",
            "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
            "merchantReturnDays": 30,
            "returnMethod": "https://schema.org/ReturnByMail",
            "returnFees": "https://schema.org/FreeReturn"
          },
          "shippingDetails": {
            "@type": "OfferShippingDetails",
            "shippingRate": {
              "@type": "MonetaryAmount",
              "value": "0",
              "currency": "{{ cart.currency.iso_code }}"
            },
            "shippingDestination": {
              "@type": "DefinedRegion",
              "addressCountry": "US"
            },
            "deliveryTime": {
              "@type": "ShippingDeliveryTime",
              "handlingTime": {
                "@type": "QuantitativeValue",
                "minValue": 1,
                "maxValue": 2,
                "unitCode": "DAY"
              },
              "transitTime": {
                "@type": "QuantitativeValue",
                "minValue": 3,
                "maxValue": 7,
                "unitCode": "DAY"
              }
            }
          }
        }
        {% assign review_count = 0 %}
        {% assign total_rating = 0 %}
        {% for review in current_product.metafields.reviews %}
          {% assign review_count = review_count | plus: 1 %}
          {% assign total_rating = total_rating | plus: review.rating %}
        {% endfor %}
        {% if review_count > 0 %}
          ,"aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "{{ total_rating | divided_by: review_count | round: 1 }}",
            "reviewCount": "{{ review_count }}",
            "bestRating": "5",
            "worstRating": "1"
          }
          ,"review": [
            {% for review in current_product.metafields.reviews limit: 5 %}
              {
                "@type": "Review",
                "reviewRating": {
                  "@type": "Rating",
                  "ratingValue": "{{ review.rating }}",
                  "bestRating": "5",
                  "worstRating": "1"
                },
                "author": {
                  "@type": "Person",
                  "name": "{{ review.author | escape }}"
                },
                "reviewBody": "{{ review.content | escape }}",
                "datePublished": "{{ review.date | date: '%Y-%m-%d' }}"
              }{% unless forloop.last %},{% endunless %}
            {% endfor %}
          ]
        {% endif %}
      }
    {% endif %}
    {% if current_page_type == 'collection' and current_collection %}
      ,{
        "@type": "CollectionPage",
        "@id": "{{ shop.url }}{{ current_collection.url }}#collection",
        "name": "{{ current_collection.title | escape }}",
        "description": "{{ current_collection.description | strip_html | truncate: 300 | escape }}",
        "url": "{{ shop.url }}{{ current_collection.url }}",
        "mainEntity": {
          "@type": "ItemList",
          "numberOfItems": "{{ current_collection.products_count }}",
          "itemListElement": [
            {% for product in current_collection.products limit: 10 %}
              {
                "@type": "Product",
                "position": "{{ forloop.index }}",
                "name": "{{ product.title | escape }}",
                "url": "{{ shop.url }}{{ product.url }}",
                "image": "{{ product.featured_image | image_url: width: 400 }}",
                "offers": {
                  "@type": "Offer",
                  "price": "{{ product.price | money_without_currency }}",
                  "priceCurrency": "{{ cart.currency.iso_code }}",
                  "availability": "{% if product.available %}https://schema.org/InStock{% else %}https://schema.org/OutOfStock{% endif %}"
                }
              }{% unless forloop.last %},{% endunless %}
            {% endfor %}
          ]
        }
      }
    {% endif %}
    {% if current_page_type == 'article' and blog and article %}
      ,{
        "@type": "Article",
        "@id": "{{ shop.url }}{{ blog.url }}/{{ article.handle }}#article",
        "headline": "{{ article.title | escape }}",
        "description": "{{ article.excerpt | default: article.content | strip_html | truncate: 300 | escape }}",
        "image": "{{ article.image | image_url: width: 800 }}",
        "author": {
          "@type": "Person",
          "name": "{{ article.author | escape }}"
        },
        "publisher": {
          "@id": "{{ shop.url }}#organization"
        },
        "datePublished": "{{ article.published_at | date: '%Y-%m-%d' }}",
        "dateModified": "{{ article.updated_at | date: '%Y-%m-%d' }}",
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": "{{ shop.url }}{{ blog.url }}/{{ article.handle }}"
        }
      }
    {% endif %}
    {% if current_page_type == 'index' %}
      ,{
        "@type": "WebPage",
        "@id": "{{ shop.url }}#homepage",
        "url": "{{ shop.url }}",
        "name": "{{ page_title | escape }}",
        "description": "{{ page_description | default: shop.description | escape }}",
        "isPartOf": {
          "@id": "{{ shop.url }}#website"
        },
        "about": {
          "@id": "{{ shop.url }}#organization"
        },
        "mainEntity": {
          "@type": "ItemList",
          "name": "Featured Products",
          "itemListElement": [
            {% assign featured_products = collections.featured.products | default: collections.all.products %}
            {% for product in featured_products limit: 8 %}
              {
                "@type": "Product",
                "position": "{{ forloop.index }}",
                "name": "{{ product.title | escape }}",
                "url": "{{ shop.url }}{{ product.url }}",
                "image": "{{ product.featured_image | image_url: width: 400 }}",
                "offers": {
                  "@type": "Offer",
                  "price": "{{ product.price | money_without_currency }}",
                  "priceCurrency": "{{ cart.currency.iso_code }}"
                }
              }{% unless forloop.last %},{% endunless %}
            {% endfor %}
          ]
        }
      }
    {% endif %}
    ,{
      "@type": "BreadcrumbList",
      "@id": "{{ shop.url }}{{ request.path }}#breadcrumb",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "{{ shop.url }}"
        }
        {% if current_page_type == 'collection' and current_collection %}
          ,{
            "@type": "ListItem",
            "position": 2,
            "name": "{{ current_collection.title | escape }}",
            "item": "{{ shop.url }}{{ current_collection.url }}"
          }
        {% endif %}
        {% if current_page_type == 'product' and current_product %}
          {% if current_collection %}
            ,{
              "@type": "ListItem",
              "position": 2,
              "name": "{{ current_collection.title | escape }}",
              "item": "{{ shop.url }}{{ current_collection.url }}"
            }
            ,{
              "@type": "ListItem",
              "position": 3,
              "name": "{{ current_product.title | escape }}",
              "item": "{{ shop.url }}{{ current_product.url }}"
            }
          {% else %}
            ,{
              "@type": "ListItem",
              "position": 2,
              "name": "{{ current_product.title | escape }}",
              "item": "{{ shop.url }}{{ current_product.url }}"
            }
          {% endif %}
        {% endif %}
        {% if current_page_type == 'article' and blog and article %}
          ,{
            "@type": "ListItem",
            "position": 2,
            "name": "{{ blog.title | escape }}",
            "item": "{{ shop.url }}{{ blog.url }}"
          }
          ,{
            "@type": "ListItem",
            "position": 3,
            "name": "{{ article.title | escape }}",
            "item": "{{ shop.url }}{{ blog.url }}/{{ article.handle }}"
          }
        {% endif %}
      ]
    }
    {% comment %} FAQ Schema for pages with FAQ content {% endcomment %}
    {% if page.handle == 'faq' or page.content contains 'question' %}
      ,{
        "@type": "FAQPage",
        "@id": "{{ shop.url }}{{ page.url }}#faq",
        "mainEntity": [
          {
            "@type": "Question",
            "name": "What is included in the membership?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Our membership includes access to premium digital resources, exclusive tutorials, community support, and monthly bonus content."
            }
          },
          {
            "@type": "Question",
            "name": "How do I access my digital downloads?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "After purchase, you'll receive an email with download links. You can also access all your purchases through your member dashboard."
            }
          },
          {
            "@type": "Question",
            "name": "What is your refund policy?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "We offer a 30-day money-back guarantee. If you're not completely satisfied, contact us for a full refund."
            }
          },
          {
            "@type": "Question",
            "name": "Do you offer customer support?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes! We provide 24/7 customer support through email and live chat. Our team is here to help with any questions."
            }
          }
        ]
      }
    {% endif %}
    {% comment %} Local Business Schema for contact/about pages {% endcomment %}
    {% if page.handle == 'about' or page.handle == 'contact' %}
      ,{
        "@type": "LocalBusiness",
        "@id": "{{ shop.url }}#localbusiness",
        "name": "{{ shop.name | escape }}",
        "description": "{{ shop.description | escape }}",
        "url": "{{ shop.url }}",
        "telephone": "******-FTAC-HELP",
        "email": "<EMAIL>",
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "US",
          "addressRegion": "CA",
          "addressLocality": "San Francisco"
        },
        "openingHours": "Mo-Fr 09:00-17:00",
        "priceRange": "$",
        "acceptsReservations": false,
        "currenciesAccepted": "USD",
        "paymentAccepted": "Credit Card, PayPal, Apple Pay, Google Pay"
      }
    {% endif %}
  ]
}
</script>

{% comment %} Additional Product Schema for Rich Snippets {% endcomment %}
{% if current_page_type == 'product' and current_product %}
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "{{ current_product.title | escape }}",
    "description": "{{ current_product.description | strip_html | truncate: 500 | escape }}",
    "image": [
      {% for image in current_product.images %}
        "{{ image | image_url: width: 800 }}"{% unless forloop.last %},{% endunless %}
      {% endfor %}
    ],
    "brand": {
      "@type": "Brand",
      "name": "{{ current_product.vendor | default: shop.name | escape }}"
    },
    "offers": {
      "@type": "Offer",
      "url": "{{ shop.url }}{{ current_product.url }}",
      "priceCurrency": "{{ cart.currency.iso_code }}",
      "price": "{{ current_product.selected_or_first_available_variant.price | money_without_currency }}",
      "availability": "{% if current_product.selected_or_first_available_variant.available %}https://schema.org/InStock{% else %}https://schema.org/OutOfStock{% endif %}",
      "seller": {
        "@type": "Organization",
        "name": "{{ shop.name | escape }}"
      }
    }
    {% if current_product.metafields.custom.rating %}
      ,"aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "{{ current_product.metafields.custom.rating }}",
        "reviewCount": "{{ current_product.metafields.custom.review_count | default: 1 }}",
        "bestRating": "5",
        "worstRating": "1"
      }
    {% endif %}
  }
  </script>
{% endif %}

{% comment %} Course/Educational Content Schema {% endcomment %}
{% if current_product.type == 'Course' or current_product.tags contains 'course' %}
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Course",
    "name": "{{ current_product.title | escape }}",
    "description": "{{ current_product.description | strip_html | truncate: 500 | escape }}",
    "provider": {
      "@type": "Organization",
      "name": "{{ shop.name | escape }}",
      "url": "{{ shop.url }}"
    },
    "offers": {
      "@type": "Offer",
      "price": "{{ current_product.price | money_without_currency }}",
      "priceCurrency": "{{ cart.currency.iso_code }}",
      "availability": "https://schema.org/InStock"
    },
    "courseMode": "online",
    "educationalLevel": "beginner",
    "teaches": "{{ current_product.metafields.custom.skills | default: 'Creative skills and techniques' }}"
  }
  </script>
{% endif %}
