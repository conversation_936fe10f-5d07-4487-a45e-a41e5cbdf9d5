{% comment %}
  FTAC Product-Level Authentication Logic

  This snippet handles product-specific access code validation and
  customer account integration for the new product-level access system.

  Usage:
  {% render 'ftac-auth-logic', action: 'validate_product_code', customer: customer, product_handle: product_handle, access_code: access_code %}
  {% render 'ftac-auth-logic', action: 'check_customer_access', customer: customer %}
{% endcomment %}

{% assign action = action | default: 'check_customer_access' %}
{% assign customer = customer %}
{% assign product_handle = product_handle %}
{% assign access_code = access_code | upcase | strip %}
{% assign auth_result = 'invalid' %}
{% assign auth_message = '' %}
{% assign validation_result = false %}

{% case action %}
  {% when 'validate_product_code' %}
    {% comment %} Validate product-specific access code {% endcomment %}
    {% assign code_valid_format = false %}
    {% if access_code contains 'FTAC-' and access_code.size == 19 %}
      {% assign code_parts = access_code | split: '-' %}
      {% if code_parts.size == 4 and code_parts[0] == 'FTAC' %}
        {% assign code_valid_format = true %}
      {% endif %}
    {% endif %}

    {% unless code_valid_format %}
      {% assign auth_result = 'invalid_format' %}
      {% assign auth_message = 'Please enter a valid access code in the format FTAC-XXXX-YYYY-ZZZZ' %}
    {% else %}
      {% comment %} Validate that the code matches the product {% endcomment %}
      {% render 'ftac-product-access', validate_access_code: true, access_code: access_code, product_handle: product_handle %}
      {% assign validation_result = ftac_validation_result %}

      {% if validation_result %}
        {% assign auth_result = 'valid' %}
        {% assign auth_message = 'Access code is valid for this product' %}
      {% else %}
        {% assign auth_result = 'invalid_product' %}
        {% assign auth_message = 'This access code is not valid for this product' %}
      {% endif %}
    {% endunless %}

  {% when 'check_customer_access' %}
    {% comment %} Check if customer has access to any products {% endcomment %}
    {% if customer %}
      {% render 'ftac-product-access', action: 'list_products', customer: customer %}
      {% assign accessible_products = ftac_accessible_products %}

      {% if accessible_products != '' %}
        {% assign auth_result = 'has_access' %}
        {% assign auth_message = 'Customer has product access' %}
      {% else %}
        {% assign auth_result = 'no_access' %}
        {% assign auth_message = 'Customer has no product access codes' %}
      {% endif %}
    {% else %}
      {% assign auth_result = 'not_logged_in' %}
      {% assign auth_message = 'Customer not logged in' %}
    {% endif %}

  {% when 'check_product_access' %}
    {% comment %} Check if customer has access to a specific product {% endcomment %}
    {% if customer and product_handle %}
      {% render 'ftac-product-access', action: 'check_access', customer: customer, product_handle: product_handle %}
      {% assign has_access = ftac_access_result %}

      {% if has_access %}
        {% assign auth_result = 'access_granted' %}
        {% assign auth_message = 'Customer has access to this product' %}
      {% else %}
        {% assign auth_result = 'access_denied' %}
        {% assign auth_message = 'Customer does not have access to this product' %}
      {% endif %}
    {% else %}
      {% assign auth_result = 'missing_data' %}
      {% assign auth_message = 'Customer or product information missing' %}
    {% endif %}

  {% else %}
    {% assign auth_result = 'unknown_action' %}
    {% assign auth_message = 'Unknown authentication action' %}
{% endcase %}

{% comment %} Output authentication result {% endcomment %}
<div class="ftac-auth-result" data-result="{{ auth_result }}" data-message="{{ auth_message }}">
  {% case auth_result %}
    {% when 'valid' %}
      <div class="ftac-auth-success">
        <p>{{ auth_message }}</p>
        <p>You can now access this product content!</p>
      </div>

    {% when 'access_granted' %}
      <div class="ftac-auth-success">
        <p>{{ auth_message }}</p>
      </div>

    {% when 'has_access' %}
      <div class="ftac-auth-success">
        <p>{{ auth_message }}</p>
      </div>

    {% when 'invalid_format' %}
      <div class="ftac-auth-error">
        <p>{{ auth_message }}</p>
        <p>Your access code should look like: <strong>FTAC-ABCD-1234-EFGH</strong></p>
      </div>

    {% when 'invalid_product' %}
      <div class="ftac-auth-error">
        <p>{{ auth_message }}</p>
        <p>Please check your welcome email for the correct access code for this product.</p>
        <p><a href="/pages/contact-support">Need help? Contact Sarah →</a></p>
      </div>

    {% when 'access_denied' %}
      <div class="ftac-auth-error">
        <p>{{ auth_message }}</p>
        <p>You need to enter an access code to view this content.</p>
      </div>

    {% when 'no_access' %}
      <div class="ftac-auth-info">
        <p>{{ auth_message }}</p>
        <p>Enter access codes for your purchased products to unlock premium content.</p>
      </div>

    {% when 'not_logged_in' %}
      <div class="ftac-auth-error">
        <p>Please sign in to access your content.</p>
        <p><a href="/pages/member-access">Sign In →</a></p>
      </div>

    {% else %}
      <div class="ftac-auth-error">
        <p>{{ auth_message | default: 'Unable to verify access. Please try again or contact support.' }}</p>
        <p><a href="/pages/contact-support">Contact Sarah for help →</a></p>
      </div>
  {% endcase %}
</div>

{% comment %}
  Product-Level Access Control System

  New customer data structure for product-specific access:

  1. Customer purchases product on Etsy
  2. Sarah receives order notification
  3. Sarah updates customer metafields with product-specific access code:
     - ftac.product_access_codes: JSON string containing product access data

  Example customer metafield structure:

  ftac.product_access_codes:
  {
    "future-tech-foundations": {
      "access_code": "FTAC-FOUN-ABCD-1234",
      "granted_date": "2024-01-15",
      "etsy_order_id": "**********"
    },
    "advanced-tech-mastery": {
      "access_code": "FTAC-MAST-WXYZ-5678",
      "granted_date": "2024-01-20",
      "etsy_order_id": "**********"
    }
  }

  Customer tags (optional):
  - "ftac_customer" (indicates they have an FTAC account)
{% endcomment %}

<style>
  .ftac-auth-result {
    margin: var(--ftac-space-4) 0;
  }
  
  .ftac-auth-success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: var(--ftac-learning-green);
    padding: var(--ftac-space-4);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-secondary);
  }
  
  .ftac-auth-error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: var(--ftac-space-4);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-secondary);
  }

  .ftac-auth-info {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    color: var(--ftac-academy-blue);
    padding: var(--ftac-space-4);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-secondary);
  }

  .ftac-auth-error a,
  .ftac-auth-info a {
    color: var(--ftac-academy-blue);
    text-decoration: none;
    font-weight: var(--ftac-font-medium);
  }

  .ftac-auth-error a:hover,
  .ftac-auth-info a:hover {
    text-decoration: underline;
  }
</style>
