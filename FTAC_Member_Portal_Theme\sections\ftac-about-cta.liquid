{{ 'base.css' | asset_url | stylesheet_tag }}
{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-about-cta {
    padding: var(--ftac-space-20) 0;
    background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green));
    color: white;
  }
  
  .ftac-about-cta__container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
    text-align: center;
  }
  
  .ftac-about-cta__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-4xl);
    font-weight: var(--ftac-font-bold);
    line-height: 1.1;
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-about-cta__description {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-xl);
    line-height: 1.6;
    margin-bottom: var(--ftac-space-10);
    opacity: 0.95;
  }
  
  .ftac-about-cta__buttons {
    display: flex;
    flex-direction: column;
    gap: var(--ftac-space-4);
    align-items: center;
  }
  
  .ftac-about-cta__primary {
    background-color: white;
    color: var(--ftac-academy-blue);
    padding: var(--ftac-space-4) var(--ftac-space-8);
    border-radius: var(--ftac-radius-xl);
    text-decoration: none;
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-semibold);
    font-size: var(--ftac-text-lg);
    transition: all 0.3s ease;
    box-shadow: var(--ftac-shadow-lg);
    min-width: 280px;
    text-align: center;
  }
  
  .ftac-about-cta__primary:hover {
    background-color: var(--ftac-warm-cream);
    transform: translateY(-2px);
    box-shadow: var(--ftac-shadow-xl);
  }
  
  .ftac-about-cta__secondary {
    color: white;
    text-decoration: none;
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    opacity: 0.9;
    transition: opacity 0.2s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 2px;
  }
  
  .ftac-about-cta__secondary:hover {
    opacity: 1;
    border-bottom-color: white;
  }
  
  .ftac-about-cta__trust-indicators {
    display: flex;
    justify-content: center;
    gap: var(--ftac-space-8);
    margin-top: var(--ftac-space-10);
    flex-wrap: wrap;
  }
  
  .ftac-about-cta__trust-item {
    display: flex;
    align-items: center;
    gap: var(--ftac-space-2);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    opacity: 0.9;
  }
  
  .ftac-about-cta__trust-icon {
    width: 20px;
    height: 20px;
  }
  
  /* Desktop Layout */
  @media (min-width: 768px) {
    .ftac-about-cta__buttons {
      flex-direction: row;
      justify-content: center;
    }
  }
  
  @media (min-width: 1024px) {
    .ftac-about-cta__title {
      font-size: var(--ftac-text-5xl);
    }
  }
</style>

<section class="ftac-about-cta">
  <div class="ftac-about-cta__container">
    <h2 class="ftac-about-cta__title">
      Ready to Start Your Journey?
    </h2>
    
    <p class="ftac-about-cta__description">
      {{ section.settings.description | default: "Join thousands of learners who have transformed their lives with Sarah's personal guidance and premium resources. Your success story starts today." }}
    </p>
    
    <div class="ftac-about-cta__buttons">
      <a href="{{ section.settings.primary_link | default: 'https://etsy.com/shop/futuretechacademyclub' }}" class="ftac-about-cta__primary" target="_blank" rel="noopener">
        {{ section.settings.primary_text | default: "Shop Premium Bundles" }}
      </a>
      <a href="{{ section.settings.secondary_link | default: '/pages/contact-support' }}" class="ftac-about-cta__secondary">
        {{ section.settings.secondary_text | default: "Have questions? Contact Sarah →" }}
      </a>
    </div>
    
    <div class="ftac-about-cta__trust-indicators">
      <div class="ftac-about-cta__trust-item">
        <svg class="ftac-about-cta__trust-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
        <span>Secure Purchase</span>
      </div>
      <div class="ftac-about-cta__trust-item">
        <svg class="ftac-about-cta__trust-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
        <span>Instant Access</span>
      </div>
      <div class="ftac-about-cta__trust-item">
        <svg class="ftac-about-cta__trust-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
        <span>Personal Support</span>
      </div>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "FTAC About CTA",
  "settings": [
    {
      "type": "header",
      "content": "Call to Action Settings"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description Text",
      "default": "Join thousands of learners who have transformed their lives with Sarah's personal guidance and premium resources. Your success story starts today."
    },
    {
      "type": "text",
      "id": "primary_text",
      "label": "Primary Button Text",
      "default": "Shop Premium Bundles"
    },
    {
      "type": "url",
      "id": "primary_link",
      "label": "Primary Button Link",
      "default": "https://etsy.com/shop/futuretechacademyclub"
    },
    {
      "type": "text",
      "id": "secondary_text",
      "label": "Secondary Link Text",
      "default": "Have questions? Contact Sarah →"
    },
    {
      "type": "url",
      "id": "secondary_link",
      "label": "Secondary Link URL",
      "default": "/pages/contact-support"
    }
  ],
  "presets": [
    {
      "name": "FTAC About CTA"
    }
  ]
}
{% endschema %}
