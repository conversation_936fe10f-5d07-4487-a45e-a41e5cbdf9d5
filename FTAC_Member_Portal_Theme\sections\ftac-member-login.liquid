{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

{% comment %} Enhanced Authentication Flow - Check if customer is logged in {% endcomment %}
{% if customer %}
  <script>
    console.log('FTAC: Customer already logged in, redirecting to member dashboard');
    window.location.href = '/pages/member-dashboard';
  </script>

  <!-- Fallback content in case JavaScript is disabled -->
  <div style="padding: 4rem; text-align: center; background: var(--ftac-cream-base);">
    <h2 style="font-family: var(--ftac-font-display); color: var(--ftac-charcoal-text); margin-bottom: 1rem;">
      Welcome back, {{ customer.first_name }}!
    </h2>
    <p style="font-family: var(--ftac-font-primary); color: var(--ftac-charcoal-text); margin-bottom: 2rem;">
      You're already logged in. Taking you to your member dashboard.
    </p>
    <a href="/pages/member-dashboard" style="background: var(--ftac-blush-primary); color: white; padding: 1rem 2rem; border-radius: 25px; text-decoration: none; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-semibold);">
      Go to Dashboard
    </a>
  </div>
{% else %}

{% comment %} Authentication Required - Show login/signup options {% endcomment %}

<style>
  .ftac-member-login {
    padding: 6rem 0;
    background: var(--ftac-cream-base);
    background-image:
      radial-gradient(circle at 1px 1px, rgba(var(--ftac-charcoal-text-rgb), 0.03) 1px, transparent 0);
    background-size: 30px 30px;
    min-height: 70vh;
    display: flex;
    align-items: center;
    position: relative;
  }

  .ftac-member-login::before {
    content: '🌿';
    position: absolute;
    top: 15%;
    left: 10%;
    font-size: 2rem;
    opacity: 0.2;
  }

  .ftac-member-login__container {
    max-width: 650px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-8);
  }

  .ftac-auth-prompt {
    background: white;
    border-radius: var(--ftac-radius-2xl);
    padding: var(--ftac-space-12);
    box-shadow: var(--ftac-shadow-xl);
    text-align: center;
    margin-bottom: var(--ftac-space-8);
  }

  .ftac-auth-prompt__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-3xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }

  .ftac-auth-prompt__subtitle {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-charcoal);
    opacity: 0.8;
    margin-bottom: var(--ftac-space-8);
    line-height: 1.6;
  }

  .ftac-auth-options {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ftac-space-6);
    margin-bottom: var(--ftac-space-8);
  }

  .ftac-auth-option {
    background: var(--ftac-warm-cream);
    border: 2px solid transparent;
    border-radius: var(--ftac-radius-xl);
    padding: var(--ftac-space-8);
    text-decoration: none;
    transition: all 0.3s ease;
    display: block;
  }

  .ftac-auth-option:hover {
    border-color: var(--ftac-academy-blue);
    transform: translateY(-2px);
    box-shadow: var(--ftac-shadow-lg);
  }

  .ftac-auth-option__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-2);
  }

  .ftac-auth-option__description {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
    opacity: 0.7;
    line-height: 1.5;
  }

  .ftac-auth-divider {
    display: flex;
    align-items: center;
    margin: var(--ftac-space-6) 0;
    color: var(--ftac-charcoal);
    opacity: 0.5;
  }

  .ftac-auth-divider::before,
  .ftac-auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: currentColor;
  }

  .ftac-auth-divider span {
    padding: 0 var(--ftac-space-4);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
  }

  @media (min-width: 768px) {
    .ftac-auth-options {
      grid-template-columns: 1fr 1fr;
    }
  }

  .ftac-member-login__header {
    text-align: center;
    margin-bottom: var(--ftac-space-16);
  }

  .ftac-member-login__title {
    font-family: var(--ftac-font-handwritten);
    font-size: var(--ftac-text-5xl);
    font-weight: 700;
    color: var(--ftac-warm-taupe);
    margin-bottom: var(--ftac-space-6);
    line-height: 1.2;
  }

  .ftac-member-login__title-highlight {
    color: var(--ftac-dusty-rose);
  }

  .ftac-member-login__subtitle {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-warm-taupe);
    line-height: 1.7;
    margin-bottom: var(--ftac-space-10);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }

  .ftac-member-login__form {
    background-color: white;
    padding: var(--ftac-space-12);
    border-radius: var(--ftac-radius-2xl);
    box-shadow: 0 8px 30px rgba(var(--ftac-warm-taupe-rgb), 0.15);
    border: 1px solid rgba(var(--ftac-dusty-rose-rgb), 0.1);
  }
  
  .ftac-member-login__field {
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-member-login__label {
    display: block;
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-2);
  }
  
  .ftac-member-login__input {
    width: 100%;
    padding: var(--ftac-space-4);
    border: 2px solid rgba(var(--ftac-charcoal-rgb), 0.2);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-charcoal);
    background-color: white;
    transition: all 0.2s ease;
  }
  
  .ftac-member-login__input:focus {
    outline: none;
    border-color: var(--ftac-academy-blue);
    box-shadow: 0 0 0 3px rgba(var(--ftac-academy-blue-rgb), 0.1);
  }
  
  .ftac-member-login__input--access-code {
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
    text-transform: uppercase;
  }
  
  .ftac-member-login__help-text {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    color: var(--ftac-charcoal);
    opacity: 0.7;
    margin-top: var(--ftac-space-2);
  }
  
  .ftac-member-login__submit {
    width: 100%;
    background-color: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-4) var(--ftac-space-8);
    border: none;
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--ftac-shadow-lg);
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-member-login__submit:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
    box-shadow: var(--ftac-shadow-xl);
  }
  
  .ftac-member-login__submit:disabled {
    background-color: rgba(var(--ftac-charcoal-rgb), 0.3);
    cursor: not-allowed;
    transform: none;
  }
  
  .ftac-member-login__error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: var(--ftac-space-4);
    border-radius: var(--ftac-radius-lg);
    margin-bottom: var(--ftac-space-6);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
  }
  
  .ftac-member-login__success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: var(--ftac-learning-green);
    padding: var(--ftac-space-4);
    border-radius: var(--ftac-radius-lg);
    margin-bottom: var(--ftac-space-6);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
  }
  
  .ftac-member-login__support {
    text-align: center;
    margin-top: var(--ftac-space-8);
    padding-top: var(--ftac-space-6);
    border-top: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
  }
  
  .ftac-member-login__support-text {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    color: var(--ftac-charcoal);
    opacity: 0.7;
    margin-bottom: var(--ftac-space-3);
  }
  
  .ftac-member-login__support-link {
    color: var(--ftac-academy-blue);
    text-decoration: none;
    font-weight: var(--ftac-font-medium);
    transition: opacity 0.2s ease;
  }
  
  .ftac-member-login__support-link:hover {
    opacity: 0.8;
    text-decoration: underline;
  }

  /* Tab Interface */
  .ftac-member-login__tabs {
    display: flex;
    margin-bottom: var(--ftac-space-6);
    border-bottom: 2px solid rgba(var(--ftac-charcoal-rgb), 0.1);
  }

  .ftac-member-login__tab {
    flex: 1;
    padding: var(--ftac-space-4) var(--ftac-space-6);
    background: none;
    border: none;
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
    opacity: 0.6;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
  }

  .ftac-member-login__tab--active {
    opacity: 1;
    color: var(--ftac-academy-blue);
    border-bottom-color: var(--ftac-academy-blue);
  }

  .ftac-member-login__tab:hover {
    opacity: 1;
  }

  .ftac-member-login__form-content {
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  /* Loading state */
  .ftac-member-login__loading {
    display: none;
    align-items: center;
    justify-content: center;
    gap: var(--ftac-space-2);
    color: var(--ftac-academy-blue);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    margin-top: var(--ftac-space-4);
  }

  .ftac-member-login__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.2);
    border-top: 2px solid var(--ftac-academy-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Desktop Layout */
  @media (min-width: 1024px) {
    .ftac-member-login__title {
      font-size: var(--ftac-text-5xl);
    }
  }
</style>

<section class="ftac-member-login">
  <div class="ftac-member-login__container">
    <div class="ftac-member-login__header">
      <h1 class="ftac-member-login__title">
        Welcome to Your <span class="ftac-member-login__title-highlight">Learning Journey</span>
      </h1>
      <p class="ftac-member-login__subtitle">
        {{ section.settings.subtitle | default: "Sign in to access your learning dashboard. Create a free account to get started, then unlock premium content with your access codes." }}
      </p>
    </div>
    
    <!-- Check if customer is already logged in -->
    {% if customer %}
      <div class="ftac-member-login__form">
        <div class="ftac-member-login__success">
          <p>Welcome back, {{ customer.first_name | default: customer.email }}! You're already signed in.</p>
        </div>
        <a href="/pages/member-dashboard" class="ftac-member-login__submit" style="text-decoration: none; text-align: center; display: block;">
          Go to Your Dashboard
        </a>
      </div>
    {% else %}
      <!-- Authentication Required Prompt -->
      <div class="ftac-auth-prompt">
        <h2 class="ftac-auth-prompt__title">🔐 Member Access Required</h2>
        <p class="ftac-auth-prompt__subtitle">
          To access your learning content and member dashboard, please sign in to your account or create a free account to get started.
        </p>

        <div class="ftac-auth-options">
          <a href="{{ routes.account_login_url }}?return_url=/pages/member-dashboard" class="ftac-auth-option">
            <div class="ftac-auth-option__title">🚀 I Have an Account</div>
            <div class="ftac-auth-option__description">Sign in to access your dashboard and premium content</div>
          </a>

          <a href="{{ routes.account_register_url }}?return_url=/pages/member-dashboard" class="ftac-auth-option">
            <div class="ftac-auth-option__title">✨ Create Free Account</div>
            <div class="ftac-auth-option__description">Get started with a free membership account</div>
          </a>
        </div>

        <div class="ftac-auth-divider">
          <span>Choose your option above</span>
        </div>

        <p style="text-align: center; font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); opacity: 0.7; font-size: var(--ftac-text-sm);">
          You'll be taken to a secure login page, then redirected back to your member dashboard.
        </p>
      </div>
    {% endif %}

      <!-- Support Section -->
      <div class="ftac-member-login__support">
        <p class="ftac-member-login__support-text">Need help accessing your content?</p>
        <a href="/pages/contact-support" class="ftac-member-login__support-link">
          Contact Sarah for Personal Support →
        </a>
      </div>
  </div>
</section>



{% endif %}

{% schema %}
{
  "name": "FTAC Member Login",
  "settings": [
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Subtitle Text",
      "default": "Sign in to access your learning dashboard. Create a free account to get started, then unlock premium content with your access codes."
    }
  ],
  "presets": [
    {
      "name": "FTAC Member Login"
    }
  ]
}
{% endschema %}
