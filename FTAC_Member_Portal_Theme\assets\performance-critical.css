/* FTAC Critical CSS - Above the fold styles for instant loading */

/* Critical Variables */
:root {
  --ftac-blush-primary: #FFC8DD;
  --ftac-sage-accent: #80ED99;
  --ftac-cream-base: #FFF8E7;
  --ftac-peach-glow: #FFAFCC;
  --ftac-dusty-trust: #A2D2FF;
  --ftac-charcoal-text: #2C2C2C;
  --ftac-rose-gold: #E8B4B8;
}

/* Critical Typography */
@font-display: swap;
body {
  font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--ftac-charcoal-text);
  background: var(--ftac-cream-base);
  line-height: 1.6;
  margin: 0;
  padding: 0;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', Georgia, serif;
  font-weight: 700;
  line-height: 1.3;
  margin: 0 0 1rem 0;
}

/* Critical Header Styles */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(255, 200, 221, 0.2);
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 1rem 0;
}

.header__wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header__heading-logo {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--ftac-blush-primary);
  text-decoration: none;
}

/* Critical Hero Styles */
.ftac-hero {
  background: linear-gradient(135deg, var(--ftac-cream-base) 0%, #FFFEF7 50%, var(--ftac-cream-base) 100%);
  padding: 4rem 2rem;
  text-align: center;
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ftac-hero-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.ftac-hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--ftac-charcoal-text);
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--ftac-charcoal-text), var(--ftac-blush-primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.ftac-hero-subtitle {
  font-size: 1.25rem;
  color: var(--ftac-charcoal-text);
  opacity: 0.8;
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* Critical Button Styles */
.ftac-luxury-btn {
  background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
  color: white;
  border: none;
  border-radius: 25px;
  padding: 1rem 2rem;
  font-family: 'Open Sans', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(255, 200, 221, 0.3);
  position: relative;
  overflow: hidden;
}

.ftac-luxury-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 200, 221, 0.4);
}

/* Critical Layout */
.page-width {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 3rem 0;
}

/* Critical Grid */
.grid {
  display: grid;
  gap: 2rem;
}

.grid--1-col {
  grid-template-columns: 1fr;
}

.grid--2-col {
  grid-template-columns: repeat(2, 1fr);
}

.grid--3-col {
  grid-template-columns: repeat(3, 1fr);
}

/* Critical Card Styles */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  border: 1px solid rgba(255, 200, 221, 0.2);
  box-shadow: 0 10px 30px rgba(255, 200, 221, 0.1);
  overflow: hidden;
  transition: all 0.4s ease;
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(255, 200, 221, 0.2);
}

.card__media {
  position: relative;
  overflow: hidden;
}

.card__media img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.4s ease;
}

.card:hover .card__media img {
  transform: scale(1.05);
}

.card__content {
  padding: 1.5rem;
}

.card__heading {
  font-family: 'Playfair Display', serif;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--ftac-charcoal-text);
  margin-bottom: 0.5rem;
}

/* Critical Mobile Styles */
@media screen and (max-width: 768px) {
  .header__wrapper {
    padding: 0 1rem;
  }
  
  .ftac-hero {
    padding: 2rem 1rem;
    min-height: 50vh;
  }
  
  .ftac-hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
  }
  
  .ftac-hero-subtitle {
    font-size: 1rem;
  }
  
  .page-width {
    padding: 0 1rem;
  }
  
  .grid--2-col,
  .grid--3-col {
    grid-template-columns: 1fr;
  }
  
  .ftac-luxury-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Critical Loading States */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--ftac-blush-primary);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Critical Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--ftac-blush-primary);
  outline-offset: 2px;
}

/* Critical Performance Optimizations */
img {
  max-width: 100%;
  height: auto;
}

.lazy {
  opacity: 0;
  transition: opacity 0.3s;
}

.lazy.loaded {
  opacity: 1;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
