{% comment %}
  FTAC Dashboard Access Code Input Component
  
  This snippet provides a prominent access code input for the member dashboard
  that allows customers to unlock any product by entering their access code.
  
  Parameters:
  - customer: Current customer object (required)
  
  Usage:
  {% render 'ftac-dashboard-access-input', customer: customer %}
{% endcomment %}

{% assign customer = customer %}

{% comment %} Only show if customer is logged in {% endcomment %}
{% if customer %}
  <div class="ftac-dashboard-access-input">
    <div class="ftac-dashboard-access-input__container">
      <div class="ftac-dashboard-access-input__header">
        <div class="ftac-dashboard-access-input__icon">
          <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3a1 1 0 011-1h2.586l6.243-6.243A6 6 0 0121 9z"></path>
          </svg>
        </div>
        <div class="ftac-dashboard-access-input__text">
          <h2 class="ftac-dashboard-access-input__title">Unlock New Content</h2>
          <p class="ftac-dashboard-access-input__subtitle">
            Enter your access code to unlock premium courses and resources
          </p>
        </div>
      </div>
      
      <form class="ftac-dashboard-access-input__form" id="dashboard-access-code-form">
        <input type="hidden" name="customer_id" value="{{ customer.id }}">
        
        <div class="ftac-dashboard-access-input__field-group">
          <div class="ftac-dashboard-access-input__field">
            <label for="dashboard-access-code" class="ftac-dashboard-access-input__label">
              Access Code
            </label>
            <input 
              type="text" 
              id="dashboard-access-code" 
              name="access_code" 
              class="ftac-dashboard-access-input__input" 
              placeholder="FTAC-XXXX-YYYY-ZZZZ"
              pattern="[Ff][Tt][Aa][Cc]-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}"
              maxlength="19"
              required
            >
            <p class="ftac-dashboard-access-input__help">
              Found in your welcome email after purchasing
            </p>
          </div>
          
          <button type="submit" class="ftac-dashboard-access-input__button">
            <svg class="ftac-dashboard-access-input__button-icon" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-3a1 1 0 011-1h2.586l6.243-6.243A6 6 0 0121 9z"></path>
            </svg>
            Unlock Content
          </button>
        </div>
        
        <div class="ftac-dashboard-access-input__messages">
          <div class="ftac-dashboard-access-input__error" id="dashboard-access-error" style="display: none;"></div>
          <div class="ftac-dashboard-access-input__success" id="dashboard-access-success" style="display: none;"></div>
          <div class="ftac-dashboard-access-input__loading" id="dashboard-access-loading" style="display: none;">
            <div class="ftac-dashboard-access-input__spinner"></div>
            <span>Verifying access code...</span>
          </div>
        </div>
      </form>
      
      <div class="ftac-dashboard-access-input__support">
        <p class="ftac-dashboard-access-input__support-text">
          Need help finding your access code? 
          <a href="/pages/contact-support" class="ftac-dashboard-access-input__support-link">
            Contact Sarah
          </a>
        </p>
      </div>
    </div>
  </div>
{% endif %}

<style>
  .ftac-dashboard-access-input {
    background: linear-gradient(135deg, var(--ftac-academy-blue), rgba(var(--ftac-academy-blue-rgb), 0.8));
    border-radius: var(--ftac-radius-xl);
    padding: var(--ftac-space-8);
    margin-bottom: var(--ftac-space-8);
    color: white;
    position: relative;
    overflow: hidden;
  }
  
  .ftac-dashboard-access-input::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
  }
  
  .ftac-dashboard-access-input__container {
    position: relative;
    z-index: 1;
  }
  
  .ftac-dashboard-access-input__header {
    display: flex;
    align-items: center;
    gap: var(--ftac-space-4);
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-dashboard-access-input__icon {
    width: 48px;
    height: 48px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: var(--ftac-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
  }
  
  .ftac-dashboard-access-input__text {
    flex-grow: 1;
  }
  
  .ftac-dashboard-access-input__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-2xl);
    font-weight: var(--ftac-font-bold);
    margin-bottom: var(--ftac-space-2);
    color: white;
  }
  
  .ftac-dashboard-access-input__subtitle {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    opacity: 0.9;
    margin: 0;
  }
  
  .ftac-dashboard-access-input__form {
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-dashboard-access-input__field-group {
    display: flex;
    gap: var(--ftac-space-4);
    align-items: end;
  }
  
  .ftac-dashboard-access-input__field {
    flex-grow: 1;
  }
  
  .ftac-dashboard-access-input__label {
    display: block;
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-medium);
    color: white;
    margin-bottom: var(--ftac-space-2);
  }
  
  .ftac-dashboard-access-input__input {
    width: 100%;
    padding: var(--ftac-space-4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--ftac-radius-lg);
    font-family: 'Courier New', monospace;
    font-size: var(--ftac-text-lg);
    letter-spacing: 1px;
    text-transform: uppercase;
    text-align: center;
    color: var(--ftac-charcoal);
    background-color: white;
    transition: all 0.2s ease;
  }
  
  .ftac-dashboard-access-input__input:focus {
    outline: none;
    border-color: white;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
  }
  
  .ftac-dashboard-access-input__help {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    color: white;
    opacity: 0.8;
    margin-top: var(--ftac-space-2);
    margin-bottom: 0;
  }
  
  .ftac-dashboard-access-input__button {
    background-color: white;
    color: var(--ftac-academy-blue);
    border: none;
    border-radius: var(--ftac-radius-lg);
    padding: var(--ftac-space-4) var(--ftac-space-6);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-semibold);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--ftac-space-2);
    white-space: nowrap;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .ftac-dashboard-access-input__button:hover {
    background-color: rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  }
  
  .ftac-dashboard-access-input__button:disabled {
    background-color: rgba(255, 255, 255, 0.7);
    cursor: not-allowed;
    transform: none;
  }
  
  .ftac-dashboard-access-input__button-icon {
    width: 20px;
    height: 20px;
  }
  
  .ftac-dashboard-access-input__messages {
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-dashboard-access-input__error {
    background-color: rgba(220, 38, 38, 0.1);
    border: 1px solid rgba(220, 38, 38, 0.3);
    color: #fecaca;
    padding: var(--ftac-space-3);
    border-radius: var(--ftac-radius-lg);
    margin-bottom: var(--ftac-space-3);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
  }
  
  .ftac-dashboard-access-input__success {
    background-color: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #bbf7d0;
    padding: var(--ftac-space-3);
    border-radius: var(--ftac-radius-lg);
    margin-bottom: var(--ftac-space-3);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
  }
  
  .ftac-dashboard-access-input__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ftac-space-2);
    color: white;
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    opacity: 0.9;
  }
  
  .ftac-dashboard-access-input__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .ftac-dashboard-access-input__support {
    text-align: center;
  }
  
  .ftac-dashboard-access-input__support-text {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    color: white;
    opacity: 0.8;
    margin: 0;
  }
  
  .ftac-dashboard-access-input__support-link {
    color: white;
    text-decoration: none;
    font-weight: var(--ftac-font-medium);
    border-bottom: 1px solid rgba(255, 255, 255, 0.5);
  }
  
  .ftac-dashboard-access-input__support-link:hover {
    border-bottom-color: white;
  }
  
  /* Mobile responsive */
  @media (max-width: 767px) {
    .ftac-dashboard-access-input__field-group {
      flex-direction: column;
      gap: var(--ftac-space-4);
    }
    
    .ftac-dashboard-access-input__button {
      width: 100%;
      justify-content: center;
    }
    
    .ftac-dashboard-access-input__header {
      text-align: center;
      flex-direction: column;
      gap: var(--ftac-space-3);
    }
    
    .ftac-dashboard-access-input__title {
      font-size: var(--ftac-text-xl);
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const dashboardForm = document.getElementById('dashboard-access-code-form');

  if (dashboardForm) {
    const accessCodeInput = dashboardForm.querySelector('input[name="access_code"]');
    const submitButton = dashboardForm.querySelector('button[type="submit"]');
    const errorDiv = document.getElementById('dashboard-access-error');
    const successDiv = document.getElementById('dashboard-access-success');
    const loadingDiv = document.getElementById('dashboard-access-loading');

    // Format access code input
    accessCodeInput.addEventListener('input', function(e) {
      let value = e.target.value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
      if (value.length > 0) {
        // Add FTAC prefix if not present
        if (!value.startsWith('FTAC')) {
          value = 'FTAC' + value;
        }
        // Format with dashes
        value = value.replace(/(.{4})(.{4})?(.{4})?(.{4})?/, function(match, p1, p2, p3, p4) {
          let result = p1;
          if (p2) result += '-' + p2;
          if (p3) result += '-' + p3;
          if (p4) result += '-' + p4;
          return result;
        });
      }
      e.target.value = value;
    });

    // Handle form submission
    dashboardForm.addEventListener('submit', function(e) {
      e.preventDefault();

      const formData = new FormData(dashboardForm);
      const accessCode = formData.get('access_code');

      // Hide previous messages
      errorDiv.style.display = 'none';
      successDiv.style.display = 'none';

      // Show loading state
      submitButton.disabled = true;
      loadingDiv.style.display = 'flex';

      // Validate access code format
      const codePattern = /^FTAC-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
      if (!codePattern.test(accessCode)) {
        showError('Please enter a valid access code in the format FTAC-XXXX-YYYY-ZZZZ');
        return;
      }

      // Validate access code using the access manager system
      validateAccessCode(accessCode);
    });

    function showError(message) {
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      submitButton.disabled = false;
      loadingDiv.style.display = 'none';
    }

    function showSuccess(message) {
      successDiv.textContent = message;
      successDiv.style.display = 'block';
      loadingDiv.style.display = 'none';
    }

    function validateAccessCode(accessCode) {
      // Demo access codes for testing
      const demoAccessCodes = {
        'FTAC-DEMO-TEST-1234': 'Future Tech Foundations',
        'FTAC-DEMO-MAST-5678': 'Advanced Tech Mastery',
        'FTAC-TEST-FOUN-9012': 'Future Tech Foundations',
        'FTAC-TEST-LEAD-3456': 'Tech Leadership Suite',
        'FTAC-TEST-PROD-1499': 'Test Product ($14.99)'
      };

      // Simulate server-side validation
      setTimeout(function() {
        if (demoAccessCodes[accessCode]) {
          const productName = demoAccessCodes[accessCode];
          showSuccess(`Access code verified! You now have access to ${productName}. Refreshing dashboard...`);

          // In production, this would make an AJAX call to update customer metafields
          // For now, just refresh the page to simulate the update
          setTimeout(function() {
            window.location.reload();
          }, 2000);
        } else {
          // Check if it's a valid format but unknown code
          const validFormat = /^FTAC-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/.test(accessCode);
          if (validFormat) {
            showError('Access code not found in our system. Please check your welcome email or contact Sarah for help.');
          } else {
            showError('Invalid access code format. Please use format: FTAC-XXXX-YYYY-ZZZZ');
          }
        }
      }, 1500);
    }
  }
});
</script>
