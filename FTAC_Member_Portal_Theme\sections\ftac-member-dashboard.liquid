{{ 'base.css' | asset_url | stylesheet_tag }}
{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}
{{ 'luxury-components.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-member-dashboard {
    background: linear-gradient(135deg, var(--ftac-cream-base) 0%, #FFFEF7 50%, var(--ftac-cream-base) 100%);
    min-height: 100vh;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
  }

  /* Luxury floating elements */
  .ftac-member-dashboard::before {
    content: '';
    position: absolute;
    top: 10%;
    right: 5%;
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    border-radius: 50%;
    opacity: 0.05;
    animation: ftacFloat 8s ease-in-out infinite;
  }

  .ftac-member-dashboard::after {
    content: '';
    position: absolute;
    bottom: 15%;
    left: 3%;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--ftac-sage-accent), #70E0A4);
    border-radius: 50%;
    opacity: 0.06;
    animation: ftacFloat 10s ease-in-out infinite reverse;
  }

  @keyframes ftacFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-25px) rotate(8deg); }
  }

  .ftac-member-dashboard__container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
  }

  .ftac-member-dashboard__header {
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    padding: 3rem 2rem;
    border-radius: 30px;
    margin-bottom: 3rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(255, 200, 221, 0.3);
  }

  .ftac-member-dashboard__header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") center/80px 80px;
    opacity: 0.3;
  }

  .ftac-member-dashboard__welcome {
    font-family: 'Playfair Display', serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__subtitle {
    font-family: 'Open Sans', sans-serif;
    font-size: clamp(1rem, 2vw, 1.2rem);
    opacity: 0.95;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
    line-height: 1.6;
  }
  
  .ftac-member-dashboard__stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
  }

  .ftac-member-dashboard__stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.4);
  }

  .ftac-member-dashboard__stat-number {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
  }

  .ftac-member-dashboard__stat-label {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: white;
    opacity: 0.9;
    font-weight: 500;
  }

  .ftac-member-dashboard__content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 2rem;
  }
  
  /* Masonry Grid Layout for Premium Experience */
  .ftac-member-dashboard__bundles {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    padding: 2.5rem;
    border-radius: 30px;
    border: 1px solid rgba(255, 200, 221, 0.2);
    box-shadow: 0 20px 40px rgba(255, 200, 221, 0.15);
    position: relative;
    overflow: hidden;
  }

  .ftac-member-dashboard__bundles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    opacity: 0.02;
  }

  .ftac-member-dashboard__section-title {
    font-family: 'Playfair Display', serif;
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__section-icon {
    width: 32px;
    height: 32px;
    color: var(--ftac-blush-primary);
    filter: drop-shadow(0 2px 4px rgba(255, 200, 221, 0.3));
  }
  
  /* Luxury Bundle Cards with Pinterest-style Layout */
  .ftac-member-dashboard__bundles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__bundle {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 200, 221, 0.2);
    border-radius: 25px;
    padding: 2rem;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(255, 200, 221, 0.1);
  }

  .ftac-member-dashboard__bundle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .ftac-member-dashboard__bundle:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(255, 200, 221, 0.25);
    border-color: var(--ftac-blush-primary);
  }

  .ftac-member-dashboard__bundle:hover::before {
    opacity: 0.03;
  }

  .ftac-member-dashboard__bundle-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__bundle-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 0.5rem;
    line-height: 1.3;
  }

  .ftac-member-dashboard__bundle-date {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.85rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.6;
    font-style: italic;
  }

  .ftac-member-dashboard__bundle-badge {
    background: linear-gradient(135deg, var(--ftac-sage-accent), #70E0A4);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-family: 'Open Sans', sans-serif;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(128, 237, 153, 0.3);
  }
  
  .ftac-member-dashboard__bundle-resources {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__resource {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(255, 248, 231, 0.8), rgba(255, 248, 231, 0.4));
    border: 1px solid rgba(255, 200, 221, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .ftac-member-dashboard__resource:hover {
    background: linear-gradient(135deg, rgba(255, 248, 231, 1), rgba(255, 248, 231, 0.7));
    border-color: var(--ftac-blush-primary);
    transform: translateX(5px);
    box-shadow: 0 8px 25px rgba(255, 200, 221, 0.2);
  }

  .ftac-member-dashboard__resource-icon {
    width: 24px;
    height: 24px;
    color: var(--ftac-blush-primary);
    filter: drop-shadow(0 2px 4px rgba(255, 200, 221, 0.3));
  }

  .ftac-member-dashboard__resource-name {
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem;
    color: var(--ftac-charcoal-text);
    flex-grow: 1;
    font-weight: 500;
  }

  .ftac-member-dashboard__resource-size {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.8rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.6;
    font-style: italic;
  }
  
  .ftac-member-dashboard__bundle-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .ftac-member-dashboard__action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  .ftac-member-dashboard__action-button:hover::before {
    left: 100%;
  }

  .ftac-member-dashboard__action-button--primary {
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    box-shadow: 0 8px 25px rgba(255, 200, 221, 0.4);
  }

  .ftac-member-dashboard__action-button--primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 200, 221, 0.6);
  }

  .ftac-member-dashboard__action-button--secondary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--ftac-blush-primary);
    border: 2px solid var(--ftac-blush-primary);
    backdrop-filter: blur(10px);
  }

  .ftac-member-dashboard__action-button--secondary:hover {
    background: var(--ftac-blush-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(255, 200, 221, 0.4);
  }

  .ftac-member-dashboard__action-icon {
    width: 20px;
    height: 20px;
  }
  
  /* Luxury Support Section */
  .ftac-member-dashboard__support {
    background: linear-gradient(135deg, var(--ftac-dusty-trust), rgba(162, 210, 255, 0.8));
    padding: 2.5rem;
    border-radius: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(162, 210, 255, 0.2);
  }

  .ftac-member-dashboard__support::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm0 0c0 5.5 4.5 10 10 10s10-4.5 10-10-4.5-10-10-10-10 4.5-10 10z'/%3E%3C/g%3E%3C/svg%3E") center/60px 60px;
  }

  .ftac-member-dashboard__support-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__support-text {
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem;
    color: white;
    opacity: 0.95;
    margin-bottom: 2rem;
    line-height: 1.6;
    position: relative;
    z-index: 1;
  }

  .ftac-member-dashboard__support-button {
    background: rgba(255, 255, 255, 0.95);
    color: var(--ftac-dusty-trust);
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
    display: inline-block;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
  }

  .ftac-member-dashboard__support-button:hover {
    background: white;
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.5);
  }
  
  /* Responsive Luxury Layout */
  @media (min-width: 768px) {
    .ftac-member-dashboard__container {
      padding: 0 3rem;
    }

    .ftac-member-dashboard__content {
      grid-template-columns: 2fr 1fr;
      gap: 3rem;
    }

    .ftac-member-dashboard__bundles-grid {
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }

    .ftac-member-dashboard__bundle-resources {
      grid-template-columns: repeat(2, 1fr);
    }

    .ftac-member-dashboard__bundle-actions {
      flex-direction: row;
    }

    .ftac-member-dashboard__stats {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (min-width: 1024px) {
    .ftac-member-dashboard__welcome {
      font-size: 3.5rem;
    }

    .ftac-member-dashboard__bundles-grid {
      grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    }

    .ftac-member-dashboard__container {
      padding: 0 4rem;
    }
  }

  @media (min-width: 1200px) {
    .ftac-member-dashboard__bundles-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
</style>

{% if customer %}
<section class="ftac-member-dashboard">
  <div class="ftac-member-dashboard__container">
    <!-- Luxury Welcome Header -->
    <div class="ftac-member-dashboard__header">
      <h1 class="ftac-member-dashboard__welcome">
        Welcome to Your <span style="font-family: 'Dancing Script', cursive; color: rgba(255, 255, 255, 0.9);">Dream Space</span>, {{ customer.first_name | default: 'Beautiful Soul' }}! ✨
      </h1>
      <p class="ftac-member-dashboard__subtitle">
        Your handcrafted digital treasures await. Each piece lovingly curated to inspire and transform your everyday moments into something magical.
      </p>
      
      <div class="ftac-member-dashboard__stats">
        {% assign bundle_count = 0 %}
        {% assign total_downloads = 0 %}
        {% for tag in customer.tags %}
          {% if tag contains 'bundle_' %}
            {% assign bundle_count = bundle_count | plus: 1 %}
          {% endif %}
        {% endfor %}
        {% assign total_downloads = customer.metafields.ftac.download_count | default: 0 %}
        
        <div class="ftac-member-dashboard__stat">
          <span class="ftac-member-dashboard__stat-number">{{ bundle_count }}</span>
          <span class="ftac-member-dashboard__stat-label">💎 Treasure Collections</span>
        </div>
        <div class="ftac-member-dashboard__stat">
          <span class="ftac-member-dashboard__stat-number">{{ total_downloads }}</span>
          <span class="ftac-member-dashboard__stat-label">✨ Magic Downloads</span>
        </div>
        <div class="ftac-member-dashboard__stat">
          <span class="ftac-member-dashboard__stat-number">∞</span>
          <span class="ftac-member-dashboard__stat-label">💖 Lifetime Love</span>
        </div>
      </div>
    </div>

    <div class="ftac-member-dashboard__content">
      <!-- Luxury Member Bundles with Masonry Grid -->
      <div class="ftac-member-dashboard__bundles">
        <h2 class="ftac-member-dashboard__section-title">
          <svg class="ftac-member-dashboard__section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
          </svg>
          Your Handcrafted Treasures
        </h2>

        <div class="ftac-member-dashboard__bundles-grid">
          {% render 'ftac-member-bundles', customer: customer %}
        </div>
      </div>

      <!-- Order History Section -->
      <div class="ftac-member-dashboard__order-history">
        {% render 'ftac-order-history', action: 'display_order_history', customer: customer %}
      </div>

      <!-- Luxury Support Section -->
      <div class="ftac-member-dashboard__support">
        <h3 class="ftac-member-dashboard__support-title">💌 Need Help?</h3>
        <p class="ftac-member-dashboard__support-text">
          Get support with downloads, access codes, or technical questions. We respond within 24-48 hours. ✨
        </p>
        <a href="/pages/contact-support" class="ftac-member-dashboard__support-button">
          💖 Get Support
        </a>
      </div>
    </div>
  </div>
</section>
{% else %}
<!-- Fallback for non-logged-in users -->
<section class="ftac-member-dashboard">
  <div class="ftac-member-dashboard__container">
    <div class="ftac-member-dashboard__header">
      <h1 class="ftac-member-dashboard__welcome">
        Welcome to Your Member Dashboard! ✨
      </h1>
      <p class="ftac-member-dashboard__subtitle">
        Please log in to access your exclusive content and manage your membership.
      </p>
    </div>

    <div class="ftac-member-dashboard__content">
      <div style="text-align: center; padding: 3rem 0;">
        <a href="/account/login" style="background: var(--ftac-dusty-trust); color: white; padding: 1rem 2rem; border-radius: 25px; text-decoration: none; font-weight: 600;">
          Log In to Your Account
        </a>
      </div>
    </div>
  </div>
</section>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Luxury dashboard animations and interactions

  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Animate bundle cards on scroll
  const bundleCards = document.querySelectorAll('.ftac-member-dashboard__bundle');
  bundleCards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px)';
    card.style.transition = `all 0.6s ease ${index * 0.1}s`;
    observer.observe(card);
  });

  // Animate stats on scroll
  const stats = document.querySelectorAll('.ftac-member-dashboard__stat');
  stats.forEach((stat, index) => {
    stat.style.opacity = '0';
    stat.style.transform = 'translateY(20px)';
    stat.style.transition = `all 0.5s ease ${index * 0.1}s`;
    observer.observe(stat);
  });

  // Add sparkle effect to action buttons
  const actionButtons = document.querySelectorAll('.ftac-member-dashboard__action-button--primary');
  actionButtons.forEach(button => {
    button.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-3px) scale(1.02)';
    });

    button.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0) scale(1)';
    });
  });

  // Parallax effect for floating elements
  window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const dashboard = document.querySelector('.ftac-member-dashboard');

    if (dashboard) {
      const dashboardRect = dashboard.getBoundingClientRect();
      if (dashboardRect.bottom > 0 && dashboardRect.top < window.innerHeight) {
        dashboard.style.transform = `translateY(${scrolled * 0.05}px)`;
      }
    }
  });

  // Add hover glow effect to resource items
  const resources = document.querySelectorAll('.ftac-member-dashboard__resource');
  resources.forEach(resource => {
    resource.addEventListener('mouseenter', function() {
      this.style.boxShadow = '0 8px 25px rgba(255, 200, 221, 0.3)';
    });

    resource.addEventListener('mouseleave', function() {
      this.style.boxShadow = 'none';
    });
  });

  // Masonry grid auto-adjustment (if needed)
  function adjustMasonryGrid() {
    const grid = document.querySelector('.ftac-member-dashboard__bundles-grid');
    if (grid && window.innerWidth > 768) {
      const items = grid.querySelectorAll('.ftac-member-dashboard__bundle');
      items.forEach((item, index) => {
        // Add slight rotation for Pinterest-style effect
        const rotation = (index % 2 === 0) ? 'rotate(0.5deg)' : 'rotate(-0.5deg)';
        item.style.transform = `${rotation} translateY(0)`;
      });
    }
  }

  // Initialize masonry adjustments
  adjustMasonryGrid();
  window.addEventListener('resize', adjustMasonryGrid);
});
</script>

{% schema %}
{
  "name": "FTAC Member Dashboard",
  "settings": [
    {
      "type": "header",
      "content": "Dashboard Settings"
    },
    {
      "type": "text",
      "id": "welcome_message",
      "label": "Custom Welcome Message",
      "default": "Your handcrafted digital treasures await"
    }
  ],
  "presets": [
    {
      "name": "FTAC Member Dashboard"
    }
  ]
}
{% endschema %}
