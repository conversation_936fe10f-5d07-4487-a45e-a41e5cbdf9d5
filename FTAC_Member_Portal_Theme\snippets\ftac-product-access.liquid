{% comment %}
  FTAC Product-Level Access Control System

  This snippet provides helper functions for managing product-specific access codes
  instead of the old membership-level access system.

  New Customer Data Structure:

  Customer Metafields:
  - ftac.product_access_codes (JSON string):
    {
      "product_handle_1": {
        "access_code": "FTAC-ABCD-1234-EFGH",
        "granted_date": "2024-01-15",
        "etsy_order_id": "**********"
      },
      "product_handle_2": {
        "access_code": "FTAC-WXYZ-5678-IJKL",
        "granted_date": "2024-01-20",
        "etsy_order_id": "**********"
      }
    }

  Customer Tags (optional, for backwards compatibility):
  - "ftac_customer" (indicates they have an FTAC account)

  Usage Examples:

  1. Check if customer has access to a specific product:
     {% render 'ftac-product-access', action: 'check_access', customer: customer, product_handle: 'future-tech-foundations' %}
     {% assign has_access = ftac_access_result %}

  2. Get access code for a product:
     {% render 'ftac-product-access', action: 'get_access_code', customer: customer, product_handle: 'future-tech-foundations' %}
     {% assign access_code = ftac_access_code %}

  3. List all accessible products:
     {% render 'ftac-product-access', action: 'list_products', customer: customer %}
     {% assign accessible_products = ftac_accessible_products %}
{% endcomment %}

{% assign action = action | default: 'check_access' %}
{% assign customer = customer %}
{% assign product_handle = product_handle %}
{% assign access_code = access_code %}

{% comment %} Initialize global result variables {% endcomment %}
{% assign ftac_access_result = false %}
{% assign ftac_access_code = '' %}
{% assign ftac_accessible_products = '' %}
{% assign ftac_validation_result = false %}

{% comment %} Get the product access codes from customer metafields {% endcomment %}
{% assign product_access_data = customer.metafields.ftac.product_access_codes %}
{% assign access_codes_json = '{}' %}

{% if product_access_data %}
  {% assign access_codes_json = product_access_data %}
{% endif %}

{% comment %}
  Demo Access Database - In production, this would come from customer metafields
  For demo purposes, we'll simulate some customer access data
{% endcomment %}
{% assign demo_customer_access = '' %}
{% if customer %}
  {% case customer.email %}
    {% when '<EMAIL>' %}
      {% assign demo_customer_access = '{"future-tech-foundations":{"access_code":"FTAC-DEMO-TEST-1234","granted_date":"2024-01-15"},"advanced-tech-mastery":{"access_code":"FTAC-DEMO-MAST-5678","granted_date":"2024-02-20"}}' %}
    {% when '<EMAIL>' %}
      {% assign demo_customer_access = '{"test-product":{"access_code":"FTAC-TEST-PROD-1499","granted_date":"2024-01-10"}}' %}
    {% when '<EMAIL>' %}
      {% assign demo_customer_access = '{"tech-leadership-suite":{"access_code":"FTAC-TEST-LEAD-3456","granted_date":"2024-03-01"}}' %}
  {% endcase %}
{% endif %}

{% comment %} Use demo data if no real metafield data exists {% endcomment %}
{% if access_codes_json == '{}' and demo_customer_access != '' %}
  {% assign access_codes_json = demo_customer_access %}
{% endif %}

{% comment %} Parse the JSON data (simplified parsing for Liquid) {% endcomment %}
{% assign has_access = false %}
{% assign found_access_code = '' %}
{% assign accessible_products_list = '' %}

{% case action %}
  {% when 'check_access' %}
    {% comment %} Check if customer has access to the specified product {% endcomment %}
    {% if product_handle and access_codes_json contains product_handle %}
      {% assign ftac_access_result = true %}
    {% endif %}

  {% when 'get_access_code' %}
    {% comment %} Get the access code for a specific product {% endcomment %}
    {% comment %} This is a simplified version - in a real implementation, you'd parse JSON properly {% endcomment %}
    {% if product_handle and access_codes_json contains product_handle %}
      {% comment %} Extract access code from JSON string {% endcomment %}
      {% assign search_pattern = '"' | append: product_handle | append: '":' %}
      {% if access_codes_json contains search_pattern %}
        {% assign code_start = access_codes_json | split: search_pattern | last %}
        {% assign code_part = code_start | split: '"access_code":"' | last %}
        {% assign ftac_access_code = code_part | split: '"' | first %}
      {% endif %}
    {% endif %}

  {% when 'list_products' %}
    {% comment %} List all products the customer has access to {% endcomment %}
    {% comment %} This would parse the JSON and return product handles {% endcomment %}
    {% if access_codes_json != '{}' %}
      {% comment %} Extract product handles from JSON keys {% endcomment %}
      {% assign json_content = access_codes_json | remove: '{' | remove: '}' %}
      {% assign key_value_pairs = json_content | split: ',' %}
      {% for pair in key_value_pairs %}
        {% if pair contains '"' %}
          {% assign key = pair | split: '":' | first | remove: '"' | strip %}
          {% if key != '' %}
            {% assign ftac_accessible_products = ftac_accessible_products | append: key | append: ',' %}
          {% endif %}
        {% endif %}
      {% endfor %}
    {% endif %}

  {% when 'add_access' %}
    {% comment %} Add access for a product (this would require server-side implementation) {% endcomment %}
    {% comment %} In a real implementation, this would use Shopify's Admin API to update customer metafields {% endcomment %}
    {% comment %} For now, this is a placeholder that would be implemented via webhook or admin interface {% endcomment %}

  {% else %}
    {% comment %} Default action: check access {% endcomment %}
    {% if product_handle and access_codes_json contains product_handle %}
      {% assign ftac_access_result = true %}
    {% endif %}
{% endcase %}

{% comment %}
  Helper Functions for Product Access Management
  
  These functions provide a clean interface for checking and managing
  product-specific access codes.
{% endcomment %}

{% comment %} 
  Product Access Code Format:
  FTAC-[PRODUCT_CODE]-[RANDOM_4]-[RANDOM_4]
  
  Examples:
  - FTAC-FOUN-ABCD-1234 (Future Tech Foundations)
  - FTAC-MAST-WXYZ-5678 (Advanced Tech Mastery)
  - FTAC-LEAD-IJKL-9012 (Tech Leadership Suite)
  
  Product Handle Mapping:
  - future-tech-foundations → FOUN
  - advanced-tech-mastery → MAST
  - tech-leadership-suite → LEAD
  - custom-bundle → CUST
{% endcomment %}

{% assign product_code_map = 'future-tech-foundations:FOUN,advanced-tech-mastery:MAST,tech-leadership-suite:LEAD,custom-bundle:CUST' %}

{% comment %}
  Access Code Validation Function
  
  Validates that an access code matches the expected format and
  corresponds to the correct product.
{% endcomment %}

{% if validate_access_code %}
  {% assign code_valid = false %}
  {% assign code_pattern = 'FTAC-' %}

  {% if access_code contains code_pattern and access_code.size == 19 %}
    {% assign code_parts = access_code | split: '-' %}
    {% if code_parts.size == 4 and code_parts[0] == 'FTAC' %}
      {% assign product_code = code_parts[1] %}

      {% comment %} Check if product code matches the product handle {% endcomment %}
      {% assign mapping_pairs = product_code_map | split: ',' %}
      {% for mapping in mapping_pairs %}
        {% assign handle_code = mapping | split: ':' %}
        {% if handle_code[0] == product_handle and handle_code[1] == product_code %}
          {% assign code_valid = true %}
          {% break %}
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endif %}

  {% assign ftac_validation_result = code_valid %}
{% endif %}
