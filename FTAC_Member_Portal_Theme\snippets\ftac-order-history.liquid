{% comment %}
  FTAC Order History Integration System
  
  This snippet integrates customer order history with the access system to:
  - Display customer's purchase history
  - Show which products they have access to
  - Connect Etsy orders to Shopify customer accounts
  - Provide order-based access validation
  
  Parameters:
  - action: 'get_orders', 'get_order_products', 'validate_order_access'
  - customer: Current customer object
  - order_id: Specific order ID (optional)
  - product_handle: Product handle to check (optional)
  
  Usage:
  {% render 'ftac-order-history', action: 'get_orders', customer: customer %}
  {% assign customer_orders = ftac_customer_orders %}
{% endcomment %}

{% assign action = action | default: 'get_orders' %}
{% assign customer = customer %}
{% assign order_id = order_id %}
{% assign product_handle = product_handle %}

{% comment %} Initialize global result variables {% endcomment %}
{% assign ftac_customer_orders = '' %}
{% assign ftac_order_products = '' %}
{% assign ftac_order_access_valid = false %}
{% assign ftac_order_total = '' %}
{% assign ftac_order_date = '' %}

{% comment %}
  Demo Order History Database
  
  In production, this would integrate with:
  1. Shopify customer order history
  2. Etsy order webhooks/API
  3. Customer metafields for order tracking
  
  Format: customer_email:order_id:order_date:product_handle:product_name:price:etsy_order_id
{% endcomment %}

{% assign demo_order_history = '<EMAIL>:1001:2024-01-15:future-tech-foundations:Future Tech Foundations:14.99:ETY123456789,<EMAIL>:1002:2024-02-20:advanced-tech-mastery:Advanced Tech Mastery:29.99:ETY987654321,<EMAIL>:1003:2024-01-10:test-product:Test Product:14.99:ETY555666777,<EMAIL>:1004:2024-03-01:tech-leadership-suite:Tech Leadership Suite:49.99:ETY111222333' %}

{% comment %}
  Order to Access Code Mapping
  
  This maps order IDs to the access codes that were sent to customers
  Format: order_id:access_code:sent_date:email_sent
{% endcomment %}

{% assign order_access_codes = '1001:FTAC-DEMO-TEST-1234:2024-01-15:true,1002:FTAC-DEMO-MAST-5678:2024-02-20:true,1003:FTAC-TEST-PROD-1499:2024-01-10:true,1004:FTAC-TEST-LEAD-3456:2024-03-01:true' %}

{% case action %}
  {% when 'get_orders' %}
    {% comment %} Get all orders for the current customer {% endcomment %}
    {% if customer %}
      {% assign customer_email = customer.email %}
      {% assign orders_list = '' %}
      {% assign orders = demo_order_history | split: ',' %}
      
      {% for order in orders %}
        {% assign order_parts = order | split: ':' %}
        {% if order_parts[0] == customer_email %}
          {% if orders_list != '' %}
            {% assign orders_list = orders_list | append: '|' %}
          {% endif %}
          {% assign orders_list = orders_list | append: order %}
        {% endif %}
      {% endfor %}
      
      {% assign ftac_customer_orders = orders_list %}
    {% endif %}

  {% when 'get_order_products' %}
    {% comment %} Get products for a specific order {% endcomment %}
    {% if order_id %}
      {% assign orders = demo_order_history | split: ',' %}
      {% for order in orders %}
        {% assign order_parts = order | split: ':' %}
        {% if order_parts[1] == order_id %}
          {% assign ftac_order_products = order_parts[3] %}
          {% assign ftac_order_total = order_parts[5] %}
          {% assign ftac_order_date = order_parts[2] %}
          {% break %}
        {% endif %}
      {% endfor %}
    {% endif %}

  {% when 'validate_order_access' %}
    {% comment %} Validate that customer has purchased the product {% endcomment %}
    {% if customer and product_handle %}
      {% assign customer_email = customer.email %}
      {% assign orders = demo_order_history | split: ',' %}
      
      {% for order in orders %}
        {% assign order_parts = order | split: ':' %}
        {% if order_parts[0] == customer_email and order_parts[3] == product_handle %}
          {% assign ftac_order_access_valid = true %}
          {% assign ftac_order_date = order_parts[2] %}
          {% assign ftac_order_total = order_parts[5] %}
          {% break %}
        {% endif %}
      {% endfor %}
    {% endif %}

  {% when 'get_access_code_for_order' %}
    {% comment %} Get the access code that was sent for a specific order {% endcomment %}
    {% if order_id %}
      {% assign access_codes = order_access_codes | split: ',' %}
      {% for code_mapping in access_codes %}
        {% assign mapping_parts = code_mapping | split: ':' %}
        {% if mapping_parts[0] == order_id %}
          {% assign ftac_order_access_code = mapping_parts[1] %}
          {% assign ftac_access_code_sent_date = mapping_parts[2] %}
          {% assign ftac_access_code_email_sent = mapping_parts[3] %}
          {% break %}
        {% endif %}
      {% endfor %}
    {% endif %}

  {% when 'get_order_summary' %}
    {% comment %} Get comprehensive order summary for customer {% endcomment %}
    {% if customer %}
      {% render 'ftac-order-history', action: 'get_orders', customer: customer %}
      {% assign orders_list = ftac_customer_orders | split: '|' %}
      {% assign total_orders = orders_list.size %}
      {% assign total_spent = 0 %}
      {% assign products_purchased = '' %}
      
      {% for order in orders_list %}
        {% assign order_parts = order | split: ':' %}
        {% assign order_amount = order_parts[5] | plus: 0 %}
        {% assign total_spent = total_spent | plus: order_amount %}
        
        {% if products_purchased != '' %}
          {% assign products_purchased = products_purchased | append: ',' %}
        {% endif %}
        {% assign products_purchased = products_purchased | append: order_parts[3] %}
      {% endfor %}
      
      {% assign ftac_total_orders = total_orders %}
      {% assign ftac_total_spent = total_spent %}
      {% assign ftac_products_purchased = products_purchased %}
    {% endif %}

  {% else %}
    {% comment %} Default: get orders {% endcomment %}
    {% render 'ftac-order-history', action: 'get_orders', customer: customer %}
{% endcase %}

{% comment %}
  Order History Display Helper
  
  This section provides formatted display of order history
{% endcomment %}

{% if action == 'display_order_history' and customer %}
  <div class="ftac-order-history">
    <h3 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-xl); font-weight: var(--ftac-font-semibold); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-6);">
      Your Purchase History
    </h3>
    
    {% render 'ftac-order-history', action: 'get_orders', customer: customer %}
    {% assign orders_list = ftac_customer_orders | split: '|' %}
    
    {% if orders_list.size > 0 %}
      <div class="ftac-order-history__list">
        {% for order in orders_list %}
          {% assign order_parts = order | split: ':' %}
          {% assign order_id = order_parts[1] %}
          {% assign order_date = order_parts[2] %}
          {% assign product_handle = order_parts[3] %}
          {% assign product_name = order_parts[4] %}
          {% assign product_price = order_parts[5] %}
          {% assign etsy_order_id = order_parts[6] %}
          
          {% comment %} Get access code for this order {% endcomment %}
          {% render 'ftac-order-history', action: 'get_access_code_for_order', order_id: order_id %}
          
          <div class="ftac-order-history__item">
            <div class="ftac-order-history__item-header">
              <div>
                <h4 style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-lg); font-weight: var(--ftac-font-medium); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-1);">
                  {{ product_name }}
                </h4>
                <p style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); color: var(--ftac-charcoal); opacity: 0.7;">
                  Order #{{ order_id }} • {{ order_date | date: "%B %d, %Y" }}
                </p>
              </div>
              <div style="text-align: right;">
                <p style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-lg); font-weight: var(--ftac-font-semibold); color: var(--ftac-academy-blue);">
                  ${{ product_price }}
                </p>
                <p style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-xs); color: var(--ftac-charcoal); opacity: 0.6;">
                  Etsy: {{ etsy_order_id }}
                </p>
              </div>
            </div>
            
            {% if ftac_order_access_code %}
              <div style="background-color: rgba(var(--ftac-academy-blue-rgb), 0.1); padding: var(--ftac-space-3); border-radius: var(--ftac-radius-lg); margin-top: var(--ftac-space-3);">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                  <div>
                    <p style="font-family: var(--ftac-font-primary); font-size: var(--ftac-text-sm); font-weight: var(--ftac-font-medium); color: var(--ftac-academy-blue); margin-bottom: var(--ftac-space-1);">
                      Access Code:
                    </p>
                    <code style="font-family: 'Courier New', monospace; font-size: var(--ftac-text-sm); color: var(--ftac-charcoal); background-color: white; padding: var(--ftac-space-2); border-radius: var(--ftac-radius-md); letter-spacing: 1px;">
                      {{ ftac_order_access_code }}
                    </code>
                  </div>
                  
                  {% comment %} Check if customer has already used this access code {% endcomment %}
                  {% render 'ftac-product-access', action: 'check_access', customer: customer, product_handle: product_handle %}
                  {% if ftac_access_result %}
                    <div style="display: flex; align-items: center; gap: var(--ftac-space-2); color: var(--ftac-sage-green);">
                      <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); font-weight: var(--ftac-font-medium);">
                        Activated
                      </span>
                    </div>
                  {% else %}
                    <div style="display: flex; align-items: center; gap: var(--ftac-space-2); color: var(--ftac-dusty-rose);">
                      <svg style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                      </svg>
                      <span style="font-family: var(--ftac-font-secondary); font-size: var(--ftac-text-sm); font-weight: var(--ftac-font-medium);">
                        Not Activated
                      </span>
                    </div>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          </div>
        {% endfor %}
      </div>
    {% else %}
      <div style="text-align: center; padding: var(--ftac-space-8); background-color: rgba(var(--ftac-warm-cream-rgb), 0.3); border-radius: var(--ftac-radius-lg);">
        <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); opacity: 0.8;">
          No purchase history found. Orders will appear here after you make purchases from our Etsy shop.
        </p>
      </div>
    {% endif %}
  </div>
{% endif %}

{% comment %}
  Demo Customer Order Data:
  
  <EMAIL>:
  - Order #1001 (Jan 15, 2024): Future Tech Foundations ($14.99) - Access Code: FTAC-DEMO-TEST-1234
  - Order #1002 (Feb 20, 2024): Advanced Tech Mastery ($29.99) - Access Code: FTAC-DEMO-MAST-5678
  
  <EMAIL>:
  - Order #1003 (Jan 10, 2024): Test Product ($14.99) - Access Code: FTAC-TEST-PROD-1499
  
  <EMAIL>:
  - Order #1004 (Mar 1, 2024): Tech Leadership Suite ($49.99) - Access Code: FTAC-TEST-LEAD-3456
{% endcomment %}
