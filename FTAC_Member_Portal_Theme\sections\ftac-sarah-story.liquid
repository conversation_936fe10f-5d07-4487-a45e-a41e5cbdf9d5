{{ 'base.css' | asset_url | stylesheet_tag }}
{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-sarah-story {
    padding: var(--ftac-space-20) 0;
    background-color: white;
  }
  
  .ftac-sarah-story__container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
  }
  
  .ftac-sarah-story__header {
    text-align: center;
    margin-bottom: var(--ftac-space-16);
  }
  
  .ftac-sarah-story__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-4xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-sarah-story__subtitle {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-xl);
    color: var(--ftac-charcoal);
    opacity: 0.8;
  }
  
  .ftac-sarah-story__content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ftac-space-8);
  }
  
  .ftac-sarah-story__paragraph {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-charcoal);
    line-height: 1.7;
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-sarah-story__highlight {
    background: linear-gradient(120deg, var(--ftac-warm-cream) 0%, var(--ftac-warm-cream) 100%);
    padding: var(--ftac-space-1) var(--ftac-space-2);
    border-radius: var(--ftac-radius-sm);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-academy-blue);
  }
  
  .ftac-sarah-story__quote {
    background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green));
    color: white;
    padding: var(--ftac-space-8);
    border-radius: var(--ftac-radius-xl);
    margin: var(--ftac-space-8) 0;
    position: relative;
    text-align: center;
  }
  
  .ftac-sarah-story__quote::before {
    content: '"';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 80px;
    color: white;
    font-family: serif;
    line-height: 1;
  }
  
  .ftac-sarah-story__quote-text {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-medium);
    line-height: 1.5;
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-sarah-story__quote-author {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    opacity: 0.9;
  }
  
  .ftac-sarah-story__values {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ftac-space-6);
    margin-top: var(--ftac-space-12);
  }
  
  .ftac-sarah-story__value {
    display: flex;
    align-items: flex-start;
    gap: var(--ftac-space-4);
    padding: var(--ftac-space-6);
    background-color: rgba(var(--ftac-warm-cream-rgb), 0.3);
    border-radius: var(--ftac-radius-lg);
    border-left: 4px solid var(--ftac-academy-blue);
  }
  
  .ftac-sarah-story__value-icon {
    width: 32px;
    height: 32px;
    color: var(--ftac-academy-blue);
    flex-shrink: 0;
    margin-top: var(--ftac-space-1);
  }
  
  .ftac-sarah-story__value-content {
    flex: 1;
  }
  
  .ftac-sarah-story__value-title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-2);
  }
  
  .ftac-sarah-story__value-description {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
    line-height: 1.6;
    opacity: 0.8;
  }
  
  /* Desktop Layout */
  @media (min-width: 768px) {
    .ftac-sarah-story__values {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (min-width: 1024px) {
    .ftac-sarah-story__title {
      font-size: var(--ftac-text-5xl);
    }
    
    .ftac-sarah-story__quote-text {
      font-size: var(--ftac-text-2xl);
    }
  }
</style>

<section class="ftac-sarah-story">
  <div class="ftac-sarah-story__container">
    <div class="ftac-sarah-story__header">
      <h2 class="ftac-sarah-story__title">Sarah's Story</h2>
      <p class="ftac-sarah-story__subtitle">Where Innovation Meets Heart</p>
    </div>
    
    <div class="ftac-sarah-story__content">
      <p class="ftac-sarah-story__paragraph">
        Hi there! I'm Sarah, and I believe that <span class="ftac-sarah-story__highlight">technology should empower, not intimidate</span>. My journey began when I realized that so many brilliant, creative people were being held back by the very tools that were supposed to help them succeed.
      </p>
      
      <p class="ftac-sarah-story__paragraph">
        After years of working in tech education and seeing countless students struggle with impersonal, one-size-fits-all approaches, I knew there had to be a better way. That's when I created Future Tech Academy Club – a place where <span class="ftac-sarah-story__highlight">innovation truly meets heart</span>.
      </p>
      
      <div class="ftac-sarah-story__quote">
        <p class="ftac-sarah-story__quote-text">
          "Every person deserves to feel confident and supported in their learning journey. Technology isn't just about tools – it's about transforming lives."
        </p>
        <p class="ftac-sarah-story__quote-author">- Sarah Mitchell, Founder</p>
      </div>
      
      <p class="ftac-sarah-story__paragraph">
        What makes Future Tech Academy Club different? It's simple: <span class="ftac-sarah-story__highlight">you're not just getting resources, you're getting a partner</span>. Every bundle comes with my personal commitment to your success. Whether you're stuck on a concept, need encouragement, or want to celebrate a breakthrough, I'm here for you.
      </p>
      
      <p class="ftac-sarah-story__paragraph">
        I've helped thousands of learners transform their relationship with technology, and I can't wait to be part of your journey too. Together, we'll turn your goals into achievements, one supportive step at a time.
      </p>
    </div>
    
    <div class="ftac-sarah-story__values">
      <div class="ftac-sarah-story__value">
        <svg class="ftac-sarah-story__value-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
        <div class="ftac-sarah-story__value-content">
          <h3 class="ftac-sarah-story__value-title">Personal Connection</h3>
          <p class="ftac-sarah-story__value-description">
            You're not just a customer – you're part of our community. I personally respond to every message and celebrate every success with you.
          </p>
        </div>
      </div>
      
      <div class="ftac-sarah-story__value">
        <svg class="ftac-sarah-story__value-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
        <div class="ftac-sarah-story__value-content">
          <h3 class="ftac-sarah-story__value-title">Innovation Made Simple</h3>
          <p class="ftac-sarah-story__value-description">
            Complex concepts broken down into digestible, actionable steps that anyone can follow and implement successfully.
          </p>
        </div>
      </div>
      
      <div class="ftac-sarah-story__value">
        <svg class="ftac-sarah-story__value-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
        <div class="ftac-sarah-story__value-content">
          <h3 class="ftac-sarah-story__value-title">Proven Results</h3>
          <p class="ftac-sarah-story__value-description">
            Thousands of successful learners have transformed their lives using our methods. Your success is our mission.
          </p>
        </div>
      </div>
      
      <div class="ftac-sarah-story__value">
        <svg class="ftac-sarah-story__value-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
        <div class="ftac-sarah-story__value-content">
          <h3 class="ftac-sarah-story__value-title">Lifetime Support</h3>
          <p class="ftac-sarah-story__value-description">
            Your learning journey doesn't end with purchase. I'm here to support you for as long as you need guidance.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "FTAC Sarah Story",
  "settings": [
    {
      "type": "header",
      "content": "Sarah's Story Section"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Sarah's Story"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Section Subtitle",
      "default": "Where Innovation Meets Heart"
    }
  ],
  "presets": [
    {
      "name": "FTAC Sarah Story"
    }
  ]
}
{% endschema %}
