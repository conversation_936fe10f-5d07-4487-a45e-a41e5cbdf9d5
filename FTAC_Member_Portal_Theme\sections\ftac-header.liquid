{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 200, 221, 0.2);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(255, 200, 221, 0.1);
    padding: var(--ftac-space-5) 0;
    transition: all 0.3s ease;
  }

  .ftac-header__container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--ftac-space-8);
    max-width: 1200px;
    margin: 0 auto;
  }

  .ftac-header__logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--ftac-warm-taupe);
  }

  .ftac-header__logo-text {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: var(--ftac-text-3xl);
    line-height: 1;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
  }

  .ftac-header__logo-academy {
    color: var(--ftac-sage-accent);
    font-size: var(--ftac-text-4xl);
    font-family: 'Dancing Script', cursive;
    font-weight: 600;
  }

  .ftac-header__logo-future {
    color: var(--ftac-sage-green);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-medium);
    margin-bottom: var(--ftac-space-1);
  }
  
  .ftac-header__nav {
    display: none;
  }
  
  .ftac-header__nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--ftac-space-8);
  }
  
  .ftac-header__nav-link {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-warm-taupe);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: var(--ftac-space-2) var(--ftac-space-3);
    border-radius: var(--ftac-radius-lg);
  }

  .ftac-header__nav-link:hover {
    color: var(--ftac-dusty-rose);
    background-color: rgba(var(--ftac-dusty-rose-rgb), 0.1);
  }

  .ftac-header__nav-link--active {
    color: var(--ftac-dusty-rose);
    background-color: rgba(var(--ftac-dusty-rose-rgb), 0.15);
  }

  .ftac-header__actions {
    display: flex;
    align-items: center;
    gap: var(--ftac-space-4);
  }

  .ftac-header__cta {
    background-color: var(--ftac-dusty-rose);
    color: white;
    padding: var(--ftac-space-4) var(--ftac-space-8);
    border-radius: var(--ftac-radius-2xl);
    text-decoration: none;
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-medium);
    font-size: var(--ftac-text-base);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(var(--ftac-dusty-rose-rgb), 0.3);
    letter-spacing: 0.025em;
  }

  .ftac-header__cta:hover {
    background-color: rgba(var(--ftac-dusty-rose-rgb), 0.9);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--ftac-dusty-rose-rgb), 0.4);
  }

  .ftac-header__cta--dashboard {
    display: flex;
    align-items: center;
    background-color: var(--ftac-sage-green);
  }

  .ftac-header__cta--dashboard:hover {
    background-color: var(--ftac-sage-green-dark, #7a9b6b);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(var(--ftac-sage-green-rgb), 0.4);
  }

  .ftac-header__logout:hover {
    opacity: 1 !important;
  }
  
  .ftac-header__mobile-menu {
    display: block;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--ftac-space-2);
  }
  
  .ftac-header__mobile-menu-icon {
    width: 24px;
    height: 24px;
    fill: var(--ftac-charcoal);
  }
  
  /* Desktop Navigation */
  @media (min-width: 768px) {
    .ftac-header__nav {
      display: block;
    }
    
    .ftac-header__mobile-menu {
      display: none;
    }
  }
  
  /* Mobile Menu Overlay */
  .ftac-mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    z-index: 200;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .ftac-mobile-menu.active {
    transform: translateX(0);
  }
  
  .ftac-mobile-menu__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ftac-space-4) var(--ftac-space-6);
    border-bottom: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
  }
  
  .ftac-mobile-menu__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--ftac-space-2);
  }
  
  .ftac-mobile-menu__nav {
    padding: var(--ftac-space-6);
  }
  
  .ftac-mobile-menu__list {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .ftac-mobile-menu__item {
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-mobile-menu__link {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
    text-decoration: none;
  }
  
  .ftac-mobile-menu__cta {
    margin-top: var(--ftac-space-8);
  }
</style>

<header class="ftac-header">
  <div class="ftac-header__container">
    <!-- Logo -->
    <a href="{{ routes.root_url }}" class="ftac-header__logo">
      {%- if settings.logo != blank -%}
        <img src="{{ settings.logo | image_url: width: 200 }}" alt="{{ shop.name }}" style="height: 40px; width: auto;">
      {%- else -%}
        <div class="ftac-header__logo-text">
          <div class="ftac-header__logo-future">Future Tech</div>
          <div class="ftac-header__logo-academy">ACADEMY CLUB</div>
        </div>
      {%- endif -%}
    </a>
    
    <!-- Desktop Navigation -->
    <nav class="ftac-header__nav">
      <ul class="ftac-header__nav-list">
        <li><a href="{{ routes.root_url }}" class="ftac-header__nav-link{% if request.page_type == 'index' %} ftac-header__nav-link--active{% endif %}">Home</a></li>
        <li><a href="/pages/shop" class="ftac-header__nav-link{% if page.handle == 'shop' %} ftac-header__nav-link--active{% endif %}">Shop</a></li>
        <li><a href="/pages/contact-support" class="ftac-header__nav-link{% if page.handle == 'contact-support' %} ftac-header__nav-link--active{% endif %}">Contact Support</a></li>
      </ul>
    </nav>
    
    <!-- Actions - Dynamic based on authentication status -->
    <div class="ftac-header__actions">
      {% if customer %}
        <!-- Logged in: Show Dashboard link with user icon -->
        <a href="/pages/member-dashboard" class="ftac-header__cta ftac-header__cta--dashboard">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" style="margin-right: 8px;">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          Dashboard
        </a>
        <!-- Optional: Add logout link -->
        <a href="{{ routes.account_logout_url }}" class="ftac-header__logout" style="margin-left: 12px; color: var(--ftac-charcoal); opacity: 0.7; font-size: 14px; text-decoration: none;">
          Sign Out
        </a>
      {% else %}
        <!-- Not logged in: Show Access Your Content -->
        <a href="/pages/member-access" class="ftac-header__cta">Access Your Content</a>
      {% endif %}
      
      <!-- Mobile Menu Button -->
      <button class="ftac-header__mobile-menu" onclick="toggleMobileMenu()">
        <svg class="ftac-header__mobile-menu-icon" viewBox="0 0 24 24">
          <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>
    </div>
  </div>
</header>

<!-- Mobile Menu Overlay -->
<div class="ftac-mobile-menu" id="mobileMenu">
  <div class="ftac-mobile-menu__header">
    <div class="ftac-header__logo-text">
      <div class="ftac-header__logo-future">Future Tech</div>
      <div class="ftac-header__logo-academy">ACADEMY CLUB</div>
    </div>
    <button class="ftac-mobile-menu__close" onclick="toggleMobileMenu()">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
  
  <nav class="ftac-mobile-menu__nav">
    <ul class="ftac-mobile-menu__list">
      <li class="ftac-mobile-menu__item">
        <a href="{{ routes.root_url }}" class="ftac-mobile-menu__link">Home</a>
      </li>
      <li class="ftac-mobile-menu__item">
        <a href="/pages/shop" class="ftac-mobile-menu__link">Shop</a>
      </li>
      <li class="ftac-mobile-menu__item">
        <a href="/pages/contact-support" class="ftac-mobile-menu__link">Contact Support</a>
      </li>
    </ul>
    
    <div class="ftac-mobile-menu__cta">
      {% if customer %}
        <!-- Logged in: Show Dashboard link -->
        <a href="/pages/member-dashboard" class="ftac-btn ftac-btn-primary ftac-btn-large" style="width: 100%; text-align: center; margin-bottom: 12px;">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" style="margin-right: 8px; vertical-align: middle;">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          Dashboard
        </a>
        <a href="{{ routes.account_logout_url }}" class="ftac-btn ftac-btn-secondary ftac-btn-large" style="width: 100%; text-align: center;">
          Sign Out
        </a>
      {% else %}
        <!-- Not logged in: Show Access Your Content -->
        <a href="/pages/member-access" class="ftac-btn ftac-btn-primary ftac-btn-large" style="width: 100%; text-align: center;">Access Your Content</a>
      {% endif %}
    </div>
  </nav>
</div>

<script>
  function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    mobileMenu.classList.toggle('active');
    
    // Prevent body scroll when menu is open
    if (mobileMenu.classList.contains('active')) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }
  
  // Close mobile menu when clicking outside
  document.addEventListener('click', function(event) {
    const mobileMenu = document.getElementById('mobileMenu');
    const menuButton = document.querySelector('.ftac-header__mobile-menu');
    
    if (!mobileMenu.contains(event.target) && !menuButton.contains(event.target)) {
      mobileMenu.classList.remove('active');
      document.body.style.overflow = '';
    }
  });
</script>

{% schema %}
{
  "name": "FTAC Header",
  "settings": [
    {
      "type": "header",
      "content": "Future Tech Academy Club Header"
    },
    {
      "type": "paragraph",
      "content": "Custom header for Future Tech Academy Club with brand-specific navigation and styling."
    }
  ]
}
{% endschema %}
