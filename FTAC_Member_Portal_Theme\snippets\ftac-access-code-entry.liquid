{% comment %}
  FTAC Product Access Code Entry Component
  
  This snippet provides a form for customers to enter access codes
  for specific products. It can be embedded on product pages or
  in the member dashboard.
  
  Parameters:
  - product_handle: The handle of the product to unlock
  - product_name: Display name of the product
  - customer: Current customer object
  - redirect_url: Where to redirect after successful code entry (optional)
  
  Usage:
  {% render 'ftac-access-code-entry', 
     product_handle: product.handle, 
     product_name: product.title,
     customer: customer,
     redirect_url: product.url
  %}
{% endcomment %}

{% assign product_handle = product_handle | default: 'unknown-product' %}
{% assign product_name = product_name | default: 'Premium Content' %}
{% assign customer = customer %}
{% assign redirect_url = redirect_url | default: '/pages/member-dashboard' %}

{% comment %} Check if customer is logged in {% endcomment %}
{% unless customer %}
  <div class="ftac-access-code-entry ftac-access-code-entry--login-required">
    <div class="ftac-access-code-entry__content">
      <h3 class="ftac-access-code-entry__title">Sign In Required</h3>
      <p class="ftac-access-code-entry__message">
        Please sign in to your account to unlock this content with your access code.
      </p>
      <a href="/pages/member-access" class="ftac-access-code-entry__button ftac-access-code-entry__button--primary">
        Sign In to Continue
      </a>
    </div>
  </div>
{% else %}
  {% comment %} Check if customer already has access to this product {% endcomment %}
  {% render 'ftac-product-access', action: 'check_access', customer: customer, product_handle: product_handle %}
  {% assign has_access = ftac_access_result %}
  
  {% if has_access %}
    <div class="ftac-access-code-entry ftac-access-code-entry--has-access">
      <div class="ftac-access-code-entry__content">
        <div class="ftac-access-code-entry__success-icon">✓</div>
        <h3 class="ftac-access-code-entry__title">Access Granted</h3>
        <p class="ftac-access-code-entry__message">
          You have access to <strong>{{ product_name }}</strong>. Enjoy your premium content!
        </p>
        <a href="{{ redirect_url }}" class="ftac-access-code-entry__button ftac-access-code-entry__button--primary">
          View Content
        </a>
      </div>
    </div>
  {% else %}
    <div class="ftac-access-code-entry ftac-access-code-entry--needs-code">
      <div class="ftac-access-code-entry__content">
        <h3 class="ftac-access-code-entry__title">Enter Access Code</h3>
        <p class="ftac-access-code-entry__message">
          Enter your access code to unlock <strong>{{ product_name }}</strong>
        </p>
        
        <form class="ftac-access-code-entry__form" id="access-code-form-{{ product_handle }}">
          <input type="hidden" name="product_handle" value="{{ product_handle }}">
          <input type="hidden" name="product_name" value="{{ product_name }}">
          <input type="hidden" name="customer_id" value="{{ customer.id }}">
          <input type="hidden" name="redirect_url" value="{{ redirect_url }}">
          
          <div class="ftac-access-code-entry__field">
            <label for="access-code-{{ product_handle }}" class="ftac-access-code-entry__label">
              Access Code for {{ product_name }}
            </label>
            <input 
              type="text" 
              id="access-code-{{ product_handle }}" 
              name="access_code" 
              class="ftac-access-code-entry__input" 
              placeholder="FTAC-XXXX-YYYY-ZZZZ"
              pattern="[Ff][Tt][Aa][Cc]-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}"
              maxlength="19"
              required
            >
            <p class="ftac-access-code-entry__help">
              Found in your welcome email after purchasing this product
            </p>
          </div>
          
          <div class="ftac-access-code-entry__error" id="error-{{ product_handle }}" style="display: none;"></div>
          <div class="ftac-access-code-entry__success" id="success-{{ product_handle }}" style="display: none;"></div>
          
          <button type="submit" class="ftac-access-code-entry__button ftac-access-code-entry__button--primary">
            Unlock {{ product_name }}
          </button>
          
          <div class="ftac-access-code-entry__loading" id="loading-{{ product_handle }}" style="display: none;">
            <div class="ftac-access-code-entry__spinner"></div>
            <span>Verifying access code...</span>
          </div>
        </form>
        
        <div class="ftac-access-code-entry__support">
          <p class="ftac-access-code-entry__support-text">
            Don't have an access code? 
            <a href="/pages/contact-support" class="ftac-access-code-entry__support-link">
              Contact Sarah for help
            </a>
          </p>
        </div>
      </div>
    </div>
  {% endif %}
{% endunless %}

<style>
  .ftac-access-code-entry {
    background-color: white;
    border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.1);
    border-radius: var(--ftac-radius-xl);
    padding: var(--ftac-space-8);
    margin: var(--ftac-space-6) 0;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .ftac-access-code-entry--has-access {
    border-color: var(--ftac-learning-green);
    background: linear-gradient(135deg, #f0fdf4, white);
  }
  
  .ftac-access-code-entry--needs-code {
    border-color: var(--ftac-academy-blue);
  }
  
  .ftac-access-code-entry--login-required {
    border-color: var(--ftac-dusty-rose);
    background: linear-gradient(135deg, var(--ftac-warm-cream), white);
  }
  
  .ftac-access-code-entry__content {
    max-width: 500px;
    margin: 0 auto;
  }
  
  .ftac-access-code-entry__success-icon {
    width: 60px;
    height: 60px;
    background-color: var(--ftac-learning-green);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--ftac-text-2xl);
    font-weight: bold;
    margin: 0 auto var(--ftac-space-4);
  }
  
  .ftac-access-code-entry__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-access-code-entry__message {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-6);
    line-height: 1.6;
  }
  
  .ftac-access-code-entry__form {
    text-align: left;
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-access-code-entry__field {
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-access-code-entry__label {
    display: block;
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-2);
  }
  
  .ftac-access-code-entry__input {
    width: 100%;
    padding: var(--ftac-space-4);
    border: 2px solid rgba(var(--ftac-charcoal-rgb), 0.2);
    border-radius: var(--ftac-radius-lg);
    font-family: 'Courier New', monospace;
    font-size: var(--ftac-text-lg);
    letter-spacing: 2px;
    text-transform: uppercase;
    text-align: center;
    color: var(--ftac-charcoal);
    background-color: white;
    transition: all 0.2s ease;
  }
  
  .ftac-access-code-entry__input:focus {
    outline: none;
    border-color: var(--ftac-academy-blue);
    box-shadow: 0 0 0 3px rgba(var(--ftac-academy-blue-rgb), 0.1);
  }
  
  .ftac-access-code-entry__help {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    color: var(--ftac-charcoal);
    opacity: 0.7;
    margin-top: var(--ftac-space-2);
    text-align: center;
  }
  
  .ftac-access-code-entry__button {
    display: inline-block;
    background-color: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-4) var(--ftac-space-8);
    border: none;
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--ftac-shadow-lg);
  }
  
  .ftac-access-code-entry__button:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
    box-shadow: var(--ftac-shadow-xl);
  }
  
  .ftac-access-code-entry__button:disabled {
    background-color: rgba(var(--ftac-charcoal-rgb), 0.3);
    cursor: not-allowed;
    transform: none;
  }
  
  .ftac-access-code-entry__error {
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: var(--ftac-space-3);
    border-radius: var(--ftac-radius-lg);
    margin-bottom: var(--ftac-space-4);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
  }
  
  .ftac-access-code-entry__success {
    background-color: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: var(--ftac-learning-green);
    padding: var(--ftac-space-3);
    border-radius: var(--ftac-radius-lg);
    margin-bottom: var(--ftac-space-4);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
  }
  
  .ftac-access-code-entry__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--ftac-space-2);
    color: var(--ftac-academy-blue);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    margin-top: var(--ftac-space-4);
  }
  
  .ftac-access-code-entry__spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.2);
    border-top: 2px solid var(--ftac-academy-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .ftac-access-code-entry__support {
    border-top: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
    padding-top: var(--ftac-space-4);
  }
  
  .ftac-access-code-entry__support-text {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    color: var(--ftac-charcoal);
    opacity: 0.7;
  }
  
  .ftac-access-code-entry__support-link {
    color: var(--ftac-academy-blue);
    text-decoration: none;
    font-weight: var(--ftac-font-medium);
  }
  
  .ftac-access-code-entry__support-link:hover {
    text-decoration: underline;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Handle access code entry forms
  const accessCodeForms = document.querySelectorAll('[id^="access-code-form-"]');

  accessCodeForms.forEach(form => {
    const productHandle = form.querySelector('input[name="product_handle"]').value;
    const accessCodeInput = form.querySelector('input[name="access_code"]');
    const submitButton = form.querySelector('button[type="submit"]');
    const errorDiv = document.getElementById('error-' + productHandle);
    const successDiv = document.getElementById('success-' + productHandle);
    const loadingDiv = document.getElementById('loading-' + productHandle);

    // Format access code input
    accessCodeInput.addEventListener('input', function(e) {
      let value = e.target.value.replace(/[^A-Za-z0-9]/g, '').toUpperCase();
      if (value.length > 0) {
        // Add FTAC prefix if not present
        if (!value.startsWith('FTAC')) {
          value = 'FTAC' + value;
        }
        // Format with dashes
        value = value.replace(/(.{4})(.{4})?(.{4})?(.{4})?/, function(match, p1, p2, p3, p4) {
          let result = p1;
          if (p2) result += '-' + p2;
          if (p3) result += '-' + p3;
          if (p4) result += '-' + p4;
          return result;
        });
      }
      e.target.value = value;
    });

    // Handle form submission
    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const formData = new FormData(form);
      const accessCode = formData.get('access_code');
      const productName = formData.get('product_name');
      const redirectUrl = formData.get('redirect_url');

      // Hide previous messages
      errorDiv.style.display = 'none';
      successDiv.style.display = 'none';

      // Show loading state
      submitButton.disabled = true;
      loadingDiv.style.display = 'flex';

      // Validate access code format
      const codePattern = /^FTAC-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
      if (!codePattern.test(accessCode)) {
        showError('Please enter a valid access code in the format FTAC-XXXX-YYYY-ZZZZ');
        return;
      }

      // TODO: Implement actual access code verification
      // This would typically involve:
      // 1. AJAX call to a Shopify app endpoint or webhook
      // 2. Verify the access code against the product
      // 3. Update customer metafields if valid
      // 4. Return success/error response

      // For now, simulate the process
      setTimeout(function() {
        // Demo validation - in production, this would be server-side
        if (accessCode.includes('DEMO') || accessCode.includes('TEST')) {
          showSuccess('Access code verified! You now have access to ' + productName + '. Redirecting...');
          setTimeout(function() {
            window.location.href = redirectUrl;
          }, 2000);
        } else {
          showError('Invalid access code for this product. Please check your welcome email or contact Sarah for help.');
        }
      }, 2000);
    });

    function showError(message) {
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      submitButton.disabled = false;
      loadingDiv.style.display = 'none';
    }

    function showSuccess(message) {
      successDiv.textContent = message;
      successDiv.style.display = 'block';
      loadingDiv.style.display = 'none';
    }
  });
});
</script>
