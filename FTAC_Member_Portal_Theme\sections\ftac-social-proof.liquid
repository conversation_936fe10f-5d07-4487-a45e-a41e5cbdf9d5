{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}
{{ 'luxury-components.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  /* Social Proof Section */
  .ftac-social-proof {
    background: linear-gradient(135deg, var(--ftac-cream-base) 0%, #FFFEF7 50%, var(--ftac-cream-base) 100%);
    position: relative;
    overflow: hidden;
  }

  .ftac-social-proof::before {
    content: '';
    position: absolute;
    top: 10%;
    left: 5%;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    border-radius: 50%;
    opacity: 0.05;
    animation: ftacFloat 15s ease-in-out infinite;
  }

  .ftac-social-proof::after {
    content: '';
    position: absolute;
    bottom: 15%;
    right: 8%;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--ftac-sage-accent), #70E0A4);
    border-radius: 50%;
    opacity: 0.05;
    animation: ftacFloat 20s ease-in-out infinite reverse;
  }

  @keyframes ftacFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(10deg); }
  }

  .ftac-social-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
  }

  .ftac-social-title {
    font-family: 'Playfair Display', serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 1rem;
  }

  .ftac-social-subtitle {
    font-family: 'Open Sans', sans-serif;
    font-size: 1.2rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto;
  }

  /* Stats Section */
  .ftac-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
    position: relative;
    z-index: 2;
  }

  .ftac-stat-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 2rem 1.5rem;
    text-align: center;
    border: 1px solid rgba(255, 200, 221, 0.2);
    box-shadow: 0 15px 35px rgba(255, 200, 221, 0.1);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
  }

  .ftac-stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(255, 200, 221, 0.2);
  }

  .ftac-stat-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(255, 200, 221, 0.05), transparent);
    transform: rotate(45deg);
    animation: shimmer 6s ease-in-out infinite;
  }

  @keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
  }

  .ftac-stat-number {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: var(--ftac-blush-primary);
    display: block;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
  }

  .ftac-stat-label {
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem;
    color: var(--ftac-charcoal-text);
    font-weight: 600;
    position: relative;
    z-index: 2;
  }

  /* Gallery Grid */
  .ftac-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    position: relative;
    z-index: 2;
  }

  .ftac-gallery-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    overflow: hidden;
    border: 1px solid rgba(255, 200, 221, 0.2);
    box-shadow: 0 15px 35px rgba(255, 200, 221, 0.1);
    transition: all 0.4s ease;
    position: relative;
  }

  .ftac-gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(255, 200, 221, 0.2);
  }

  .ftac-gallery-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.4s ease;
  }

  .ftac-gallery-item:hover .ftac-gallery-image {
    transform: scale(1.05);
  }

  .ftac-gallery-content {
    padding: 1.5rem;
  }

  .ftac-gallery-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 0.5rem;
  }

  .ftac-gallery-description {
    font-family: 'Open Sans', sans-serif;
    color: var(--ftac-charcoal-text);
    opacity: 0.8;
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  .ftac-gallery-author {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .ftac-gallery-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: 1.1rem;
  }

  .ftac-gallery-author-info {
    flex: 1;
  }

  .ftac-gallery-author-name {
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    color: var(--ftac-charcoal-text);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }

  .ftac-gallery-author-role {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.8rem;
    color: var(--ftac-blush-primary);
    font-weight: 600;
  }

  /* Reviews Carousel */
  .ftac-reviews-carousel {
    position: relative;
    overflow: hidden;
    margin-bottom: 3rem;
  }

  .ftac-reviews-track {
    display: flex;
    gap: 2rem;
    transition: transform 0.5s ease;
  }

  .ftac-review-card {
    min-width: 350px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 2rem;
    border: 1px solid rgba(255, 200, 221, 0.2);
    box-shadow: 0 15px 35px rgba(255, 200, 221, 0.1);
    position: relative;
  }

  .ftac-review-stars {
    color: var(--ftac-peach-glow);
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .ftac-review-text {
    font-family: 'Open Sans', sans-serif;
    color: var(--ftac-charcoal-text);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-style: italic;
  }

  .ftac-review-author {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .ftac-review-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--ftac-sage-accent), #70E0A4);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: 1.2rem;
  }

  .ftac-review-author-info {
    flex: 1;
  }

  .ftac-review-author-name {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 0.25rem;
  }

  .ftac-review-author-location {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: var(--ftac-sage-accent);
    font-weight: 600;
  }

  /* Carousel Controls */
  .ftac-carousel-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
  }

  .ftac-carousel-btn {
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(255, 200, 221, 0.3);
  }

  .ftac-carousel-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(255, 200, 221, 0.4);
  }

  /* Responsive Design */
  @media screen and (max-width: 768px) {
    .ftac-stats-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }
    
    .ftac-gallery-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
    
    .ftac-review-card {
      min-width: 280px;
    }
    
    .ftac-stat-card {
      padding: 1.5rem 1rem;
    }
    
    .ftac-stat-number {
      font-size: 2.5rem;
    }
  }
{%- endstyle -%}

<div class="ftac-social-proof section-{{ section.id }}-padding">
  <div class="page-width">
    <div class="ftac-social-header">
      <h2 class="ftac-social-title">{{ section.settings.heading | default: "Join Our Creative Community" }}</h2>
      <p class="ftac-social-subtitle">{{ section.settings.subheading | default: "See what our amazing members are creating and achieving" }}</p>
    </div>

    {% if section.settings.show_stats %}
      <div class="ftac-stats-grid">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'stat' %}
              <div class="ftac-stat-card" {{ block.shopify_attributes }}>
                <span class="ftac-stat-number">{{ block.settings.number | default: "1000+" }}</span>
                <span class="ftac-stat-label">{{ block.settings.label | default: "Happy Customers" }}</span>
              </div>
          {% endcase %}
        {% endfor %}
      </div>
    {% endif %}

    {% if section.settings.show_gallery %}
      <div class="ftac-gallery-grid">
        {% for block in section.blocks %}
          {% case block.type %}
            {% when 'gallery_item' %}
              <div class="ftac-gallery-item" {{ block.shopify_attributes }}>
                {% if block.settings.image %}
                  <img src="{{ block.settings.image | image_url: width: 400 }}" 
                       alt="{{ block.settings.title }}" 
                       class="ftac-gallery-image"
                       loading="lazy">
                {% else %}
                  <div class="ftac-gallery-image" style="background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow)); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem;">🎨</div>
                {% endif %}
                <div class="ftac-gallery-content">
                  <h3 class="ftac-gallery-title">{{ block.settings.title | default: "Amazing Creation" }}</h3>
                  <p class="ftac-gallery-description">{{ block.settings.description | default: "A beautiful piece created by our talented community member." }}</p>
                  <div class="ftac-gallery-author">
                    <div class="ftac-gallery-avatar">{{ block.settings.author_name | default: "A" | slice: 0 }}</div>
                    <div class="ftac-gallery-author-info">
                      <div class="ftac-gallery-author-name">{{ block.settings.author_name | default: "Anonymous" }}</div>
                      <div class="ftac-gallery-author-role">{{ block.settings.author_role | default: "Community Member" }}</div>
                    </div>
                  </div>
                </div>
              </div>
          {% endcase %}
        {% endfor %}
      </div>
    {% endif %}

    {% if section.settings.show_reviews %}
      <div class="ftac-reviews-carousel">
        <div class="ftac-reviews-track" id="reviews-track">
          {% for block in section.blocks %}
            {% case block.type %}
              {% when 'review' %}
                <div class="ftac-review-card" {{ block.shopify_attributes }}>
                  <div class="ftac-review-stars">★★★★★</div>
                  <p class="ftac-review-text">"{{ block.settings.review_text | default: "This has completely transformed my creative process. Highly recommended!" }}"</p>
                  <div class="ftac-review-author">
                    <div class="ftac-review-avatar">{{ block.settings.reviewer_name | default: "A" | slice: 0 }}</div>
                    <div class="ftac-review-author-info">
                      <div class="ftac-review-author-name">{{ block.settings.reviewer_name | default: "Anonymous" }}</div>
                      <div class="ftac-review-author-location">{{ block.settings.reviewer_location | default: "Verified Customer" }}</div>
                    </div>
                  </div>
                </div>
            {% endcase %}
          {% endfor %}
        </div>
        
        <div class="ftac-carousel-controls">
          <button class="ftac-carousel-btn" id="prev-btn">‹</button>
          <button class="ftac-carousel-btn" id="next-btn">›</button>
        </div>
      </div>
    {% endif %}
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Reviews Carousel
  const track = document.getElementById('reviews-track');
  const prevBtn = document.getElementById('prev-btn');
  const nextBtn = document.getElementById('next-btn');
  
  if (track && prevBtn && nextBtn) {
    let currentIndex = 0;
    const cards = track.querySelectorAll('.ftac-review-card');
    const cardWidth = cards[0] ? cards[0].offsetWidth + 32 : 382; // 350px + 2rem gap
    
    function updateCarousel() {
      const translateX = -currentIndex * cardWidth;
      track.style.transform = `translateX(${translateX}px)`;
    }
    
    prevBtn.addEventListener('click', () => {
      currentIndex = Math.max(0, currentIndex - 1);
      updateCarousel();
    });
    
    nextBtn.addEventListener('click', () => {
      const maxIndex = Math.max(0, cards.length - Math.floor(track.offsetWidth / cardWidth));
      currentIndex = Math.min(maxIndex, currentIndex + 1);
      updateCarousel();
    });
    
    // Auto-scroll
    setInterval(() => {
      const maxIndex = Math.max(0, cards.length - Math.floor(track.offsetWidth / cardWidth));
      currentIndex = currentIndex >= maxIndex ? 0 : currentIndex + 1;
      updateCarousel();
    }, 5000);
  }
});
</script>

{% schema %}
{
  "name": "Social Proof Gallery",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Join Our Creative Community",
      "label": "Heading"
    },
    {
      "type": "textarea",
      "id": "subheading",
      "default": "See what our amazing members are creating and achieving",
      "label": "Subheading"
    },
    {
      "type": "checkbox",
      "id": "show_stats",
      "default": true,
      "label": "Show statistics"
    },
    {
      "type": "checkbox",
      "id": "show_gallery",
      "default": true,
      "label": "Show member gallery"
    },
    {
      "type": "checkbox",
      "id": "show_reviews",
      "default": true,
      "label": "Show reviews carousel"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 60
    }
  ],
  "blocks": [
    {
      "type": "stat",
      "name": "Statistic",
      "settings": [
        {
          "type": "text",
          "id": "number",
          "default": "1000+",
          "label": "Number"
        },
        {
          "type": "text",
          "id": "label",
          "default": "Happy Customers",
          "label": "Label"
        }
      ]
    },
    {
      "type": "gallery_item",
      "name": "Gallery Item",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Amazing Creation",
          "label": "Title"
        },
        {
          "type": "textarea",
          "id": "description",
          "default": "A beautiful piece created by our talented community member.",
          "label": "Description"
        },
        {
          "type": "text",
          "id": "author_name",
          "default": "Sarah Johnson",
          "label": "Author name"
        },
        {
          "type": "text",
          "id": "author_role",
          "default": "Community Member",
          "label": "Author role"
        }
      ]
    },
    {
      "type": "review",
      "name": "Review",
      "settings": [
        {
          "type": "textarea",
          "id": "review_text",
          "default": "This has completely transformed my creative process. Highly recommended!",
          "label": "Review text"
        },
        {
          "type": "text",
          "id": "reviewer_name",
          "default": "Emma Rodriguez",
          "label": "Reviewer name"
        },
        {
          "type": "text",
          "id": "reviewer_location",
          "default": "Creative Entrepreneur",
          "label": "Reviewer location/role"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Social Proof Gallery",
      "blocks": [
        {
          "type": "stat",
          "settings": {
            "number": "2,500+",
            "label": "Happy Members"
          }
        },
        {
          "type": "stat",
          "settings": {
            "number": "50,000+",
            "label": "Creations Made"
          }
        },
        {
          "type": "stat",
          "settings": {
            "number": "98%",
            "label": "Satisfaction Rate"
          }
        },
        {
          "type": "stat",
          "settings": {
            "number": "24/7",
            "label": "Support Available"
          }
        },
        {
          "type": "gallery_item",
          "settings": {
            "title": "Digital Art Masterpiece",
            "description": "Created using our premium digital art bundle. The attention to detail is incredible!",
            "author_name": "Sarah Chen",
            "author_role": "Digital Artist"
          }
        },
        {
          "type": "gallery_item",
          "settings": {
            "title": "Handcrafted Jewelry",
            "description": "Beautiful handmade jewelry inspired by our design templates and tutorials.",
            "author_name": "Maria Rodriguez",
            "author_role": "Jewelry Designer"
          }
        },
        {
          "type": "gallery_item",
          "settings": {
            "title": "Custom Illustration",
            "description": "A stunning custom illustration created for a client using our professional techniques.",
            "author_name": "Alex Thompson",
            "author_role": "Freelance Illustrator"
          }
        },
        {
          "type": "review",
          "settings": {
            "review_text": "The quality of resources and community support is outstanding. I've grown my creative business by 300% since joining!",
            "reviewer_name": "Emma Johnson",
            "reviewer_location": "Creative Entrepreneur, California"
          }
        },
        {
          "type": "review",
          "settings": {
            "review_text": "I was skeptical at first, but this platform has completely transformed how I approach my art. The tutorials are world-class.",
            "reviewer_name": "Michael Chen",
            "reviewer_location": "Professional Artist, New York"
          }
        },
        {
          "type": "review",
          "settings": {
            "review_text": "The community aspect is what sets this apart. Getting feedback and inspiration from other creators is invaluable.",
            "reviewer_name": "Sophie Williams",
            "reviewer_location": "Graphic Designer, London"
          }
        }
      ]
    }
  ]
}
{% endschema %}
