{{ 'component-image-with-text.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  if section.settings.image_layout contains 'right'
    assign odd_class = ' image-with-text__grid--reverse'
  else
    assign even_class = ' image-with-text__grid--reverse'
  endif

  if section.settings.row_color_scheme == section.settings.section_color_scheme
    assign no_content_background = true
  endif

  if settings.text_boxes_shadow_opacity == 0 and settings.text_boxes_border_thickness == 0 or settings.text_boxes_border_opacity == 0
    assign no_content_styles = true
  endif

  if settings.text_boxes_border_thickness > 0 and settings.text_boxes_border_opacity > 0 and settings.media_border_thickness > 0 and settings.media_border_opacity > 0
    assign borders_class = ' collapse-borders'
  endif

  if no_content_background and no_content_styles
    assign padding_class = ' collapse-padding'
  endif

  unless no_content_background and no_content_styles
    assign corners_class = ' collapse-corners'
  endunless
-%}

<div class="multirow section-{{ section.id }}-padding gradient color-{{ section.settings.section_color_scheme }}">
  <div class="multirow__inner page-width">
    {%- for block in section.blocks -%}
      <div
        class="image-with-text isolate{{ borders_class }}{{ corners_class }}{{ padding_class }}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--slide-in{% endif %}"
        {{ block.shopify_attributes }}
      >
        <div class="image-with-text__grid grid grid--gapless grid--1-col grid--{% if section.settings.desktop_image_width == 'medium' %}2-col-tablet{% else %}3-col-tablet{% endif %}{% if section.settings.image_layout contains 'alternate' %}{% cycle odd_class, even_class %}{% else %}{{ odd_class }}{% endif %}">
          <div class="image-with-text__media-item image-with-text__media-item--{{ section.settings.desktop_image_width }} image-with-text__media-item--{{ section.settings.desktop_content_position }} grid__item">
            <div
              class="image-with-text__media image-with-text__media--{{ section.settings.image_height }} gradient color-{{ section.settings.row_color_scheme }} global-media-settings{% if block.settings.image != blank %} media{% else %} image-with-text__media--placeholder placeholder{% endif %}"
              {% if section.settings.image_height == 'adapt' and block.settings.image != blank %}
                style="padding-bottom: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%;"
              {% endif %}
            >
              {%- if block.settings.image != blank -%}
                {%- capture sizes -%}
                  (min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px,
                  (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)
                {%- endcapture -%}
                {{
                  block.settings.image
                  | image_url: width: 1500
                  | image_tag: sizes: sizes, widths: '165, 360, 535, 750, 1070, 1500'
                }}
              {%- else -%}
                {{ 'detailed-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
              {%- endif -%}
            </div>
          </div>
          <div class="image-with-text__text-item grid__item">
            <div class="image-with-text__content image-with-text__content--{{ section.settings.desktop_content_position }} image-with-text__content--desktop-{{ section.settings.desktop_content_alignment }} image-with-text__content--mobile-{{ section.settings.mobile_content_alignment }} image-with-text__content--{{ section.settings.image_height }} content-container{% unless no_content_background and no_content_styles %} gradient color-{{ section.settings.row_color_scheme }}{% else %} background-transparent{% endunless %}">
              {%- if block.settings.caption -%}
                <p class="image-with-text__text image-with-text__text--caption caption-with-letter-spacing caption-with-letter-spacing--medium">
                  {{ block.settings.caption | escape }}
                </p>
              {%- endif -%}
              {%- if block.settings.heading -%}
                <h2 class="image-with-text__heading {{ section.settings.heading_size }} rte">
                  {{ block.settings.heading }}
                </h2>
              {%- endif -%}
              {%- if block.settings.text -%}
                <div class="image-with-text__text rte {{ section.settings.text_style }}">{{ block.settings.text }}</div>
              {%- endif -%}
              {%- if block.settings.button_label != blank -%}
                <a
                  {% if block.settings.button_link == blank %}
                    role="link" aria-disabled="true"
                  {% else %}
                    href="{{ block.settings.button_link }}"
                  {% endif %}
                  class="button button--{{ section.settings.button_style }}"
                >
                  {{ block.settings.button_label | escape }}
                </a>
              {%- endif -%}
            </div>
          </div>
        </div>
      </div>
    {%- endfor -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.multirow.name",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "header",
      "content": "t:sections.multirow.settings.header.content"
    },
    {
      "type": "select",
      "id": "image_height",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.multirow.settings.image_height.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.multirow.settings.image_height.options__2.label"
        },
        {
          "value": "medium",
          "label": "t:sections.multirow.settings.image_height.options__3.label"
        },
        {
          "value": "large",
          "label": "t:sections.multirow.settings.image_height.options__4.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.multirow.settings.image_height.label"
    },
    {
      "type": "select",
      "id": "desktop_image_width",
      "options": [
        {
          "value": "small",
          "label": "t:sections.multirow.settings.desktop_image_width.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.multirow.settings.desktop_image_width.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.multirow.settings.desktop_image_width.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.multirow.settings.desktop_image_width.label"
    },
    {
      "type": "select",
      "id": "image_layout",
      "options": [
        {
          "value": "alternate-left",
          "label": "t:sections.multirow.settings.image_layout.options__1.label"
        },
        {
          "value": "alternate-right",
          "label": "t:sections.multirow.settings.image_layout.options__2.label"
        },
        {
          "value": "align-left",
          "label": "t:sections.multirow.settings.image_layout.options__3.label"
        },
        {
          "value": "align-right",
          "label": "t:sections.multirow.settings.image_layout.options__4.label"
        }
      ],
      "default": "alternate-left",
      "label": "t:sections.multirow.settings.image_layout.label"
    },
    {
      "type": "header",
      "content": "t:sections.multirow.settings.header_2.content"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        },
        {
          "value": "hxl",
          "label": "t:sections.all.heading_size.options__4.label"
        },
        {
          "value": "hxxl",
          "label": "t:sections.all.heading_size.options__5.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "select",
      "id": "text_style",
      "options": [
        {
          "value": "body",
          "label": "t:sections.multirow.settings.text_style.options__1.label"
        },
        {
          "value": "subtitle",
          "label": "t:sections.multirow.settings.text_style.options__2.label"
        }
      ],
      "default": "body",
      "label": "t:sections.multirow.settings.text_style.label"
    },
    {
      "type": "select",
      "id": "button_style",
      "options": [
        {
          "value": "primary",
          "label": "t:sections.multirow.settings.button_style.options__1.label"
        },
        {
          "value": "secondary",
          "label": "t:sections.multirow.settings.button_style.options__2.label"
        }
      ],
      "default": "secondary",
      "label": "t:sections.multirow.settings.button_style.label"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "top",
          "label": "t:sections.multirow.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "middle",
          "label": "t:sections.multirow.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "bottom",
          "label": "t:sections.multirow.settings.desktop_content_position.options__3.label"
        }
      ],
      "default": "middle",
      "label": "t:sections.multirow.settings.desktop_content_position.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multirow.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multirow.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.multirow.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.multirow.settings.desktop_content_alignment.label"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.multirow.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.multirow.settings.mobile_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.multirow.settings.mobile_content_alignment.options__3.label"
        }
      ],
      "default": "left",
      "label": "t:sections.multirow.settings.mobile_content_alignment.label"
    },
    {
      "type": "header",
      "content": "t:sections.multirow.settings.header_3.content"
    },
    {
      "type": "color_scheme",
      "id": "section_color_scheme",
      "label": "t:sections.all.colors.label",
      "default": "scheme-1"
    },
    {
      "type": "color_scheme",
      "id": "row_color_scheme",
      "label": "t:sections.multirow.settings.container_color_scheme.label",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "row",
      "name": "t:sections.multirow.blocks.row.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.multirow.blocks.row.settings.image.label"
        },
        {
          "type": "text",
          "id": "caption",
          "default": "t:sections.multirow.blocks.row.settings.caption.default",
          "label": "t:sections.multirow.blocks.row.settings.caption.label"
        },
        {
          "type": "inline_richtext",
          "id": "heading",
          "default": "t:sections.multirow.blocks.row.settings.heading.default",
          "label": "t:sections.multirow.blocks.row.settings.heading.label"
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "t:sections.multirow.blocks.row.settings.text.default",
          "label": "t:sections.multirow.blocks.row.settings.text.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "t:sections.multirow.blocks.row.settings.button_label.default",
          "label": "t:sections.multirow.blocks.row.settings.button_label.label", 
          "info": "t:sections.multirow.blocks.row.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "t:sections.multirow.blocks.row.settings.button_link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.multirow.presets.name",
      "blocks": [
        {
          "type": "row"
        },
        {
          "type": "row"
        },
        {
          "type": "row"
        }
      ]
    }
  ]
}
{% endschema %}
