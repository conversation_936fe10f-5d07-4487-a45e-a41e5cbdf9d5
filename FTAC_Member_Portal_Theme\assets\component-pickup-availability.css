pickup-availability {
  display: block;
}

pickup-availability[available] {
  min-height: 8rem;
}

.pickup-availability-preview {
  align-items: flex-start;
  display: flex;
  gap: 0.2rem;
  padding: 1rem 2rem 0 0;
}

.pickup-availability-preview .icon {
  flex-shrink: 0;
  height: 1.8rem;
}

.pickup-availability-preview .icon-unavailable {
  height: 1.6rem;
  margin-top: 0.1rem;
}

.pickup-availability-button {
  background-color: transparent;
  color: rgba(var(--color-foreground), 0.75);
  letter-spacing: 0.06rem;
  padding: 0 0 0.2rem;
  text-align: left;
  text-decoration: underline;
}

.pickup-availability-button:hover {
  color: rgb(var(--color-foreground));
}

.pickup-availability-info * {
  margin: 0 0 0.6rem;
}

pickup-availability-drawer {
  background-color: rgb(var(--color-background));
  height: 100%;
  opacity: 0;
  overflow-y: auto;
  padding: 2rem;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 4;
  transition: opacity var(--duration-default) ease, transform var(--duration-default) ease;
  transform: translateX(100%);
  width: 100%;
  border-width: 0 0 0 var(--drawer-border-width);
  border-color: rgba(var(--color-foreground), var(--drawer-border-opacity));
  border-style: solid;
  filter: drop-shadow(
    var(--drawer-shadow-horizontal-offset) var(--drawer-shadow-vertical-offset) var(--drawer-shadow-blur-radius)
      rgba(var(--color-shadow), var(--drawer-shadow-opacity))
  );
}

pickup-availability-drawer[open] {
  transform: translateX(0);
  opacity: 1;
}

@media screen and (min-width: 750px) {
  pickup-availability-drawer {
    transform: translateX(100%);
    width: 37.5rem;
  }

  pickup-availability-drawer[open] {
    opacity: 1;
    transform: translateX(0);
    animation: animateDrawerOpen var(--duration-default) ease;
  }
}

.pickup-availability-header {
  align-items: flex-start;
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.2rem;
}

.pickup-availability-drawer-title {
  margin: 0.5rem 0 0;
}

.pickup-availability-header .icon {
  width: 2rem;
}

.pickup-availability-drawer-button {
  background-color: transparent;
  border: none;
  color: rgb(var(--color-foreground));
  cursor: pointer;
  display: block;
  height: 4.4rem;
  padding: 1.2rem;
  width: 4.4rem;
}

.pickup-availability-drawer-button:hover {
  color: rgba(var(--color-foreground), 0.75);
}

.pickup-availability-variant {
  font-size: 1.3rem;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
  margin: 0 0 1.2rem;
  text-transform: capitalize;
}

.pickup-availability-variant > * + strong {
  margin-left: 1rem;
}

.pickup-availability-list__item {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  padding: 2rem 0;
}

.pickup-availability-list__item:first-child {
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);
}

.pickup-availability-list__item > * {
  margin: 0;
}

.pickup-availability-list__item > * + * {
  margin-top: 1rem;
}

.pickup-availability-address {
  font-style: normal;
  font-size: 1.2rem;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
}

.pickup-availability-address p {
  margin: 0;
}

@keyframes animateDrawerOpen {
  @media screen and (max-width: 749px) {
    0% {
      opacity: 0;
      transform: translateX(100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @media screen and (min-width: 750px) {
    0% {
      opacity: 0;
      transform: translateX(100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
}
