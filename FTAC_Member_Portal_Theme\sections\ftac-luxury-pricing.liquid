{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}
{{ 'luxury-components.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  /* Luxury Pricing Section Styles */
  .ftac-luxury-pricing {
    background: linear-gradient(135deg, var(--ftac-cream-base) 0%, #FFFEF7 50%, var(--ftac-cream-base) 100%);
    position: relative;
    overflow: hidden;
  }

  .ftac-luxury-pricing::before {
    content: '';
    position: absolute;
    top: 5%;
    right: 10%;
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    border-radius: 50%;
    opacity: 0.02;
    animation: ftacFloat 20s ease-in-out infinite;
  }

  .ftac-luxury-pricing::after {
    content: '';
    position: absolute;
    bottom: 10%;
    left: 5%;
    width: 250px;
    height: 250px;
    background: linear-gradient(135deg, var(--ftac-sage-accent), #70E0A4);
    border-radius: 50%;
    opacity: 0.03;
    animation: ftacFloat 25s ease-in-out infinite reverse;
  }

  @keyframes ftacFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-40px) rotate(15deg); }
  }

  .ftac-pricing-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 2;
  }

  .ftac-pricing-title {
    font-family: 'Playfair Display', serif;
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .ftac-pricing-subtitle {
    font-family: 'Dancing Script', cursive;
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    color: var(--ftac-blush-primary);
    margin-bottom: 2rem;
    font-weight: 600;
  }

  .ftac-pricing-description {
    font-family: 'Open Sans', sans-serif;
    font-size: 1.1rem;
    color: var(--ftac-charcoal-text);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }

  .ftac-pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
  }

  .ftac-pricing-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 30px;
    padding: 3rem 2rem;
    border: 1px solid rgba(255, 200, 221, 0.2);
    box-shadow: 0 20px 40px rgba(255, 200, 221, 0.15);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    text-align: center;
  }

  .ftac-pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 30px 60px rgba(255, 200, 221, 0.25);
    border-color: var(--ftac-blush-primary);
  }

  .ftac-pricing-card.featured {
    border: 2px solid var(--ftac-blush-primary);
    transform: scale(1.05);
  }

  .ftac-pricing-card.featured::before {
    content: '✨ Most Popular ✨';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-family: 'Open Sans', sans-serif;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 8px 20px rgba(255, 200, 221, 0.4);
  }

  .ftac-pricing-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(255, 200, 221, 0.05), transparent);
    transform: rotate(45deg);
    animation: shimmer 4s ease-in-out infinite;
  }

  @keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
  }

  .ftac-pricing-tier {
    font-family: 'Dancing Script', cursive;
    font-size: 1.5rem;
    color: var(--ftac-blush-primary);
    margin-bottom: 1rem;
    font-weight: 600;
  }

  .ftac-pricing-name {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 0.5rem;
  }

  .ftac-pricing-tagline {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.8;
    margin-bottom: 2rem;
  }

  .ftac-pricing-price {
    margin-bottom: 2rem;
  }

  .ftac-pricing-amount {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    line-height: 1;
  }

  .ftac-pricing-period {
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.7;
  }

  .ftac-pricing-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
    text-align: left;
  }

  .ftac-pricing-feature {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.95rem;
    color: var(--ftac-charcoal-text);
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .ftac-pricing-feature-icon {
    color: var(--ftac-sage-accent);
    font-size: 1.2rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
  }

  .ftac-pricing-cta {
    margin-top: 2rem;
  }

  .ftac-pricing-button {
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    border: none;
    border-radius: 25px;
    padding: 1rem 2rem;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(255, 200, 221, 0.4);
    position: relative;
    overflow: hidden;
    width: 100%;
    text-decoration: none;
    display: inline-block;
  }

  .ftac-pricing-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 200, 221, 0.6);
    color: white;
    text-decoration: none;
  }

  .ftac-pricing-button.secondary {
    background: linear-gradient(135deg, var(--ftac-sage-accent), #70E0A4);
  }

  .ftac-pricing-guarantee {
    text-align: center;
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 200, 221, 0.2);
    position: relative;
    z-index: 2;
  }

  .ftac-pricing-guarantee-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.3rem;
    color: var(--ftac-blush-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
  }

  .ftac-pricing-guarantee-details {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.95rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.8;
  }

  /* Responsive Design */
  @media screen and (max-width: 768px) {
    .ftac-pricing-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
    
    .ftac-pricing-card {
      padding: 2rem 1.5rem;
    }
    
    .ftac-pricing-card.featured {
      transform: none;
    }
    
    .ftac-pricing-amount {
      font-size: 2.5rem;
    }
  }
{%- endstyle -%}

<div class="ftac-luxury-pricing section-{{ section.id }}-padding">
  <div class="page-width">
    <div class="ftac-pricing-header">
      <h2 class="ftac-pricing-title">{{ section.settings.heading | default: "Choose Your Journey" }}</h2>
      <p class="ftac-pricing-subtitle">{{ section.settings.subheading | default: "✨ Handcrafted Learning Experiences ✨" }}</p>
      <p class="ftac-pricing-description">{{ section.settings.description | default: "Transform your creative dreams into reality with our carefully curated digital resources and premium support." }}</p>
    </div>

    <div class="ftac-pricing-grid">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'pricing_tier' %}
            <div class="ftac-pricing-card {% if block.settings.featured %}featured{% endif %}" {{ block.shopify_attributes }}>
              <div class="ftac-pricing-tier">{{ block.settings.tier_label | default: "Starter" }}</div>
              <h3 class="ftac-pricing-name">{{ block.settings.plan_name | default: "Essential Bundle" }}</h3>
              <p class="ftac-pricing-tagline">{{ block.settings.tagline | default: "Perfect for beginners" }}</p>
              
              <div class="ftac-pricing-price">
                <span class="ftac-pricing-amount">${{ block.settings.price | default: "29" }}</span>
                <span class="ftac-pricing-period">{{ block.settings.period | default: "one-time" }}</span>
              </div>

              <ul class="ftac-pricing-features">
                {% if block.settings.feature_1 != blank %}
                  <li class="ftac-pricing-feature">
                    <span class="ftac-pricing-feature-icon">✨</span>
                    <span>{{ block.settings.feature_1 }}</span>
                  </li>
                {% endif %}
                {% if block.settings.feature_2 != blank %}
                  <li class="ftac-pricing-feature">
                    <span class="ftac-pricing-feature-icon">💖</span>
                    <span>{{ block.settings.feature_2 }}</span>
                  </li>
                {% endif %}
                {% if block.settings.feature_3 != blank %}
                  <li class="ftac-pricing-feature">
                    <span class="ftac-pricing-feature-icon">🌟</span>
                    <span>{{ block.settings.feature_3 }}</span>
                  </li>
                {% endif %}
                {% if block.settings.feature_4 != blank %}
                  <li class="ftac-pricing-feature">
                    <span class="ftac-pricing-feature-icon">🎯</span>
                    <span>{{ block.settings.feature_4 }}</span>
                  </li>
                {% endif %}
                {% if block.settings.feature_5 != blank %}
                  <li class="ftac-pricing-feature">
                    <span class="ftac-pricing-feature-icon">💝</span>
                    <span>{{ block.settings.feature_5 }}</span>
                  </li>
                {% endif %}
              </ul>

              <div class="ftac-pricing-cta">
                <a href="{{ block.settings.cta_url | default: '/collections/all' }}" 
                   class="ftac-pricing-button {% if block.settings.secondary_button %}secondary{% endif %}">
                  {{ block.settings.cta_text | default: "Get Started" }}
                </a>
              </div>
            </div>
        {% endcase %}
      {% endfor %}
    </div>

    {% if section.settings.show_guarantee %}
      <div class="ftac-pricing-guarantee">
        <p class="ftac-pricing-guarantee-text">💖 {{ section.settings.guarantee_title | default: "30-Day Happiness Guarantee" }}</p>
        <p class="ftac-pricing-guarantee-details">{{ section.settings.guarantee_text | default: "Love your purchase or get your money back. No questions asked." }}</p>
      </div>
    {% endif %}
  </div>
</div>

{% schema %}
{
  "name": "Luxury Pricing",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Choose Your Journey",
      "label": "Heading"
    },
    {
      "type": "text",
      "id": "subheading",
      "default": "✨ Handcrafted Learning Experiences ✨",
      "label": "Subheading"
    },
    {
      "type": "textarea",
      "id": "description",
      "default": "Transform your creative dreams into reality with our carefully curated digital resources and premium support.",
      "label": "Description"
    },
    {
      "type": "checkbox",
      "id": "show_guarantee",
      "default": true,
      "label": "Show guarantee section"
    },
    {
      "type": "text",
      "id": "guarantee_title",
      "default": "30-Day Happiness Guarantee",
      "label": "Guarantee title"
    },
    {
      "type": "textarea",
      "id": "guarantee_text",
      "default": "Love your purchase or get your money back. No questions asked.",
      "label": "Guarantee text"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 60
    }
  ],
  "blocks": [
    {
      "type": "pricing_tier",
      "name": "Pricing Tier",
      "settings": [
        {
          "type": "text",
          "id": "tier_label",
          "default": "Starter",
          "label": "Tier label"
        },
        {
          "type": "text",
          "id": "plan_name",
          "default": "Essential Bundle",
          "label": "Plan name"
        },
        {
          "type": "text",
          "id": "tagline",
          "default": "Perfect for beginners",
          "label": "Tagline"
        },
        {
          "type": "text",
          "id": "price",
          "default": "29",
          "label": "Price (numbers only)"
        },
        {
          "type": "text",
          "id": "period",
          "default": "one-time",
          "label": "Period"
        },
        {
          "type": "checkbox",
          "id": "featured",
          "default": false,
          "label": "Featured plan"
        },
        {
          "type": "text",
          "id": "feature_1",
          "label": "Feature 1"
        },
        {
          "type": "text",
          "id": "feature_2",
          "label": "Feature 2"
        },
        {
          "type": "text",
          "id": "feature_3",
          "label": "Feature 3"
        },
        {
          "type": "text",
          "id": "feature_4",
          "label": "Feature 4"
        },
        {
          "type": "text",
          "id": "feature_5",
          "label": "Feature 5"
        },
        {
          "type": "text",
          "id": "cta_text",
          "default": "Get Started",
          "label": "Button text"
        },
        {
          "type": "url",
          "id": "cta_url",
          "label": "Button URL"
        },
        {
          "type": "checkbox",
          "id": "secondary_button",
          "default": false,
          "label": "Use secondary button style"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Luxury Pricing",
      "blocks": [
        {
          "type": "pricing_tier",
          "settings": {
            "tier_label": "Starter",
            "plan_name": "Essential Bundle",
            "tagline": "Perfect for creative beginners",
            "price": "29",
            "period": "one-time",
            "feature_1": "5 Premium Digital Templates",
            "feature_2": "Basic Design Tutorials",
            "feature_3": "Email Support",
            "feature_4": "Commercial License",
            "cta_text": "Start Creating",
            "cta_url": "/collections/starter"
          }
        },
        {
          "type": "pricing_tier",
          "settings": {
            "tier_label": "Popular",
            "plan_name": "Creative Pro",
            "tagline": "For serious creators",
            "price": "79",
            "period": "one-time",
            "featured": true,
            "feature_1": "20 Premium Digital Templates",
            "feature_2": "Advanced Video Tutorials",
            "feature_3": "Priority Support",
            "feature_4": "Extended Commercial License",
            "feature_5": "Bonus Resource Pack",
            "cta_text": "Go Pro",
            "cta_url": "/collections/pro"
          }
        },
        {
          "type": "pricing_tier",
          "settings": {
            "tier_label": "Ultimate",
            "plan_name": "Master Collection",
            "tagline": "Everything you need",
            "price": "149",
            "period": "one-time",
            "feature_1": "50+ Premium Templates",
            "feature_2": "Complete Video Course",
            "feature_3": "1-on-1 Coaching Call",
            "feature_4": "Lifetime Updates",
            "feature_5": "Private Community Access",
            "cta_text": "Master It All",
            "cta_url": "/collections/master",
            "secondary_button": true
          }
        }
      ]
    }
  ]
}
{% endschema %}
