{{ 'base.css' | asset_url | stylesheet_tag }}
{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-shop-hero {
    background: linear-gradient(135deg, rgba(var(--ftac-dusty-rose-rgb), 0.1) 0%, rgba(var(--ftac-sage-green-rgb), 0.1) 100%);
    background-color: var(--ftac-warm-cream);
    color: var(--ftac-warm-taupe);
    padding: var(--ftac-space-20) 0;
    text-align: center;
    position: relative;
  }

  .ftac-shop-hero::before {
    content: '';
    position: absolute;
    top: 30%;
    left: 15%;
    width: 80px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, var(--ftac-dusty-rose) 50%, transparent 100%);
    opacity: 0.3;
  }

  .ftac-shop-hero__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-8);
  }

  .ftac-shop-hero__heading {
    font-family: var(--ftac-font-handwritten);
    font-size: var(--ftac-text-5xl);
    font-weight: 700;
    margin-bottom: var(--ftac-space-6);
    color: var(--ftac-warm-taupe);
  }

  .ftac-shop-hero__subheading {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-xl);
    line-height: 1.7;
    margin-bottom: var(--ftac-space-12);
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
    color: var(--ftac-warm-taupe);
  }

  .ftac-shop-hero__search {
    max-width: 550px;
    margin: 0 auto;
    position: relative;
  }

  .ftac-shop-hero__search-input {
    width: 100%;
    padding: var(--ftac-space-5) var(--ftac-space-8);
    padding-right: 70px;
    border: 2px solid rgba(var(--ftac-dusty-rose-rgb), 0.2);
    border-radius: var(--ftac-radius-2xl);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    background-color: white;
    color: var(--ftac-warm-taupe);
    box-shadow: 0 4px 20px rgba(var(--ftac-warm-taupe-rgb), 0.1);
    transition: all 0.3s ease;
  }

  .ftac-shop-hero__search-input:focus {
    outline: none;
    border-color: var(--ftac-dusty-rose);
    box-shadow: 0 6px 25px rgba(var(--ftac-dusty-rose-rgb), 0.2);
  }
  
  .ftac-shop-hero__search-button {
    position: absolute;
    right: var(--ftac-space-2);
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--ftac-academy-blue);
    color: white;
    border: none;
    border-radius: var(--ftac-radius-full);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .ftac-shop-hero__search-button:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.8);
  }
  
  .ftac-shop-hero__search-icon {
    width: 20px;
    height: 20px;
  }
  
  @media (min-width: 1024px) {
    .ftac-shop-hero__heading {
      font-size: var(--ftac-text-5xl);
    }
  }
</style>

<section class="ftac-shop-hero">
  <div class="ftac-shop-hero__container">
    <h1 class="ftac-shop-hero__heading">
      {{ section.settings.heading | default: "Premium Learning Bundles" }}
    </h1>
    <p class="ftac-shop-hero__subheading">
      {{ section.settings.subheading | default: "Transform your skills with our comprehensive tech education packages" }}
    </p>
    
    <div class="ftac-shop-hero__search">
      <input 
        type="text" 
        class="ftac-shop-hero__search-input" 
        placeholder="Search for courses, bundles, or topics..."
        id="shop-search-input"
      >
      <button class="ftac-shop-hero__search-button" onclick="performSearch()">
        <svg class="ftac-shop-hero__search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </button>
    </div>
  </div>
</section>

<script>
function performSearch() {
  const searchInput = document.getElementById('shop-search-input');
  const query = searchInput.value.trim();
  
  if (query) {
    // Scroll to products section and filter
    const productsSection = document.querySelector('.ftac-shop-products');
    if (productsSection) {
      productsSection.scrollIntoView({ behavior: 'smooth' });
      // Trigger search in products section
      const event = new CustomEvent('shopSearch', { detail: { query: query } });
      document.dispatchEvent(event);
    }
  }
}

// Allow Enter key to trigger search
document.getElementById('shop-search-input').addEventListener('keypress', function(e) {
  if (e.key === 'Enter') {
    performSearch();
  }
});
</script>

{% schema %}
{
  "name": "FTAC Shop Hero",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Premium Learning Bundles"
    },
    {
      "type": "textarea",
      "id": "subheading",
      "label": "Subheading",
      "default": "Transform your skills with our comprehensive tech education packages"
    }
  ],
  "presets": [
    {
      "name": "FTAC Shop Hero"
    }
  ]
}
{% endschema %}
