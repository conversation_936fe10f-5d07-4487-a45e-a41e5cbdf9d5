{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-contact-form {
    padding: var(--ftac-space-20) 0;
    background-color: white;
  }
  
  .ftac-contact-form__container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
  }
  
  .ftac-contact-form__header {
    text-align: center;
    margin-bottom: var(--ftac-space-12);
  }
  
  .ftac-contact-form__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-3xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-contact-form__subtitle {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-charcoal);
    opacity: 0.8;
  }
  
  .ftac-contact-form__form {
    background-color: rgba(var(--ftac-warm-cream-rgb), 0.2);
    padding: var(--ftac-space-8);
    border-radius: var(--ftac-radius-xl);
    box-shadow: var(--ftac-shadow-lg);
  }
  
  .ftac-contact-form__field {
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-contact-form__label {
    display: block;
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-base);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-2);
  }
  
  .ftac-contact-form__label--required::after {
    content: ' *';
    color: var(--ftac-academy-blue);
  }
  
  .ftac-contact-form__input,
  .ftac-contact-form__textarea,
  .ftac-contact-form__select {
    width: 100%;
    padding: var(--ftac-space-4);
    border: 2px solid rgba(var(--ftac-charcoal-rgb), 0.2);
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
    background-color: white;
    transition: border-color 0.2s ease;
  }
  
  .ftac-contact-form__input:focus,
  .ftac-contact-form__textarea:focus,
  .ftac-contact-form__select:focus {
    outline: none;
    border-color: var(--ftac-academy-blue);
    box-shadow: 0 0 0 3px rgba(var(--ftac-academy-blue-rgb), 0.1);
  }
  
  .ftac-contact-form__textarea {
    min-height: 120px;
    resize: vertical;
  }
  
  .ftac-contact-form__row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ftac-space-4);
  }
  
  .ftac-contact-form__submit {
    background-color: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-4) var(--ftac-space-8);
    border: none;
    border-radius: var(--ftac-radius-lg);
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--ftac-shadow-md);
    width: 100%;
  }
  
  .ftac-contact-form__submit:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
    box-shadow: var(--ftac-shadow-lg);
  }
  
  .ftac-contact-form__note {
    margin-top: var(--ftac-space-4);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    color: var(--ftac-charcoal);
    opacity: 0.7;
    text-align: center;
  }
  
  /* Desktop Layout */
  @media (min-width: 768px) {
    .ftac-contact-form__row {
      grid-template-columns: 1fr 1fr;
    }
    
    .ftac-contact-form__submit {
      width: auto;
      min-width: 200px;
      margin: 0 auto;
      display: block;
    }
  }
  
  @media (min-width: 1024px) {
    .ftac-contact-form__title {
      font-size: var(--ftac-text-4xl);
    }
  }
</style>

<section class="ftac-contact-form" id="contact-form">
  <div class="ftac-contact-form__container">
    <div class="ftac-contact-form__header">
      <h2 class="ftac-contact-form__title">Send Sarah a Message</h2>
      <p class="ftac-contact-form__subtitle">
        {{ section.settings.subtitle | default: "We respond to support requests promptly. Let us know how we can help with your digital products." }}
      </p>
    </div>
    
    {% form 'contact', class: 'ftac-contact-form__form' %}
      {% if form.posted_successfully? %}
        <div style="background-color: var(--ftac-learning-green); color: white; padding: var(--ftac-space-4); border-radius: var(--ftac-radius-lg); margin-bottom: var(--ftac-space-6); text-align: center;">
          <strong>Thank you!</strong> Your message has been sent successfully. I'll respond within 3 hours.
        </div>
      {% endif %}
      
      {% if form.errors %}
        <div style="background-color: #ef4444; color: white; padding: var(--ftac-space-4); border-radius: var(--ftac-radius-lg); margin-bottom: var(--ftac-space-6);">
          <strong>Please fix the following errors:</strong>
          <ul style="margin: var(--ftac-space-2) 0 0 var(--ftac-space-4);">
            {% for field in form.errors %}
              <li>{{ field | capitalize }} {{ form.errors.messages[field] }}</li>
            {% endfor %}
          </ul>
        </div>
      {% endif %}
      
      <div class="ftac-contact-form__row">
        <div class="ftac-contact-form__field">
          <label for="contact_name" class="ftac-contact-form__label ftac-contact-form__label--required">Your Name</label>
          <input type="text" id="contact_name" name="contact[name]" class="ftac-contact-form__input" value="{{ form.name }}" required>
        </div>
        
        <div class="ftac-contact-form__field">
          <label for="contact_email" class="ftac-contact-form__label ftac-contact-form__label--required">Email Address</label>
          <input type="email" id="contact_email" name="contact[email]" class="ftac-contact-form__input" value="{{ form.email }}" required>
        </div>
      </div>
      
      <div class="ftac-contact-form__field">
        <label for="contact_subject" class="ftac-contact-form__label">Subject</label>
        <select id="contact_subject" name="contact[subject]" class="ftac-contact-form__select">
          <option value="">Select a topic...</option>
          <option value="General Question" {% if form.subject == "General Question" %}selected{% endif %}>General Question</option>
          <option value="Technical Support" {% if form.subject == "Technical Support" %}selected{% endif %}>Technical Support</option>
          <option value="Bundle Information" {% if form.subject == "Bundle Information" %}selected{% endif %}>Bundle Information</option>
          <option value="App Access Help" {% if form.subject == "App Access Help" %}selected{% endif %}>App Access Help</option>
          <option value="Learning Guidance" {% if form.subject == "Learning Guidance" %}selected{% endif %}>Learning Guidance</option>
          <option value="Other" {% if form.subject == "Other" %}selected{% endif %}>Other</option>
        </select>
      </div>
      
      <div class="ftac-contact-form__field">
        <label for="contact_order" class="ftac-contact-form__label">Etsy Order Number (if applicable)</label>
        <input type="text" id="contact_order" name="contact[order_number]" class="ftac-contact-form__input" value="{{ form.order_number }}" placeholder="e.g., #1234567890">
      </div>
      
      <div class="ftac-contact-form__field">
        <label for="contact_message" class="ftac-contact-form__label ftac-contact-form__label--required">Your Message</label>
        <textarea id="contact_message" name="contact[body]" class="ftac-contact-form__textarea" required placeholder="Tell me about your question or how I can help you...">{{ form.body }}</textarea>
      </div>
      
      <button type="submit" class="ftac-contact-form__submit">
        Send Message to Sarah
      </button>
      
      <p class="ftac-contact-form__note">
        * Required fields. I typically respond within 3 hours during business hours.
      </p>
    {% endform %}
  </div>
</section>

{% schema %}
{
  "name": "FTAC Contact Form",
  "settings": [
    {
      "type": "textarea",
      "id": "subtitle",
      "label": "Form Subtitle",
      "default": "We respond to support requests promptly. Let us know how we can help with your digital products."
    }
  ],
  "presets": [
    {
      "name": "FTAC Contact Form"
    }
  ]
}
{% endschema %}
