{{ 'base.css' | asset_url | stylesheet_tag }}
{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-about-hero {
    padding: var(--ftac-space-20) 0;
    background: linear-gradient(135deg, var(--ftac-warm-cream) 0%, rgba(var(--ftac-warm-cream-rgb), 0.3) 100%);
  }
  
  .ftac-about-hero__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ftac-space-12);
    align-items: center;
  }
  
  .ftac-about-hero__content {
    text-align: center;
  }
  
  .ftac-about-hero__headline {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-4xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    line-height: 1.1;
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-about-hero__headline-highlight {
    color: var(--ftac-academy-blue);
  }
  
  .ftac-about-hero__intro {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-xl);
    color: var(--ftac-charcoal);
    line-height: 1.6;
    margin-bottom: var(--ftac-space-8);
    opacity: 0.9;
  }
  
  .ftac-about-hero__image {
    text-align: center;
  }
  
  .ftac-about-hero__sarah-photo {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: var(--ftac-radius-2xl);
    box-shadow: var(--ftac-shadow-xl);
  }
  
  .ftac-about-hero__credentials {
    display: flex;
    justify-content: center;
    gap: var(--ftac-space-6);
    margin-top: var(--ftac-space-8);
    flex-wrap: wrap;
  }
  
  .ftac-about-hero__credential {
    display: flex;
    align-items: center;
    gap: var(--ftac-space-2);
    padding: var(--ftac-space-3) var(--ftac-space-4);
    background-color: white;
    border-radius: var(--ftac-radius-lg);
    box-shadow: var(--ftac-shadow-sm);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-sm);
    font-weight: var(--ftac-font-medium);
    color: var(--ftac-charcoal);
  }
  
  .ftac-about-hero__credential-icon {
    width: 20px;
    height: 20px;
    color: var(--ftac-academy-blue);
  }
  
  /* Desktop Layout */
  @media (min-width: 768px) {
    .ftac-about-hero__container {
      grid-template-columns: 1fr 1fr;
      text-align: left;
    }
    
    .ftac-about-hero__content {
      text-align: left;
    }
    
    .ftac-about-hero__headline {
      font-size: var(--ftac-text-5xl);
    }
    
    .ftac-about-hero__credentials {
      justify-content: flex-start;
    }
  }
  
  @media (min-width: 1024px) {
    .ftac-about-hero__headline {
      font-size: var(--ftac-text-6xl);
    }
  }
</style>

<section class="ftac-about-hero">
  <div class="ftac-about-hero__container">
    <div class="ftac-about-hero__content">
      <h1 class="ftac-about-hero__headline">
        Meet <span class="ftac-about-hero__headline-highlight">Sarah Mitchell</span>
      </h1>
      
      <p class="ftac-about-hero__intro">
        {{ section.settings.intro_text | default: "Your caring educator and technology guide, dedicated to making innovation accessible to everyone through personalized learning and heartfelt support." }}
      </p>
      
      <div class="ftac-about-hero__credentials">
        <div class="ftac-about-hero__credential">
          <svg class="ftac-about-hero__credential-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          <span>Certified Educator</span>
        </div>
        <div class="ftac-about-hero__credential">
          <svg class="ftac-about-hero__credential-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
          <span>Innovation Expert</span>
        </div>
        <div class="ftac-about-hero__credential">
          <svg class="ftac-about-hero__credential-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
          </svg>
          <span>Personal Support</span>
        </div>
      </div>
    </div>
    
    <div class="ftac-about-hero__image">
      {% if section.settings.sarah_photo %}
        <img src="{{ section.settings.sarah_photo | image_url: width: 1000 }}" alt="Sarah Mitchell - Future Tech Academy Club Founder" class="ftac-about-hero__sarah-photo">
      {% else %}
        <div style="width: 100%; max-width: 500px; height: 600px; background-color: var(--ftac-academy-blue); border-radius: var(--ftac-radius-2xl); display: flex; align-items: center; justify-content: center; color: white; font-family: var(--ftac-font-primary); font-size: var(--ftac-text-lg); margin: 0 auto; text-align: center;">
          Sarah's Professional<br>Photo Coming Soon
        </div>
      {% endif %}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "FTAC About Hero",
  "settings": [
    {
      "type": "image_picker",
      "id": "sarah_photo",
      "label": "Sarah's Professional Photo",
      "info": "High-quality professional photo of Sarah Mitchell"
    },
    {
      "type": "textarea",
      "id": "intro_text",
      "label": "Introduction Text",
      "default": "Your caring educator and technology guide, dedicated to making innovation accessible to everyone through personalized learning and heartfelt support."
    }
  ],
  "presets": [
    {
      "name": "FTAC About Hero"
    }
  ]
}
{% endschema %}
