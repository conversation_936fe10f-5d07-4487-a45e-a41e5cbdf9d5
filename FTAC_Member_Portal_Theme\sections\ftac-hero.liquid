<style>
  .ftac-hero {
    background: linear-gradient(135deg, var(--ftac-cream-base) 0%, var(--ftac-peach-glow) 50%, var(--ftac-cream-base) 100%);
    position: relative;
    min-height: 90vh;
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  /* Luxury floating elements */
  .ftac-hero::before {
    content: '';
    position: absolute;
    top: 15%;
    right: 8%;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    border-radius: 50%;
    opacity: 0.1;
    animation: ftacFloat 6s ease-in-out infinite;
  }

  .ftac-hero::after {
    content: '';
    position: absolute;
    bottom: 20%;
    left: 5%;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--ftac-sage-accent), var(--ftac-rose-gold));
    border-radius: 50%;
    opacity: 0.08;
    animation: ftacFloat 8s ease-in-out infinite reverse;
  }

  @keyframes ftacFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
  }

  @keyframes ftacGlow {
    0%, 100% { box-shadow: 0 0 20px rgba(var(--ftac-blush-primary-rgb), 0.3); }
    50% { box-shadow: 0 0 40px rgba(var(--ftac-blush-primary-rgb), 0.6); }
  }

  .ftac-hero__container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
    align-items: center;
    position: relative;
    z-index: 2;
  }

  .ftac-hero__content {
    text-align: center;
    padding: 2rem 1rem;
  }

  .ftac-hero__headline {
    font-family: var(--ftac-font-display);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    line-height: 1.2;
    margin-bottom: 1.5rem;
    position: relative;
  }

  .ftac-hero__headline-highlight {
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
  }

  .ftac-hero__subheadline {
    font-family: var(--ftac-font-primary);
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    color: var(--ftac-charcoal-text);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
  }

  /* Emotional connection points */
  .ftac-hero__emotion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .ftac-hero__emotion-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 200, 221, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .ftac-hero__emotion-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .ftac-hero__emotion-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(255, 200, 221, 0.3);
  }

  .ftac-hero__emotion-card:hover::before {
    opacity: 0.05;
  }

  .ftac-hero__emotion-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
    display: block;
    position: relative;
    z-index: 1;
  }

  .ftac-hero__emotion-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--ftac-charcoal-text);
    position: relative;
    z-index: 1;
  }

  .ftac-hero__cta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    margin-bottom: 2rem;
  }

  .ftac-hero__cta-primary {
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-family: var(--ftac-font-primary);
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(var(--ftac-blush-primary-rgb), 0.4);
    min-width: 300px;
    text-align: center;
    position: relative;
    overflow: hidden;
    animation: ftacGlow 3s ease-in-out infinite;
  }

  .ftac-hero__cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
  }

  .ftac-hero__cta-primary:hover::before {
    left: 100%;
  }

  .ftac-hero__cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(var(--ftac-blush-primary-rgb), 0.6);
  }

  .ftac-hero__cta-secondary {
    color: var(--ftac-charcoal-text);
    text-decoration: none;
    font-family: var(--ftac-font-primary);
    font-size: 0.95rem;
    opacity: 0.7;
    transition: all 0.2s ease;
    position: relative;
  }

  .ftac-hero__cta-secondary::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--ftac-blush-primary);
    transition: width 0.3s ease;
  }

  .ftac-hero__cta-secondary:hover {
    opacity: 1;
    color: var(--ftac-blush-primary);
  }

  .ftac-hero__cta-secondary:hover::after {
    width: 100%;
  }

  /* Pinterest-style trust indicators */
  .ftac-hero__trust-indicators {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
  }

  .ftac-hero__trust-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(128, 237, 153, 0.2);
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(128, 237, 153, 0.1);
    font-family: 'Open Sans', sans-serif;
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--ftac-charcoal-text);
    transition: all 0.3s ease;
  }

  .ftac-hero__trust-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(128, 237, 153, 0.2);
    border-color: var(--ftac-sage-accent);
  }

  .ftac-hero__trust-icon {
    width: 18px;
    height: 18px;
    color: var(--ftac-sage-accent);
  }

  /* Pinterest-style visual grid */
  .ftac-hero__visual {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .ftac-hero__visual-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    max-width: 500px;
    width: 100%;
  }

  .ftac-hero__visual-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    border: 1px solid rgba(255, 200, 221, 0.2);
  }

  .ftac-hero__visual-card:nth-child(1) {
    transform: rotate(-2deg);
    background: linear-gradient(135deg, rgba(255, 200, 221, 0.1), rgba(255, 175, 204, 0.1));
  }

  .ftac-hero__visual-card:nth-child(2) {
    transform: rotate(1deg);
    background: linear-gradient(135deg, rgba(128, 237, 153, 0.1), rgba(112, 224, 164, 0.1));
  }

  .ftac-hero__visual-card:nth-child(3) {
    transform: rotate(1.5deg);
    background: linear-gradient(135deg, rgba(162, 210, 255, 0.1), rgba(180, 220, 255, 0.1));
  }

  .ftac-hero__visual-card:nth-child(4) {
    transform: rotate(-1deg);
    background: linear-gradient(135deg, rgba(232, 180, 184, 0.1), rgba(240, 190, 194, 0.1));
  }

  .ftac-hero__visual-card:hover {
    transform: rotate(0deg) translateY(-10px);
    box-shadow: 0 20px 40px rgba(255, 200, 221, 0.3);
  }

  .ftac-hero__visual-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
    display: block;
  }

  .ftac-hero__visual-text {
    font-family: 'Dancing Script', cursive;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--ftac-charcoal-text);
    margin-bottom: 0.25rem;
  }

  .ftac-hero__visual-subtext {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.8rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.7;
  }

  /* Responsive Design */
  @media (min-width: 768px) {
    .ftac-hero__container {
      grid-template-columns: 1.2fr 1fr;
      gap: 4rem;
      text-align: left;
    }

    .ftac-hero__content {
      text-align: left;
    }

    .ftac-hero__emotion-grid {
      grid-template-columns: repeat(2, 1fr);
      margin-left: 0;
      margin-right: 0;
    }

    .ftac-hero__cta {
      flex-direction: row;
      justify-content: flex-start;
    }

    .ftac-hero__trust-indicators {
      justify-content: flex-start;
    }

    .ftac-hero__visual-grid {
      max-width: 600px;
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .ftac-hero__container {
      padding: 0 3rem;
    }

    .ftac-hero__emotion-grid {
      grid-template-columns: repeat(3, 1fr);
    }

    .ftac-hero__visual-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1200px) {
    .ftac-hero__emotion-grid {
      grid-template-columns: repeat(3, 1fr);
      max-width: 900px;
    }
  }
</style>

<section class="ftac-hero">
  <div class="ftac-hero__container">
    <div class="ftac-hero__content">
      <h1 class="ftac-hero__headline">
        Discover Your <span class="ftac-hero__headline-highlight">Dream Lifestyle</span> with Handcrafted Digital Treasures
      </h1>

      <p class="ftac-hero__subheadline">
        Curated with love for dreamers, creators, and those seeking beauty in everyday moments. Each digital bundle is thoughtfully designed to inspire and transform your world.
      </p>

      <div class="ftac-hero__emotion-grid">
        <div class="ftac-hero__emotion-card">
          <span class="ftac-hero__emotion-icon">😴</span>
          <div class="ftac-hero__emotion-text">Better Sleep</div>
        </div>
        <div class="ftac-hero__emotion-card">
          <span class="ftac-hero__emotion-icon">🧘</span>
          <div class="ftac-hero__emotion-text">Less Anxiety</div>
        </div>
        <div class="ftac-hero__emotion-card">
          <span class="ftac-hero__emotion-icon">💝</span>
          <div class="ftac-hero__emotion-text">Stronger Bonds</div>
        </div>
      </div>

      <div class="ftac-hero__cta">
        <a href="https://www.etsy.com/shop/FutureTechAcademy" class="ftac-hero__cta-primary" target="_blank" rel="noopener">
          ✨ Explore Our Etsy Collection ✨
        </a>
        <a href="/pages/member-access" class="ftac-hero__cta-secondary">
          Already a member? Access your treasures →
        </a>
      </div>

      <div class="ftac-hero__trust-indicators">
        <div class="ftac-hero__trust-badge">
          <svg class="ftac-hero__trust-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span>PDF + Interactive App</span>
        </div>
        <div class="ftac-hero__trust-badge">
          <svg class="ftac-hero__trust-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
          <span>Instant Access</span>
        </div>
        <div class="ftac-hero__trust-badge">
          <svg class="ftac-hero__trust-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
          </svg>
          <span>Lifetime Access</span>
        </div>
      </div>
    </div>

    <div class="ftac-hero__visual">
      <div class="ftac-hero__visual-grid">
        <div class="ftac-hero__visual-card">
          <span class="ftac-hero__visual-icon">😴</span>
          <div class="ftac-hero__visual-text">Sleep Trackers</div>
          <div class="ftac-hero__visual-subtext">Better rest habits</div>
        </div>
        <div class="ftac-hero__visual-card">
          <span class="ftac-hero__visual-icon">🧘</span>
          <div class="ftac-hero__visual-text">Anxiety Journals</div>
          <div class="ftac-hero__visual-subtext">Self-therapy tools</div>
        </div>
        <div class="ftac-hero__visual-card">
          <span class="ftac-hero__visual-icon">👥</span>
          <div class="ftac-hero__visual-text">Friendship Tools</div>
          <div class="ftac-hero__visual-subtext">Relationship insights</div>
        </div>
        <div class="ftac-hero__visual-card">
          <span class="ftac-hero__visual-icon">📱</span>
          <div class="ftac-hero__visual-text">Interactive Apps</div>
          <div class="ftac-hero__visual-subtext">Beyond static PDFs</div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
// Luxury hero animations and interactions
document.addEventListener('DOMContentLoaded', function() {
  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Animate emotion cards on scroll
  const emotionCards = document.querySelectorAll('.ftac-hero__emotion-card');
  emotionCards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px)';
    card.style.transition = `all 0.6s ease ${index * 0.1}s`;
    observer.observe(card);
  });

  // Animate visual cards on scroll
  const visualCards = document.querySelectorAll('.ftac-hero__visual-card');
  visualCards.forEach((card, index) => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px) rotate(0deg)';
    card.style.transition = `all 0.8s ease ${index * 0.15}s`;
    observer.observe(card);
  });

  // Add sparkle effect to CTA button
  const ctaButton = document.querySelector('.ftac-hero__cta-primary');
  if (ctaButton) {
    ctaButton.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-3px) scale(1.02)';
    });

    ctaButton.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0) scale(1)';
    });
  }

  // Parallax effect for floating elements
  window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.ftac-hero');

    if (hero) {
      const heroRect = hero.getBoundingClientRect();
      if (heroRect.bottom > 0 && heroRect.top < window.innerHeight) {
        hero.style.transform = `translateY(${scrolled * 0.1}px)`;
      }
    }
  });
});
</script>

{% schema %}
{
  "name": "FTAC Hero",
  "settings": [],
  "presets": [
    {
      "name": "FTAC Hero"
    }
  ]
}
{% endschema %}
