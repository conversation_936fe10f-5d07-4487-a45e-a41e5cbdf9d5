/* FTAC Mobile-First Responsive Optimization
   Ensures luxury design elements work perfectly on mobile devices
   with touch-friendly interactions and optimal performance */

/* Mobile-First Base Styles */
:root {
  --mobile-padding: 1rem;
  --mobile-margin: 0.75rem;
  --touch-target-size: 44px;
  --mobile-font-scale: 0.9;
}

/* Touch-Friendly Interactive Elements */
.ftac-luxury-btn,
.btn,
button,
[role="button"] {
  min-height: var(--touch-target-size);
  min-width: var(--touch-target-size);
  padding: 0.75rem 1.5rem;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Mobile Navigation Optimizations */
@media screen and (max-width: 768px) {
  .header {
    padding: 0.5rem var(--mobile-padding);
  }
  
  .header__menu-item {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 200, 221, 0.1);
  }
  
  .header__menu-item:last-child {
    border-bottom: none;
  }
  
  /* Mobile menu improvements */
  .mobile-nav__menu {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
  }
  
  .mobile-nav__link {
    font-size: 1.1rem;
    padding: 1rem var(--mobile-padding);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

/* Hero Section Mobile Optimization */
@media screen and (max-width: 768px) {
  .ftac-hero {
    padding: 2rem var(--mobile-padding);
    min-height: 70vh;
  }
  
  .ftac-hero-content {
    text-align: center;
    max-width: 100%;
  }
  
  .ftac-hero-title {
    font-size: clamp(2rem, 8vw, 3rem);
    line-height: 1.2;
    margin-bottom: 1rem;
  }
  
  .ftac-hero-subtitle {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .ftac-hero-cta-group {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .ftac-luxury-btn {
    width: 100%;
    justify-content: center;
  }
  
  /* Mobile emotion cards */
  .ftac-emotion-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 2rem;
  }
  
  .ftac-emotion-card {
    padding: 1rem;
  }
  
  /* Mobile visual grid */
  .ftac-visual-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-top: 2rem;
  }
  
  .ftac-visual-item {
    height: 120px;
  }
}

/* Member Dashboard Mobile Optimization */
@media screen and (max-width: 768px) {
  .ftac-member-dashboard {
    padding: 1rem var(--mobile-padding);
  }
  
  .ftac-dashboard-header {
    text-align: center;
    margin-bottom: 2rem;
  }
  
  .ftac-dashboard-title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .ftac-dashboard-subtitle {
    font-size: 1rem;
  }
  
  /* Mobile masonry grid */
  .ftac-dashboard-masonry {
    columns: 1;
    gap: 1rem;
  }
  
  .ftac-dashboard-card {
    margin-bottom: 1rem;
    break-inside: avoid;
  }
  
  /* Mobile access code input */
  .ftac-access-code-input {
    flex-direction: column;
    gap: 1rem;
  }
  
  .ftac-access-code-input input {
    width: 100%;
    text-align: center;
  }
  
  .ftac-access-code-input button {
    width: 100%;
  }
}

/* Product Pages Mobile Optimization */
@media screen and (max-width: 768px) {
  .ftac-luxury-product {
    padding: 1rem var(--mobile-padding);
  }
  
  .ftac-luxury-product__gallery {
    margin-bottom: 2rem;
  }
  
  .ftac-luxury-product__info {
    padding: 0;
  }
  
  .ftac-luxury-product__title {
    font-size: 1.75rem;
    margin-bottom: 1rem;
  }
  
  .ftac-luxury-product__price {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .ftac-luxury-product__description {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
  }
  
  /* Mobile buy section */
  .ftac-luxury-product__buy-section {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    padding: 1rem var(--mobile-padding);
    border-top: 1px solid rgba(255, 200, 221, 0.2);
    z-index: 100;
  }
  
  .ftac-luxury-product__add-to-cart {
    width: 100%;
    font-size: 1.1rem;
    padding: 1rem;
  }
  
  /* Mobile trust signals */
  .ftac-luxury-product__trust-signals {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 2rem;
  }
}

/* Pricing Page Mobile Optimization */
@media screen and (max-width: 768px) {
  .ftac-pricing-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1rem var(--mobile-padding);
  }
  
  .ftac-pricing-card {
    max-width: 100%;
    margin: 0;
  }
  
  .ftac-pricing-card.featured {
    order: -1;
    transform: none;
    margin-bottom: 1rem;
  }
  
  .ftac-pricing-features {
    margin-bottom: 2rem;
  }
  
  .ftac-pricing-cta {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
  }
}

/* Shop Section Mobile Optimization */
@media screen and (max-width: 768px) {
  .collection {
    padding: 1rem var(--mobile-padding);
  }
  
  .collection__title {
    font-size: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
  }
  
  /* Mobile product grid */
  .collection__products {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .card-wrapper {
    margin-bottom: 1rem;
  }
  
  .card {
    border-radius: 15px;
  }
  
  .card__media {
    border-radius: 15px 15px 0 0;
  }
  
  .card__content {
    padding: 1rem;
  }
  
  .card__heading {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .price {
    font-size: 1.1rem;
    font-weight: 700;
  }
  
  /* Mobile facets */
  .facets__wrapper {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .facets__wrapper.active {
    transform: translateX(0);
  }
  
  .facets__header {
    padding: 1rem var(--mobile-padding);
    border-bottom: 1px solid rgba(255, 200, 221, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .facets__close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
  }
}

/* Trust Signals Mobile Optimization */
@media screen and (max-width: 768px) {
  .ftac-trust-signals {
    padding: 2rem var(--mobile-padding);
  }
  
  .ftac-trust-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .ftac-trust-card {
    padding: 1.5rem;
    text-align: center;
  }
  
  .ftac-trust-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
  }
  
  .ftac-testimonials-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .ftac-testimonial-card {
    padding: 1.5rem;
  }
}

/* Social Proof Mobile Optimization */
@media screen and (max-width: 768px) {
  .ftac-social-proof {
    padding: 2rem var(--mobile-padding);
  }
  
  .ftac-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .ftac-stat-card {
    padding: 1.5rem 1rem;
  }
  
  .ftac-stat-number {
    font-size: 2rem;
  }
  
  .ftac-gallery-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .ftac-gallery-item {
    border-radius: 20px;
  }
  
  .ftac-gallery-image {
    height: 200px;
  }
  
  /* Mobile reviews carousel */
  .ftac-review-card {
    min-width: 280px;
    padding: 1.5rem;
  }
  
  .ftac-carousel-controls {
    margin-top: 1rem;
  }
  
  .ftac-carousel-btn {
    width: 40px;
    height: 40px;
  }
}

/* Urgency Elements Mobile Optimization */
@media screen and (max-width: 768px) {
  .ftac-urgency-section {
    padding: 2rem var(--mobile-padding);
  }
  
  .ftac-urgency-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .ftac-countdown-timer {
    padding: 1.5rem;
  }
  
  .ftac-countdown-display {
    gap: 0.5rem;
  }
  
  .ftac-countdown-item {
    min-width: 60px;
    padding: 0.75rem 0.5rem;
  }
  
  .ftac-countdown-number {
    font-size: 1.5rem;
  }
  
  .ftac-countdown-label {
    font-size: 0.75rem;
  }
  
  /* Mobile scarcity indicators */
  .ftac-scarcity-card {
    padding: 1.5rem;
  }
  
  .ftac-progress-bar {
    height: 8px;
    margin: 1rem 0;
  }
  
  /* Mobile floating notifications */
  .ftac-floating-notification {
    position: fixed;
    bottom: 80px;
    left: var(--mobile-padding);
    right: var(--mobile-padding);
    max-width: none;
    border-radius: 15px;
    padding: 1rem;
  }
  
  .ftac-social-notification {
    bottom: 80px;
    left: var(--mobile-padding);
    right: var(--mobile-padding);
    max-width: none;
  }
}

/* Mobile Form Optimizations */
@media screen and (max-width: 768px) {
  .field {
    margin-bottom: 1rem;
  }
  
  .field__input,
  .field__textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 1rem;
    border-radius: 12px;
  }
  
  .field__label {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  /* Mobile newsletter signup */
  .newsletter-form {
    flex-direction: column;
    gap: 1rem;
  }
  
  .newsletter-form__field-wrapper {
    width: 100%;
  }
  
  .newsletter-form__button {
    width: 100%;
    padding: 1rem;
  }
}

/* Mobile Performance Optimizations */
@media screen and (max-width: 768px) {
  /* Reduce animations on mobile for performance */
  .ftac-luxury-btn::after,
  .ftac-pricing-card::after,
  .ftac-gallery-item::after {
    animation-duration: 8s; /* Slower animations */
  }
  
  /* Optimize background effects */
  .ftac-hero::before,
  .ftac-hero::after,
  .ftac-social-proof::before,
  .ftac-social-proof::after {
    opacity: 0.03; /* Lighter effects */
  }
  
  /* Reduce backdrop blur for performance */
  .card,
  .ftac-trust-card,
  .ftac-gallery-item,
  .ftac-review-card {
    backdrop-filter: blur(10px); /* Reduced from 15px */
  }
}

/* Touch Gesture Improvements */
.ftac-reviews-track {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.ftac-gallery-grid {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Mobile Accessibility Improvements */
@media screen and (max-width: 768px) {
  /* Larger focus indicators */
  button:focus,
  .btn:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 3px solid var(--ftac-blush-primary);
    outline-offset: 2px;
  }
  
  /* Better contrast for mobile */
  .ftac-luxury-btn {
    box-shadow: 0 4px 15px rgba(255, 200, 221, 0.4);
  }
  
  .ftac-luxury-btn:hover,
  .ftac-luxury-btn:focus {
    box-shadow: 0 6px 25px rgba(255, 200, 221, 0.6);
  }
}

/* Mobile-Specific Utility Classes */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media screen and (max-width: 768px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
  
  .mobile-center {
    text-align: center;
  }
  
  .mobile-full-width {
    width: 100%;
  }
  
  .mobile-stack {
    flex-direction: column;
  }
  
  .mobile-hide {
    display: none;
  }
}

/* Mobile Loading States */
@media screen and (max-width: 768px) {
  .loading {
    position: relative;
  }
  
  .loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--ftac-blush-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
}
