/* Future Tech Academy Club Brand Design System - 2025 Edition */
/* Implementing: Clean Minimalism, Professional Typography, Sustainable Design, Accessibility-First */

/* Typography - Modern Sans-Serif (Outfit/Inter) with System Font Fallbacks for Performance */
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* 2025 Typography Trends - Clean & Professional */
  --ftac-font-primary: 'Outfit', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --ftac-font-secondary: 'Inter', 'Outfit', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  /* Font Weights */
  --ftac-font-light: 300;
  --ftac-font-regular: 400;
  --ftac-font-medium: 500;
  --ftac-font-semibold: 600;
  --ftac-font-bold: 700;

  /* Font Sizes */
  --ftac-text-xs: 0.75rem;    /* 12px */
  --ftac-text-sm: 0.875rem;   /* 14px */
  --ftac-text-base: 1rem;     /* 16px */
  --ftac-text-lg: 1.125rem;   /* 18px */
  --ftac-text-xl: 1.25rem;    /* 20px */
  --ftac-text-2xl: 1.5rem;    /* 24px */
  --ftac-text-3xl: 1.875rem;  /* 30px */
  --ftac-text-4xl: 2.25rem;   /* 36px */
  --ftac-text-5xl: 3rem;      /* 48px */
  --ftac-text-6xl: 3.75rem;   /* 60px */

  /* 2025 Spacing Scale - Generous White Space for Clean Minimalism */
  --ftac-space-1: 0.25rem;    /* 4px - Micro spacing */
  --ftac-space-2: 0.5rem;     /* 8px - Tight spacing */
  --ftac-space-3: 0.75rem;    /* 12px - Small spacing */
  --ftac-space-4: 1rem;       /* 16px - Base spacing */
  --ftac-space-5: 1.25rem;    /* 20px - Medium spacing */
  --ftac-space-6: 1.5rem;     /* 24px - Large spacing */
  --ftac-space-8: 2rem;       /* 32px - XL spacing */
  --ftac-space-10: 2.5rem;    /* 40px - XXL spacing */
  --ftac-space-12: 3rem;      /* 48px - Section spacing */
  --ftac-space-16: 4rem;      /* 64px - Large section spacing */
  --ftac-space-20: 5rem;      /* 80px - Hero spacing */
  --ftac-space-24: 6rem;      /* 96px - Major section spacing */
  --ftac-space-32: 8rem;      /* 128px - Page-level spacing */

  /* Border Radius */
  --ftac-radius-sm: 0.25rem;  /* 4px */
  --ftac-radius-md: 0.5rem;   /* 8px */
  --ftac-radius-lg: 0.75rem;  /* 12px */
  --ftac-radius-xl: 1rem;     /* 16px */
  --ftac-radius-2xl: 1.5rem;  /* 24px */

  /* Shadows */
  --ftac-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ftac-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ftac-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --ftac-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Brand Colors - Professional & Unisex */
  --ftac-academy-blue: #2563eb;
  --ftac-academy-blue-rgb: 37, 99, 235;
  --ftac-learning-green: #059669;
  --ftac-learning-green-rgb: 5, 150, 105;
  --ftac-warm-cream: #fdf6e3;
  --ftac-warm-cream-rgb: 253, 246, 227;
  --ftac-charcoal: #374151;
  --ftac-charcoal-rgb: 55, 65, 81;
}

/* Global Body Styling */
body {
  background-color: var(--ftac-warm-cream);
  color: var(--ftac-charcoal);
  font-family: var(--ftac-font-primary);
  line-height: 1.6;
}

/* Base Typography Classes */
.ftac-font-primary {
  font-family: var(--ftac-font-primary);
}

.ftac-font-secondary {
  font-family: var(--ftac-font-secondary);
}

/* Heading Styles */
.ftac-heading-hero {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-5xl);
  font-weight: var(--ftac-font-bold);
  line-height: 1.1;
  color: var(--ftac-charcoal);
}

.ftac-heading-1 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-4xl);
  font-weight: var(--ftac-font-semibold);
  line-height: 1.2;
  color: var(--ftac-charcoal);
}

.ftac-heading-2 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-3xl);
  font-weight: var(--ftac-font-semibold);
  line-height: 1.3;
  color: var(--ftac-charcoal);
}

.ftac-heading-3 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-2xl);
  font-weight: var(--ftac-font-medium);
  line-height: 1.4;
  color: var(--ftac-charcoal);
}

/* Body Text Styles */
.ftac-text-body {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-base);
  font-weight: var(--ftac-font-regular);
  line-height: 1.6;
  color: var(--ftac-charcoal);
}

.ftac-text-large {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-lg);
  font-weight: var(--ftac-font-regular);
  line-height: 1.6;
  color: var(--ftac-charcoal);
}

.ftac-text-small {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-sm);
  font-weight: var(--ftac-font-regular);
  line-height: 1.5;
  color: var(--ftac-charcoal);
  opacity: 0.8;
}

/* Button Styles */
.ftac-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--ftac-space-3) var(--ftac-space-6);
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-base);
  font-weight: var(--ftac-font-medium);
  text-decoration: none;
  border: none;
  border-radius: var(--ftac-radius-lg);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 44px; /* Touch-friendly */
}

.ftac-btn-primary {
  background-color: var(--ftac-academy-blue);
  color: white;
  box-shadow: var(--ftac-shadow-md);
}

.ftac-btn-primary:hover {
  background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
  box-shadow: var(--ftac-shadow-lg);
  transform: translateY(-1px);
}

.ftac-btn-secondary {
  background-color: var(--ftac-learning-green);
  color: white;
  box-shadow: var(--ftac-shadow-md);
}

.ftac-btn-secondary:hover {
  background-color: rgba(var(--ftac-learning-green-rgb), 0.9);
  box-shadow: var(--ftac-shadow-lg);
  transform: translateY(-1px);
}

/* Base Typography Classes */
.ftac-font-primary {
  font-family: var(--ftac-font-primary);
}

.ftac-font-secondary {
  font-family: var(--ftac-font-secondary);
}

/* Heading Styles */
.ftac-heading-hero {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-5xl);
  font-weight: var(--ftac-font-bold);
  line-height: 1.1;
  color: var(--ftac-charcoal);
}

.ftac-heading-1 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-4xl);
  font-weight: var(--ftac-font-semibold);
  line-height: 1.2;
  color: var(--ftac-charcoal);
}

.ftac-heading-2 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-3xl);
  font-weight: var(--ftac-font-semibold);
  line-height: 1.3;
  color: var(--ftac-charcoal);
}

.ftac-heading-3 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-2xl);
  font-weight: var(--ftac-font-medium);
  line-height: 1.4;
  color: var(--ftac-charcoal);
}

/* Body Text Styles */
.ftac-text-body {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-base);
  font-weight: var(--ftac-font-regular);
  line-height: 1.6;
  color: var(--ftac-charcoal);
}

.ftac-text-large {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-lg);
  font-weight: var(--ftac-font-regular);
  line-height: 1.6;
  color: var(--ftac-charcoal);
}

.ftac-text-small {
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-sm);
  font-weight: var(--ftac-font-regular);
  line-height: 1.5;
  color: var(--ftac-charcoal);
  opacity: 0.8;
}



.ftac-btn-outline {
  background-color: transparent;
  color: var(--ftac-academy-blue);
  border: 2px solid var(--ftac-academy-blue);
}

.ftac-btn-outline:hover {
  background-color: var(--ftac-academy-blue);
  color: white;
}

.ftac-btn-large {
  padding: var(--ftac-space-4) var(--ftac-space-8);
  font-size: var(--ftac-text-lg);
  min-height: 52px;
}

/* Card Styles */
.ftac-card {
  background-color: white;
  border-radius: var(--ftac-radius-xl);
  box-shadow: var(--ftac-shadow-md);
  padding: var(--ftac-space-6);
  transition: all 0.2s ease-in-out;
}

.ftac-card:hover {
  box-shadow: var(--ftac-shadow-lg);
  transform: translateY(-2px);
}

.ftac-card-warm {
  background-color: var(--ftac-warm-cream);
}

/* Color Utilities */
.ftac-text-academy-blue { color: var(--ftac-academy-blue); }
.ftac-text-learning-green { color: var(--ftac-learning-green); }
.ftac-text-charcoal { color: var(--ftac-charcoal); }

.ftac-bg-academy-blue { background-color: var(--ftac-academy-blue); }
.ftac-bg-learning-green { background-color: var(--ftac-learning-green); }
.ftac-bg-warm-cream { background-color: var(--ftac-warm-cream); }
.ftac-bg-charcoal { background-color: var(--ftac-charcoal); }

/* Layout Utilities */
.ftac-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--ftac-space-4);
}

.ftac-section {
  padding: var(--ftac-space-16) 0;
}

.ftac-section-large {
  padding: var(--ftac-space-24) 0;
}

/* Grid Utilities */
.ftac-grid {
  display: grid;
  gap: var(--ftac-space-6);
}

.ftac-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.ftac-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.ftac-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .ftac-grid-2,
  .ftac-grid-3,
  .ftac-grid-4 {
    grid-template-columns: 1fr;
  }

  .ftac-heading-hero {
    font-size: var(--ftac-text-4xl);
  }

  .ftac-heading-1 {
    font-size: var(--ftac-text-3xl);
  }

  .ftac-heading-2 {
    font-size: var(--ftac-text-2xl);
  }

  .ftac-heading-3 {
    font-size: var(--ftac-text-xl);
  }
}

/* Form Styles */
.ftac-input {
  width: 100%;
  padding: var(--ftac-space-3);
  border: 2px solid #e5e7eb;
  border-radius: var(--ftac-radius-lg);
  font-family: var(--ftac-font-secondary);
  font-size: var(--ftac-text-base);
  transition: border-color 0.2s ease-in-out;
}

.ftac-input:focus {
  outline: none;
  border-color: var(--ftac-academy-blue);
}

/* Testimonial Styles */
.ftac-testimonial {
  background-color: white;
  border-left: 4px solid var(--ftac-learning-green);
  padding: var(--ftac-space-6);
  border-radius: var(--ftac-radius-lg);
  box-shadow: var(--ftac-shadow-sm);
}

/* Professional Polish */
.ftac-gradient-bg {
  background: linear-gradient(135deg, var(--ftac-academy-blue) 0%, var(--ftac-learning-green) 100%);
}

.ftac-warm-gradient {
  background: linear-gradient(135deg, var(--ftac-warm-cream) 0%, rgba(var(--ftac-warm-cream-rgb), 0.5) 100%);
}

/* ===== 2025 DESIGN TRENDS IMPLEMENTATION ===== */

/* 1. Clean Minimalism - Generous White Space */
.ftac-space-generous {
  padding: var(--ftac-space-20) 0;
}

.ftac-space-hero {
  padding: var(--ftac-space-32) 0;
}

/* 2. Sustainable Design - Reduced Motion for Performance */
@media (prefers-reduced-motion: reduce) {
  .ftac-btn,
  .ftac-card,
  .ftac-input {
    transition: none;
  }
}

/* 3. Accessibility-First Design - WCAG AA Compliance */
.ftac-focus-visible:focus-visible {
  outline: 3px solid var(--ftac-academy-blue);
  outline-offset: 2px;
}

.ftac-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 4. Modern Typography - Enhanced Readability */
.ftac-text-readable {
  line-height: 1.7;
  letter-spacing: 0.01em;
  color: var(--ftac-charcoal);
}

.ftac-text-large-readable {
  font-size: var(--ftac-text-lg);
  line-height: 1.6;
  letter-spacing: 0.005em;
}

/* 5. Professional E-commerce Components */
.ftac-product-card {
  background: white;
  border-radius: var(--ftac-radius-xl);
  box-shadow: var(--ftac-shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
}

.ftac-product-card:hover {
  box-shadow: var(--ftac-shadow-lg);
  transform: translateY(-4px);
  border-color: rgba(var(--ftac-academy-blue-rgb), 0.2);
}

.ftac-price-display {
  font-family: var(--ftac-font-primary);
  font-weight: var(--ftac-font-semibold);
  color: var(--ftac-academy-blue);
  font-size: var(--ftac-text-xl);
}

/* 6. Etsy-Friendly Marketplace Design */
.ftac-marketplace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--ftac-space-6);
  padding: var(--ftac-space-8) 0;
}

.ftac-category-tag {
  display: inline-block;
  padding: var(--ftac-space-2) var(--ftac-space-4);
  background-color: rgba(var(--ftac-learning-green-rgb), 0.1);
  color: var(--ftac-learning-green);
  border-radius: var(--ftac-radius-full);
  font-size: var(--ftac-text-sm);
  font-weight: var(--ftac-font-medium);
  text-decoration: none;
  transition: all 0.2s ease;
}

.ftac-category-tag:hover {
  background-color: rgba(var(--ftac-learning-green-rgb), 0.2);
  transform: translateY(-1px);
}

/* 7. Modern Navigation Patterns */
.ftac-nav-clean {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(var(--ftac-charcoal-rgb), 0.1);
}

/* 8. Enhanced Interactive Elements */
.ftac-interactive-element {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.ftac-interactive-element::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--ftac-academy-blue-rgb), 0.05);
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: inherit;
}

.ftac-interactive-element:hover::before {
  opacity: 1;
}

/* 9. Professional Content Sections */
.ftac-content-section {
  max-width: 65ch;
  margin: 0 auto;
  padding: var(--ftac-space-12) var(--ftac-space-4);
}

.ftac-content-section h2 {
  margin-bottom: var(--ftac-space-6);
  color: var(--ftac-charcoal);
}

.ftac-content-section p {
  margin-bottom: var(--ftac-space-4);
  line-height: 1.7;
}

/* 10. Modern Loading States */
.ftac-loading {
  position: relative;
  overflow: hidden;
}

.ftac-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--ftac-academy-blue-rgb), 0.1), transparent);
  animation: ftac-shimmer 1.5s infinite;
}

@keyframes ftac-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .ftac-marketplace-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--ftac-space-4);
  }

  .ftac-space-hero {
    padding: var(--ftac-space-16) 0;
  }

  .ftac-content-section {
    padding: var(--ftac-space-8) var(--ftac-space-4);
  }
}
