/* Future Tech Academy Club Brand Design System */

/* Typography - Modern Sans-Serif (Outfit/Inter) */
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Brand Typography */
  --ftac-font-primary: 'Outfit', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --ftac-font-secondary: 'Inter', 'Outfit', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  /* Font Weights */
  --ftac-font-light: 300;
  --ftac-font-regular: 400;
  --ftac-font-medium: 500;
  --ftac-font-semibold: 600;
  --ftac-font-bold: 700;

  /* Font Sizes */
  --ftac-text-xs: 0.75rem;    /* 12px */
  --ftac-text-sm: 0.875rem;   /* 14px */
  --ftac-text-base: 1rem;     /* 16px */
  --ftac-text-lg: 1.125rem;   /* 18px */
  --ftac-text-xl: 1.25rem;    /* 20px */
  --ftac-text-2xl: 1.5rem;    /* 24px */
  --ftac-text-3xl: 1.875rem;  /* 30px */
  --ftac-text-4xl: 2.25rem;   /* 36px */
  --ftac-text-5xl: 3rem;      /* 48px */
  --ftac-text-6xl: 3.75rem;   /* 60px */

  /* Spacing */
  --ftac-space-1: 0.25rem;    /* 4px */
  --ftac-space-2: 0.5rem;     /* 8px */
  --ftac-space-3: 0.75rem;    /* 12px */
  --ftac-space-4: 1rem;       /* 16px */
  --ftac-space-5: 1.25rem;    /* 20px */
  --ftac-space-6: 1.5rem;     /* 24px */
  --ftac-space-8: 2rem;       /* 32px */
  --ftac-space-10: 2.5rem;    /* 40px */
  --ftac-space-12: 3rem;      /* 48px */
  --ftac-space-16: 4rem;      /* 64px */
  --ftac-space-20: 5rem;      /* 80px */
  --ftac-space-24: 6rem;      /* 96px */

  /* Border Radius */
  --ftac-radius-sm: 0.25rem;  /* 4px */
  --ftac-radius-md: 0.5rem;   /* 8px */
  --ftac-radius-lg: 0.75rem;  /* 12px */
  --ftac-radius-xl: 1rem;     /* 16px */
  --ftac-radius-2xl: 1.5rem;  /* 24px */

  /* Shadows */
  --ftac-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --ftac-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --ftac-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --ftac-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Brand Colors - Professional & Unisex */
  --ftac-academy-blue: #2563eb;
  --ftac-academy-blue-rgb: 37, 99, 235;
  --ftac-learning-green: #059669;
  --ftac-learning-green-rgb: 5, 150, 105;
  --ftac-warm-cream: #fdf6e3;
  --ftac-warm-cream-rgb: 253, 246, 227;
  --ftac-charcoal: #374151;
  --ftac-charcoal-rgb: 55, 65, 81;
}

/* Global Body Styling */
body {
  background-color: var(--ftac-warm-cream);
  color: var(--ftac-charcoal);
  font-family: var(--ftac-font-primary);
  line-height: 1.6;
}

/* Base Typography Classes */
.ftac-font-primary {
  font-family: var(--ftac-font-primary);
}

.ftac-font-secondary {
  font-family: var(--ftac-font-secondary);
}

/* Heading Styles */
.ftac-heading-hero {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-5xl);
  font-weight: var(--ftac-font-bold);
  line-height: 1.1;
  color: var(--ftac-charcoal);
}

.ftac-heading-1 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-4xl);
  font-weight: var(--ftac-font-semibold);
  line-height: 1.2;
  color: var(--ftac-charcoal);
}

.ftac-heading-2 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-3xl);
  font-weight: var(--ftac-font-semibold);
  line-height: 1.3;
  color: var(--ftac-charcoal);
}

.ftac-heading-3 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-2xl);
  font-weight: var(--ftac-font-medium);
  line-height: 1.4;
  color: var(--ftac-charcoal);
}

/* Body Text Styles */
.text-body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-regular);
  line-height: 1.6;
  color: var(--charcoal-text);
}

.text-large {
  font-family: var(--font-primary);
  font-size: var(--text-lg);
  font-weight: var(--font-regular);
  line-height: 1.6;
  color: var(--charcoal-text);
}

.text-small {
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-regular);
  line-height: 1.5;
  color: var(--charcoal-text);
  opacity: 0.8;
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  text-decoration: none;
  border: none;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 48px; /* Touch-friendly */
}

.btn-primary {
  background-color: var(--sky-blue);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background-color: rgba(var(--sky-blue-rgb), 0.9);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--sage-green);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
  background-color: rgba(var(--sage-green-rgb), 0.9);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-outline {
  background-color: transparent;
  color: var(--sky-blue);
  border: 2px solid var(--sky-blue);
}

.btn-outline:hover {
  background-color: var(--sky-blue);
  color: white;
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  min-height: 52px;
}

/* Card Styles */
.card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  padding: var(--space-6);
  transition: all 0.2s ease-in-out;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-warm {
  background-color: var(--cream-base);
}

/* Color Utilities */
.text-sky-blue { color: var(--sky-blue); }
.text-sage-green { color: var(--sage-green); }
.text-charcoal { color: var(--charcoal-text); }
.text-blush { color: var(--blush-accent); }
.text-gold { color: var(--gold-accent); }

.bg-sky-blue { background-color: var(--sky-blue); }
.bg-sage-green { background-color: var(--sage-green); }
.bg-cream-base { background-color: var(--cream-base); }
.bg-charcoal { background-color: var(--charcoal-text); }
.bg-blush { background-color: var(--blush-accent); }
.bg-peach { background-color: var(--peach-glow); }

/* Layout Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.section {
  padding: var(--space-16) 0;
}

.section-large {
  padding: var(--space-24) 0;
}

/* Grid Utilities */
.grid {
  display: grid;
  gap: var(--space-6);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }

  .heading-hero {
    font-size: var(--text-4xl);
  }

  .heading-1 {
    font-size: var(--text-3xl);
  }

  .heading-2 {
    font-size: var(--text-2xl);
  }

  .heading-3 {
    font-size: var(--text-xl);
  }
}

/* Form Styles */
.input {
  width: 100%;
  padding: var(--space-3);
  border: 2px solid #e5e7eb;
  border-radius: var(--radius-lg);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  transition: border-color 0.2s ease-in-out;
  min-height: 48px; /* Touch-friendly */
}

.input:focus {
  outline: none;
  border-color: var(--sky-blue);
  box-shadow: 0 0 0 3px rgba(var(--sky-blue-rgb), 0.1);
}

/* Testimonial Styles */
.testimonial {
  background-color: white;
  border-left: 4px solid var(--sage-green);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.testimonial-handwritten {
  font-family: var(--font-handwritten);
  font-size: var(--text-lg);
  color: var(--charcoal-text);
}

/* Premium Gradients */
.gradient-wellness {
  background: linear-gradient(135deg, var(--sky-blue) 0%, var(--sage-green) 100%);
}

.gradient-warm {
  background: linear-gradient(135deg, var(--cream-base) 0%, var(--peach-glow) 100%);
}

/* ===== PREMIUM WELLNESS DESIGN IMPLEMENTATION ===== */

/* 1. Generous Spacing for Calm Experience */
.space-generous {
  padding: var(--space-20) 0;
}

.space-hero {
  padding: var(--space-32) 0;
}

/* 2. Sustainable Design - Reduced Motion for Performance */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .card,
  .input {
    transition: none;
  }
}

/* 3. Accessibility-First Design - WCAG AA Compliance */
.focus-visible:focus-visible {
  outline: 3px solid var(--sky-blue);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 4. Enhanced Readability for Wellness Content */
.text-readable {
  line-height: 1.7;
  letter-spacing: 0.01em;
  color: var(--charcoal-text);
}

.text-large-readable {
  font-size: var(--text-lg);
  line-height: 1.6;
  letter-spacing: 0.005em;
}

/* 5. Premium Wellness Product Components */
.product-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(var(--charcoal-text-rgb), 0.1);
}

.product-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: rgba(var(--sky-blue-rgb), 0.2);
}

.price-display {
  font-family: var(--font-primary);
  font-weight: var(--font-semibold);
  color: var(--sky-blue);
  font-size: var(--text-xl);
}

.dual-format-badge {
  background: linear-gradient(135deg, var(--blush-accent), var(--peach-glow));
  color: var(--charcoal-text);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* 6. Wellness Marketplace Design */
.marketplace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  padding: var(--space-8) 0;
}

.category-tag {
  display: inline-block;
  padding: var(--space-2) var(--space-4);
  background-color: rgba(var(--sage-green-rgb), 0.1);
  color: var(--sage-green);
  border-radius: 999px;
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  text-decoration: none;
  transition: all 0.2s ease;
}

.category-tag:hover {
  background-color: rgba(var(--sage-green-rgb), 0.2);
  transform: translateY(-1px);
}

/* 7. Premium Navigation */
.nav-clean {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(var(--charcoal-text-rgb), 0.1);
}

/* 8. Enhanced Interactive Elements */
.interactive-element {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.interactive-element::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--sky-blue-rgb), 0.05);
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: inherit;
}

.interactive-element:hover::before {
  opacity: 1;
}

/* 9. Wellness Content Sections */
.content-section {
  max-width: 65ch;
  margin: 0 auto;
  padding: var(--space-12) var(--space-4);
}

.content-section h2 {
  margin-bottom: var(--space-6);
  color: var(--charcoal-text);
}

.content-section p {
  margin-bottom: var(--space-4);
  line-height: 1.7;
}

/* 10. Premium Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(var(--sky-blue-rgb), 0.1), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .marketplace-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--space-4);
  }

  .space-hero {
    padding: var(--space-16) 0;
  }

  .content-section {
    padding: var(--space-8) var(--space-4);
  }
}
