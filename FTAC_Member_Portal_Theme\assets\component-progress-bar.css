.progress-bar-container {
  width: 100%;
  margin: auto;
}

.progress-bar {
  height: 0.13rem;
  width: 100%;
}

.progress-bar-value {
  width: 100%;
  height: 100%;
  background-color: rgb(var(--color-foreground));
  animation: indeterminateAnimation var(--duration-extra-longer) infinite ease-in-out;
  transform-origin: 0;
}

.progress-bar .progress-bar-value {
  display: block;
}

@keyframes indeterminateAnimation {
  0% {
    transform: translateX(-20%) scaleX(0);
  }
  40% {
    transform: translateX(30%) scaleX(0.7);
  }
  100% {
    transform: translateX(100%) scaleX(0);
  }
}
