{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-legal {
    padding: var(--ftac-space-20) 0;
    background-color: white;
  }
  
  .ftac-legal__container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
  }
  
  .ftac-legal__header {
    text-align: center;
    margin-bottom: var(--ftac-space-16);
  }
  
  .ftac-legal__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-4xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-legal__subtitle {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-xl);
    color: var(--ftac-charcoal);
    opacity: 0.8;
  }
  
  .ftac-legal__content {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
    line-height: 1.7;
  }
  
  .ftac-legal__section {
    margin-bottom: var(--ftac-space-12);
  }
  
  .ftac-legal__section-title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-2xl);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-6);
    border-bottom: 2px solid var(--ftac-academy-blue);
    padding-bottom: var(--ftac-space-2);
  }
  
  .ftac-legal__paragraph {
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-legal__list {
    margin: var(--ftac-space-4) 0;
    padding-left: var(--ftac-space-6);
  }
  
  .ftac-legal__list-item {
    margin-bottom: var(--ftac-space-2);
  }
  
  .ftac-legal__contact {
    background: linear-gradient(135deg, var(--ftac-warm-cream) 0%, rgba(var(--ftac-warm-cream-rgb), 0.3) 100%);
    padding: var(--ftac-space-8);
    border-radius: var(--ftac-radius-lg);
    margin-top: var(--ftac-space-16);
    text-align: center;
  }
  
  .ftac-legal__contact-title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-xl);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-legal__contact-text {
    font-family: var(--ftac-font-secondary);
    color: var(--ftac-charcoal);
    opacity: 0.8;
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-legal__contact-button {
    background-color: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-3) var(--ftac-space-6);
    border-radius: var(--ftac-radius-lg);
    text-decoration: none;
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-medium);
    transition: all 0.3s ease;
    display: inline-block;
  }
  
  .ftac-legal__contact-button:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
  }
  
  /* Desktop Layout */
  @media (min-width: 1024px) {
    .ftac-legal__title {
      font-size: var(--ftac-text-5xl);
    }
  }
</style>

<section class="ftac-legal">
  <div class="ftac-legal__container">
    <div class="ftac-legal__header">
      <h1 class="ftac-legal__title">{{ section.settings.page_title | default: "Legal Information" }}</h1>
      <p class="ftac-legal__subtitle">{{ section.settings.page_subtitle | default: "Effective Date: " }}{{ section.settings.effective_date | default: "January 1, 2024" }}</p>
    </div>
    
    <div class="ftac-legal__content">
      {% if section.settings.page_type == 'privacy' %}
        {% render 'legal-privacy-content' %}
      {% elsif section.settings.page_type == 'terms' %}
        {% render 'legal-terms-content' %}
      {% elsif section.settings.page_type == 'refund' %}
        {% render 'legal-refund-content' %}
      {% else %}
        <div class="ftac-legal__section">
          <p class="ftac-legal__paragraph">{{ section.settings.custom_content | default: "Legal content will be displayed here." }}</p>
        </div>
      {% endif %}
    </div>
    
    <div class="ftac-legal__contact">
      <h3 class="ftac-legal__contact-title">Questions About This Policy?</h3>
      <p class="ftac-legal__contact-text">
        If you have any questions about this policy or need clarification, I'm here to help.
      </p>
      <a href="/pages/contact-support" class="ftac-legal__contact-button">Contact Sarah</a>
    </div>
  </div>
</section>

{% schema %}
{
  "name": "FTAC Legal Content",
  "settings": [
    {
      "type": "text",
      "id": "page_title",
      "label": "Page Title",
      "default": "Legal Information"
    },
    {
      "type": "text",
      "id": "page_subtitle",
      "label": "Page Subtitle",
      "default": "Effective Date: "
    },
    {
      "type": "text",
      "id": "effective_date",
      "label": "Effective Date",
      "default": "January 1, 2024"
    },
    {
      "type": "select",
      "id": "page_type",
      "label": "Legal Page Type",
      "options": [
        {
          "value": "privacy",
          "label": "Privacy Policy"
        },
        {
          "value": "terms",
          "label": "Terms of Service"
        },
        {
          "value": "refund",
          "label": "Refund Policy"
        },
        {
          "value": "custom",
          "label": "Custom Content"
        }
      ],
      "default": "privacy"
    },
    {
      "type": "textarea",
      "id": "custom_content",
      "label": "Custom Content",
      "info": "Only used when page type is set to 'Custom Content'"
    }
  ],
  "presets": [
    {
      "name": "FTAC Legal Content"
    }
  ]
}
{% endschema %}
