quick-order-list {
  display: block;
}

quick-order-list .quantity {
  width: calc(11rem / var(--font-body-scale) + var(--inputs-border-width) * 2);
  min-height: calc((var(--inputs-border-width) * 2) + 3.5rem);
}

quick-order-list .quantity__button {
  width: calc(3.5rem / var(--font-body-scale));
}

quick-order-list .pagination-wrapper {
  margin-top: 2rem;
}

.quick-order-list__contents {
  position: relative;
  padding-bottom: 2rem;
}

.quick-order-list__container--disabled {
  pointer-events: none;
  opacity: 0.5;
}

.quick-order-list__total {
  padding-top: 2rem;
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);
}

.variant-item__quantity .quantity:before {
  z-index: 0;
}

.variant-item__quantity .quantity__button {
  z-index: 1;
}

.variant-item__image-container.global-media-settings::after {
  content: none;
}

@media screen and (min-width: 990px) {
  .quick-order-list__total {
    position: sticky;
    bottom: 0;
    z-index: 2;
    background-color: rgb(var(--color-background));
  }

  .variant-item__quantity-wrapper--no-info,
  .variant-item__error {
    padding-left: calc(15px + 3.4rem);
  }

  .variant-item__error {
    margin-left: 0.3rem;
  }

  .variant-item--unit-price .variant-item__totals {
    vertical-align: top;
  }

  .variant-item--unit-price .variant-item__totals .loading__spinner {
    padding-top: 1.7rem;
  }
}

.quick-order-list__table td,
.quick-order-list__table th {
  padding: 0;
  border: none;
}

.quick-order-list__table th {
  text-align: left;
  padding-bottom: 2rem;
  opacity: 0.85;
  font-weight: normal;
  font-size: 1.1rem;
}

.variant-item__quantity-wrapper {
  display: flex;
}

.variant-item__totals,
.variant-item__details,
.variant-item__price {
  position: relative;
}

.variant-item__price .price,
.variant-item__totals .price {
  display: block;
}

.quick-order-list__table *.right {
  text-align: right;
}

.variant-item__image-container {
  display: inline-flex;
  align-items: flex-start;
  height: 4.5rem;
  width: 4.5rem;
}

.variant-item__media {
  width: 4.5rem;
  height: 4.5rem;
  margin-right: 1.2rem;
}

.variant-item__image {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

@media screen and (min-width: 990px) {
  .variant-item__image {
    max-width: 100%;
  }

  .variant-item__inner .small-hide {
    display: flex;
    flex-direction: column;
    align-self: center;
  }

  .variant-item:not(.variant-item--no-media) .variant-item__inner {
    display: flex;
  }

  .variant-item__discounted-prices {
    justify-content: flex-end;
  }
}

.variant-item__details {
  font-size: 1.6rem;
  line-height: calc(1 + 0.4 / var(--font-body-scale));
}

.variant-item__details > * {
  margin: 0;
  max-width: 30rem;
}

.variant-item__info {
  position: relative;
  padding-bottom: 0.5rem;
}

.variant-item__name {
  display: block;
  font-size: 1.6rem;
  letter-spacing: 0.06rem;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
}

.variant-item__sku {
  font-size: 1.1rem;
  letter-spacing: 0.04rem;
  margin-top: 0.2rem;
}

.variant-item__discounted-prices {
  margin-top: 0;
  margin-bottom: 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.variant-item__discounted-prices dd {
  margin: 0;
}

.variant-item__discounted-prices dd:first-of-type {
  margin-right: 0.8rem;
}

.variant-item__discounted-prices .variant-item__old-price {
  font-size: 1.4rem;
}

.variant-item__old-price {
  opacity: 0.7;
}

.variant-item__final-price {
  font-weight: 400;
}

.variant-item__sold-out {
  opacity: 0.7;
  font-size: 1.6rem;
  color: rgb(var(--color-foreground));
}

.quick-order-list-remove-button {
  display: flex;
  margin: 0 0 0 1.2rem;
  align-self: center;
}

.quick-order-list__button-cancel {
  font-size: 1.5rem;
  letter-spacing: 0.06rem;
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .quick-order-list-remove-button {
    width: 1.5rem;
    height: 1.5rem;
  }

  .quick-order-list-total__column.large-up-hide .loading__spinner {
    margin-top: 2.5rem;
  }

  quick-order-list-remove-all-button {
    margin-left: -1.5rem;
    margin-top: 1rem;
  }

  .quick-order-list-total__column {
    flex-wrap: wrap;
  }

  .quick-order-list__message,
  .quick-order-list-error {
    padding-bottom: 1rem;
  }
}

.quick-order-list-remove-button .button {
  min-width: calc(1.5rem / var(--font-body-scale));
  min-height: 1.5rem;
  padding: 0;
  margin: 0 0.1rem 0.1rem 0;
}

.quick-order-list-remove-button .button:not([disabled]):hover {
  color: rgb(var(--color-foreground));
}

.quick-order-list-remove-button .icon-remove {
  height: 1.5rem;
  width: 1.5rem;
  transition: transform var(--duration-default) ease;
}

.variant-item .loading__spinner {
  top: 0;
  left: auto;
  right: 0;
  bottom: 0;
  padding: 0;
}

.variant-remove-total {
  position: relative;
  align-self: center;
}

.variant-remove-total .button--tertiary {
  width: max-content;
}

.variant-remove-total .icon-remove {
  width: 1.2rem;
  height: 1.2rem;
  margin-right: 0.8rem;
}

.quick-order-list__message {
  margin-top: 1rem;
  display: block;
}

.quick-order-list__message .svg-wrapper {
  margin-right: 1rem;
  width: 1.3rem;
}

.quick-order-list-error {
  margin-top: 1rem;
  display: flex;
}

.quick-order-list-error .svg-wrapper {
  flex-shrink: 0;
  margin-right: 0.7rem;
  align-self: flex-start;
}

@media screen and (min-width: 990px) {
  .variant-item .loading__spinner {
    padding-top: 3rem;
    bottom: auto;
  }

  .variant-item .loading__spinner--error {
    padding-top: 5rem;
  }

  .variant-remove-total .loading__spinner {
    left: 2rem;
    top: 1.2rem;
  }

 .variant-remove-total--empty .loading__spinner {
    top: -1rem;
  }
}

.quick-order-list-remove-button:hover .icon-remove {
  transform: scale(1.25);
}

.quick-order-list-total__info quick-order-list-remove-all-button:hover {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

.variant-remove-total {
  position: relative;
  align-self: center;
}

.variant-item .loading__spinner:not(.hidden) ~ *,
.variant-remove-total .loading__spinner:not(.hidden) ~ * {
  visibility: hidden;
}

.quick-order-list-total__info .loading__spinner:not(.hidden) + quick-order-list-remove-all-button {
  visibility: hidden;
}

.variant-item__error {
  display: flex;
  align-items: flex-start;
  margin-top: 0.2rem;
  width: min-content;
  min-width: 100%;
}

.variant-item__error-text {
  font-size: 1.2rem;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
  order: 1;
}

.variant-item__error-text + .svg-wrapper {
  flex-shrink: 0;
  width: 1.2rem;
  margin-right: 0.5rem;
  margin-top: 0.1rem;
}

.variant-item__error-text:empty + .svg-wrapper {
  display: none;
}

.quick-order-list__table thead th {
  text-transform: uppercase;
}

.variant-item__image-container--no-img {
  border: 0;
}

@media screen and (max-width: 989px) {
  .quick-order-list-total__info {
    flex-direction: column;
    align-items: center;
  }

  .variant-item__details .loading__spinner {
    left: 0;
    top: auto;
  }

  .quick-order-list__table,
  .quick-order-list__table thead,
  .quick-order-list__table tbody {
    display: block;
    width: 100%;
  }

  .quick-order-list__table thead tr {
    display: flex;
    justify-content: space-between;
    border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
    margin-bottom: 4rem;
  }

  .variant-item {
    display: grid;
    grid-template-columns: 4.5rem 1fr;
    grid-template-rows: repeat(2, auto);
    gap: 1.5rem;
    margin-bottom: 3.5rem;
  }

  .variant-item--no-media {
    grid-template: repeat(2, auto) / repeat(3, auto);
  }

  .variant-item:last-child {
    margin-bottom: 0;
  }

  .variant-item__totals {
    grid-column: 5 / 3;
  }

  .variant-item--no-media .variant-item__inner ~ .variant-item__quantity {
    grid-column: 1 / 5;
  }

  .variant-item__quantity {
    grid-column: 2 / 5;
  }

  .variant-item__quantity-wrapper {
    flex-wrap: wrap;
  }

  .variant-item--no-media .variant-item__inner {
    display: none;
  }

  .variant-remove-total {
    margin-top: 1rem;
    text-align: center;
  }

  .quick-order-list__message {
    text-align: center;
  }

  .quick-order-list-total__column,
  .quick-order-list-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .quick-order-list__button {
    max-width: 36rem;
  }
}

.quick-order-list__button-text {
  text-align: center;
}

.quick-order-list-total__confirmation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: -2rem;
}

@media screen and (min-width: 990px) {
  .quick-order-list__table {
    border-spacing: 0;
    border-collapse: separate;
    box-shadow: none;
    width: 100%;
    display: table;
  }

  .quick-order-list__table th {
    border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  }

  .quick-order-list__table th + th {
    padding-left: 5.4rem;
  }

  .quick-order-list__table .quick-order-list__table-heading--wide + .quick-order-list__table-heading--wide {
    padding-left: 10rem;
    text-align: right;
  }

  .quick-order-list__table td {
    padding-top: 1.6rem;
  }

  .quick-order-list__table .desktop-row-error td {
    padding-top: 0;
  }

  .quick-order-list__table .desktop-row-error td {
    padding-top: 0;
  }

  .quick-order-list__table .variant-item--unit-price td {
    vertical-align: middle;
  }

  .variant-item {
    display: table-row;
  }

  .variant-item .variant-item__price {
    text-align: right;
  }

  .variant-item__info {
    width: 20rem;
    display: flex;
    padding: 0.5rem;
  }

  .quick-order-list-total__confirmation span {
    margin-right: 3rem;
  }

  .quick-order-list__total-items {
    width: calc(((11rem / var(--font-body-scale) + var(--inputs-border-width) * 2)));
    margin-left: calc(15px + 3.4rem);
    flex-direction: column;
  }
}

@media screen and (min-width: 990px) {

  .quick-order-list__table thead th:first-child,
  .quick-order-list-total__column {
    width: 37%;
  }

  .quick-order-list-buttons {
    display: flex;
  }

  quick-order-list-remove-all-button {
    margin-left: 0.9rem;
  }
}

.quick-order-list-total__column.large-up-hide .variant-remove-total {
  display: flex;
  justify-content: center;
  margin: 0;

  .loading__spinner {
    margin-top: 1.5rem;
  }
}

.quick-order-list__total-items {
  display: flex;
  align-items: center;
}

.quick-order-list__total-items span,
.totals__subtotal-value {
  margin-top: 0;
  margin-bottom: 0;
  color: rgb(var(--color-foreground));
}

.quick-order-list__total-items p,
.totals__subtotal {
  margin-top: 0.2rem;
  opacity: 0.75;
  margin-bottom: 0;
}

.quick-order-list__total-items p {
  text-align: center;
}

.quick-order-list-total__info {
  display: flex;
}

.quick-order-list-total__info,
.quick-order-list-total__confirmation {
  min-height: 10rem;
}

.quick-order-list-total__price {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  flex-grow: 1;
  text-align: right;
  width: min-content;
}

.quick-order-list-total__price .button {
  margin-right: 2rem;
}

@media screen and (max-width: 989px) {
  .quick-order-list-total__price {
    justify-content: center;
    text-align: center;
    width: 100%;
  }

  .totals__product-total {
    display: flex;
    justify-content: center;
    width: 100%;
    align-items: center;
    padding-bottom: 2rem;
  }

  .totals__subtotal-value,
  .quick-order-list__total-items span {
    margin-right: 1.2rem;
  }

  .quick-order-list__total-items {
    margin-top: 1rem;
    margin-bottom: 1.3rem;
  }

  .quick-order-list-total__price .button {
    margin-bottom: 2rem;
  }

  .quick-order-list-total__confirmation quick-order-list-remove-all-button button {
    margin-top: 1rem;
  }

  .quick-order-list-total__confirmation {
    flex-direction: column;
    margin-top: 2rem;
  }

  .quick-order-list__button-confirm {
    width: 100%;
    max-width: 36rem;
  }

  quick-order-list .tax-note {
    text-align: center;
  }
}

quick-order-list .tax-note {
  margin: 0 0 2rem;
  display: block;
  width: 100%;
}
