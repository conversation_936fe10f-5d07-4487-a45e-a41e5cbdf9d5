# 🚨 FTAC Shopify Theme Naming Protocol

## **MANDATORY RULES - NO EXCEPTIONS**

### 1. **Maximum Length: 32 Characters**
- Filename without extension must be ≤ 32 characters
- Platform max is 64, but FTAC enforces 32 for safety
- **Example:** `member-dashboard.liquid` ✅ (16 chars)
- **Violation:** `ftac-member-dashboard-premium-v2.liquid` ❌ (35 chars)

### 2. **Character Restrictions**
- **ONLY ALLOWED:** lowercase letters (`a-z`), numbers (`0-9`), hyphens (`-`)
- **FORBIDDEN:** spaces, underscores, camelCase, special characters

```
✅ CORRECT:   about-sarah-section.liquid
❌ WRONG:     About_Sarah_Section.liquid
❌ WRONG:     aboutSarahSection.liquid  
❌ WRONG:     about sarah section.liquid
```

### 3. **Exact Matching Required**
- Filename = Liquid reference = Schema name
- Must match character-for-character

```liquid
<!-- File: member-login.liquid -->
{% schema %}
{
  "name": "member-login"  ← Must match filename exactly
}
{% endschema %}
```

### 4. **Assignment Verification**
- Always confirm section/template assignments in Shopify admin
- If page shows defaults/errors → check name length and references immediately

---

## **ENFORCEMENT SYSTEM**

### **Automated Validation**
- Pre-commit hooks block violations
- Build scripts validate all files
- IDE integration flags issues in real-time

### **Violation Response**
1. **BLOCK** creation/commit of non-compliant files
2. **ALERT** with specific violation details
3. **SUGGEST** compliant alternative names
4. **REQUIRE** fix before proceeding

---

## **COMMON VIOLATIONS & FIXES**

| ❌ Violation | ✅ Fix | Reason |
|-------------|--------|---------|
| `ftac_member_dashboard.liquid` | `member-dashboard.liquid` | Underscores → hyphens |
| `FTACMemberDashboard.liquid` | `member-dashboard.liquid` | camelCase → lowercase-hyphens |
| `ftac-premium-member-dashboard-v2.liquid` | `premium-dashboard.liquid` | 38 chars → 17 chars |
| `member dashboard.liquid` | `member-dashboard.liquid` | Spaces → hyphens |

---

## **VALIDATION COMMANDS**

```bash
# Validate current theme
node .ftac-naming-protocol.js

# Pre-commit validation
git add . && node .ftac-naming-protocol.js && git commit

# Batch rename utility
node .ftac-naming-protocol.js --fix-violations
```

---

## **DEVELOPER CHECKLIST**

Before creating any Shopify section/template:

- [ ] Filename ≤ 32 characters
- [ ] Only `a-z`, `0-9`, `-` characters  
- [ ] Lowercase with hyphens
- [ ] Schema name matches filename exactly
- [ ] Tested assignment in Shopify admin
- [ ] No silent failures or default displays

---

## **BUSINESS IMPACT**

### **Why This Matters:**
- **Prevents silent failures** that waste hours debugging
- **Ensures consistent naming** across all developers
- **Maintains Shopify compatibility** across all environments
- **Reduces support tickets** from naming-related issues

### **Cost of Violations:**
- ⏰ **Hours lost** debugging "missing" sections
- 🐛 **Silent failures** in production
- 😤 **Developer frustration** with unclear errors
- 💸 **Client delays** from technical issues

---

## **PROTOCOL STATUS: ACTIVE**

✅ **Implemented:** Automated validation system  
✅ **Enforced:** Pre-commit hooks active  
✅ **Documented:** Rules clearly defined  
✅ **Tested:** Current theme validated compliant  

**This protocol is now part of FTAC's official coding SOPs.**  
**ALL developers and AI agents must follow for future Shopify work.**

---

*Last Updated: January 2025*  
*Protocol Version: 1.0*  
*Status: MANDATORY - NO EXCEPTIONS*
