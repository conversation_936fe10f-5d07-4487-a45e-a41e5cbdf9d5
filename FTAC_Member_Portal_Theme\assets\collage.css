.collage-wrapper-title {
  margin-top: 0;
  margin-bottom: 3rem;
}

.collage {
  display: grid;
}

.collage__item > * {
  width: 100%;
}

.collage__item .card__content {
  flex-grow: initial;
}

@media screen and (max-width: 749px) {
  .collage {
    grid-column-gap: var(--grid-mobile-horizontal-spacing);
    grid-row-gap: var(--grid-mobile-vertical-spacing);
  }

  .collage--mobile {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .collage--mobile .collage__item--left:nth-child(3n - 2) {
    grid-column: span 2;
  }

  .collage--mobile .collage__item--left:nth-child(3n - 2):nth-last-child(2) {
    grid-column: span 1;
  }

  .collage--mobile .collage__item--left:nth-child(3n) {
    grid-column-start: 2;
  }

  .collage--mobile .collage__item--right:nth-child(3n - 2) {
    grid-column-start: 1;
  }

  .collage--mobile .collage__item--right:nth-child(3n - 2):last-child {
    grid-column: span 2;
  }

  .collage--mobile .collage__item--right:nth-child(3n - 1) {
    grid-column-start: 2;
  }

  .collage--mobile .collage__item--right:nth-child(3n) {
    grid-column: 1 / span 2;
  }
}

@media screen and (min-width: 750px) {
  .collage {
    grid-auto-flow: column;
    grid-column-gap: var(--grid-desktop-horizontal-spacing);
    grid-row-gap: var(--grid-desktop-vertical-spacing);
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .collage__item--left:nth-child(3n - 2) {
    grid-column: 1 / span 2;
    grid-row: span 2;
  }

  .collage__item--left:nth-child(3n - 2):last-child {
    grid-column: 1 / span 3;
  }

  .collage__item--left:nth-child(3n - 1),
  .collage__item--left:nth-child(3n) {
    grid-column-start: 3;
  }

  .collage__item--left:nth-child(3n - 1):last-child {
    grid-row: span 2;
  }

  .collage__item--right:nth-child(3n - 2) {
    grid-column: 1 / span 1;
    grid-row: span 1;
  }

  .collage__item--right:nth-child(3n - 2):last-child {
    grid-column: 1 / span 3;
  }

  .collage__item--right:nth-child(3n - 1) {
    grid-column-start: 1;
  }

  .collage__item--right:nth-child(3n-1):last-child {
    grid-column: span 2;
  }

  .collage__item--right:nth-child(3n) {
    grid-column: 2 / span 2;
    grid-row: span 2;
  }

  .collage__item--collection:only-child,
  .collage__item--product:only-child {
    justify-self: center;
    max-width: 73rem;
    width: 100%;
  }
}

.collage-card {
  height: 100%;
  position: relative;
  border-radius: var(--border-radius);
  border: var(--border-width) solid rgba(var(--color-foreground), var(--border-opacity));
  padding: var(--image-padding);
}

/* Needed for gradient continuity with or without animation, background-attachment: local scopes the gradient to its container which happens automatically when a transform is applied (animation on scroll) */
.collage-card.gradient {
  transform: perspective(0);
}

.collage-card:after {
  content: '';
  position: absolute;
  z-index: -1;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius)
    rgba(var(--color-shadow), var(--shadow-opacity));
  width: calc(var(--border-width) * 2 + 100%);
  height: calc(var(--border-width) * 2 + 100%);
  top: calc(var(--border-width) * -1);
  left: calc(var(--border-width) * -1);
}

.collage-card.product-card-wrapper {
  --border-radius: var(--product-card-corner-radius);
  --border-width: var(--product-card-border-width);
  --border-opacity: var(--product-card-border-opacity);
  --shadow-horizontal-offset: var(--product-card-shadow-horizontal-offset);
  --shadow-vertical-offset: var(--product-card-shadow-vertical-offset);
  --shadow-blur-radius: var(--product-card-shadow-blur-radius);
  --shadow-opacity: var(--product-card-shadow-opacity);
  --shadow-visible: var(--product-card-shadow-visible);
  --image-padding: var(--product-card-image-padding);
}

.collage-card .media {
  height: 100%;
  overflow: hidden;
  border-radius: calc(var(--border-radius) - var(--border-width) - var(--image-padding));
}

.collage-card .deferred-media {
  height: 100%;
  overflow: visible;
}

.collage-card__link {
  display: block;
  height: 100%;
}

.collage-card .deferred-media__poster {
  background-color: transparent;
  border: 0;
}

.collage-card .deferred-media__poster:after {
  content: '';
  position: absolute;
  z-index: 1;
  outline-offset: 0.3rem;
  bottom: calc(var(--border-width) * -1);
  left: calc(var(--border-width) * -1);
  right: calc(var(--border-width) * -1);
  top: calc(var(--border-width) * -1);
}

.collage-card .deferred-media__poster:focus:after {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
}

.collage-card .deferred-media__poster:focus-visible:after {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
}

.collage-card .deferred-media__poster:focus:not(:focus-visible),
.collage-card .deferred-media__poster:focus:not(:focus-visible):after {
  outline: none;
  box-shadow: none;
}

.collage-card .deferred-media__poster:focus {
  outline: none;
  box-shadow: none;
}
