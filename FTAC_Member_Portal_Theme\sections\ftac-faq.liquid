{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}

<style>
  .ftac-faq {
    padding: var(--ftac-space-20) 0;
    background: linear-gradient(135deg, var(--ftac-warm-cream) 0%, rgba(var(--ftac-warm-cream-rgb), 0.3) 100%);
  }
  
  .ftac-faq__container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 var(--ftac-space-6);
  }
  
  .ftac-faq__header {
    text-align: center;
    margin-bottom: var(--ftac-space-16);
  }
  
  .ftac-faq__title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-4xl);
    font-weight: var(--ftac-font-bold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-faq__subtitle {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-xl);
    color: var(--ftac-charcoal);
    opacity: 0.8;
  }
  
  .ftac-faq__list {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--ftac-space-4);
  }
  
  .ftac-faq__item {
    background-color: white;
    border-radius: var(--ftac-radius-lg);
    box-shadow: var(--ftac-shadow-sm);
    overflow: hidden;
    transition: box-shadow 0.2s ease;
  }
  
  .ftac-faq__item:hover {
    box-shadow: var(--ftac-shadow-md);
  }
  
  .ftac-faq__question {
    width: 100%;
    padding: var(--ftac-space-6);
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-lg);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    transition: color 0.2s ease;
  }
  
  .ftac-faq__question:hover {
    color: var(--ftac-academy-blue);
  }
  
  .ftac-faq__question-icon {
    width: 24px;
    height: 24px;
    color: var(--ftac-academy-blue);
    transition: transform 0.3s ease;
  }
  
  .ftac-faq__question[aria-expanded="true"] .ftac-faq__question-icon {
    transform: rotate(180deg);
  }
  
  .ftac-faq__answer {
    padding: 0 var(--ftac-space-6) var(--ftac-space-6);
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-base);
    color: var(--ftac-charcoal);
    line-height: 1.6;
    opacity: 0.8;
    display: none;
  }
  
  .ftac-faq__answer.active {
    display: block;
    animation: fadeIn 0.3s ease;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 0.8; transform: translateY(0); }
  }
  
  .ftac-faq__cta {
    text-align: center;
    margin-top: var(--ftac-space-16);
    padding: var(--ftac-space-8);
    background-color: white;
    border-radius: var(--ftac-radius-xl);
    box-shadow: var(--ftac-shadow-lg);
  }
  
  .ftac-faq__cta-title {
    font-family: var(--ftac-font-primary);
    font-size: var(--ftac-text-2xl);
    font-weight: var(--ftac-font-semibold);
    color: var(--ftac-charcoal);
    margin-bottom: var(--ftac-space-4);
  }
  
  .ftac-faq__cta-description {
    font-family: var(--ftac-font-secondary);
    font-size: var(--ftac-text-lg);
    color: var(--ftac-charcoal);
    opacity: 0.8;
    margin-bottom: var(--ftac-space-6);
  }
  
  .ftac-faq__cta-button {
    background-color: var(--ftac-academy-blue);
    color: white;
    padding: var(--ftac-space-4) var(--ftac-space-8);
    border-radius: var(--ftac-radius-lg);
    text-decoration: none;
    font-family: var(--ftac-font-primary);
    font-weight: var(--ftac-font-semibold);
    font-size: var(--ftac-text-lg);
    transition: all 0.3s ease;
    box-shadow: var(--ftac-shadow-md);
    display: inline-block;
  }
  
  .ftac-faq__cta-button:hover {
    background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
    transform: translateY(-1px);
    box-shadow: var(--ftac-shadow-lg);
  }
  
  /* Desktop Layout */
  @media (min-width: 1024px) {
    .ftac-faq__title {
      font-size: var(--ftac-text-5xl);
    }
  }
</style>

<section class="ftac-faq">
  <div class="ftac-faq__container">
    <div class="ftac-faq__header">
      <h2 class="ftac-faq__title">Frequently Asked Questions</h2>
      <p class="ftac-faq__subtitle">Quick answers to common questions about Future Tech Academy Club</p>
    </div>
    
    <div class="ftac-faq__list">
      <div class="ftac-faq__item">
        <button class="ftac-faq__question" aria-expanded="false" onclick="toggleFAQ(this)">
          <span>How do I access my purchased content?</span>
          <svg class="ftac-faq__question-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        <div class="ftac-faq__answer">
          After purchasing on Etsy, you'll receive an email with your access credentials and download links. You can also visit our Member Access page and enter your order details to retrieve your content anytime.
        </div>
      </div>
      
      <div class="ftac-faq__item">
        <button class="ftac-faq__question" aria-expanded="false" onclick="toggleFAQ(this)">
          <span>What's included in the premium bundles?</span>
          <svg class="ftac-faq__question-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        <div class="ftac-faq__answer">
          Each bundle includes comprehensive learning resources, exclusive app access, and step-by-step guides. Specific contents vary by bundle - check the Etsy listing for detailed information.
        </div>
      </div>
      
      <div class="ftac-faq__item">
        <button class="ftac-faq__question" aria-expanded="false" onclick="toggleFAQ(this)">
          <span>How quickly will Sarah respond to my questions?</span>
          <svg class="ftac-faq__question-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        <div class="ftac-faq__answer">
          We respond to support requests within 24-48 hours during business hours (Monday-Friday). For technical issues with downloads or access, we aim to respond faster.
        </div>
      </div>
      
      <div class="ftac-faq__item">
        <button class="ftac-faq__question" aria-expanded="false" onclick="toggleFAQ(this)">
          <span>Can I get a refund if I'm not satisfied?</span>
          <svg class="ftac-faq__question-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        <div class="ftac-faq__answer">
          Yes! I offer a 30-day satisfaction guarantee. If you're not completely happy with your purchase, contact me and I'll work with you to make it right or provide a full refund.
        </div>
      </div>
      
      <div class="ftac-faq__item">
        <button class="ftac-faq__question" aria-expanded="false" onclick="toggleFAQ(this)">
          <span>Do I need technical experience to use the resources?</span>
          <svg class="ftac-faq__question-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        <div class="ftac-faq__answer">
          Not at all! Everything is designed for beginners. I break down complex concepts into simple, actionable steps. Plus, I'm always here to help if you get stuck.
        </div>
      </div>
      
      <div class="ftac-faq__item">
        <button class="ftac-faq__question" aria-expanded="false" onclick="toggleFAQ(this)">
          <span>How long do I have access to the content?</span>
          <svg class="ftac-faq__question-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        <div class="ftac-faq__answer">
          You have lifetime access to all purchased content. Download everything to your device, and it's yours forever. App access credentials don't expire either.
        </div>
      </div>
    </div>
    
    <div class="ftac-faq__cta">
      <h3 class="ftac-faq__cta-title">Still have questions?</h3>
      <p class="ftac-faq__cta-description">
        I'm here to help! Send me a message and I'll get back to you within 3 hours.
      </p>
      <a href="#contact-form" class="ftac-faq__cta-button">Ask Sarah a Question</a>
    </div>
  </div>
</section>

<script>
  function toggleFAQ(button) {
    const answer = button.nextElementSibling;
    const isExpanded = button.getAttribute('aria-expanded') === 'true';
    
    // Close all other FAQs
    document.querySelectorAll('.ftac-faq__question').forEach(q => {
      if (q !== button) {
        q.setAttribute('aria-expanded', 'false');
        q.nextElementSibling.classList.remove('active');
      }
    });
    
    // Toggle current FAQ
    button.setAttribute('aria-expanded', !isExpanded);
    answer.classList.toggle('active');
  }
</script>

{% schema %}
{
  "name": "FTAC FAQ",
  "settings": [
    {
      "type": "header",
      "content": "FAQ Section"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Frequently Asked Questions"
    },
    {
      "type": "text",
      "id": "subtitle",
      "label": "Section Subtitle",
      "default": "Quick answers to common questions about Future Tech Academy Club"
    }
  ],
  "presets": [
    {
      "name": "FTAC FAQ"
    }
  ]
}
{% endschema %}
