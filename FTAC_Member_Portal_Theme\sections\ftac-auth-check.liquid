{% comment %}
  FTAC Authentication Check Section
  
  This section handles authentication checks and redirects for member pages.
  It ensures users are properly authenticated and redirects them as needed.
{% endcomment %}

{% if customer %}
  <!-- Customer is logged in, show nothing (let the page content load) -->
  <script>
    // Customer is authenticated, continue to page content
    console.log('FTAC: Customer authenticated, loading member dashboard');
  </script>
{% else %}
  <!-- Customer is not logged in, redirect to member access page -->
  <script>
    console.log('FTAC: Customer not authenticated, redirecting to member access');
    window.location.href = '/pages/member-access';
  </script>
  
  <!-- Fallback content in case JavaScript is disabled -->
  <div style="padding: var(--ftac-space-16); text-align: center; background: var(--ftac-warm-cream);">
    <h2 style="font-family: var(--ftac-font-primary); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-4);">
      Authentication Required
    </h2>
    <p style="font-family: var(--ftac-font-secondary); color: var(--ftac-charcoal); margin-bottom: var(--ftac-space-6);">
      Please log in to access your member dashboard.
    </p>
    <a href="/pages/member-access" style="background: var(--ftac-academy-blue); color: white; padding: var(--ftac-space-3) var(--ftac-space-6); border-radius: var(--ftac-radius-lg); text-decoration: none; font-family: var(--ftac-font-primary); font-weight: var(--ftac-font-medium);">
      Go to Login
    </a>
  </div>
{% endif %}

{% schema %}
{
  "name": "FTAC Auth Check",
  "tag": "section",
  "class": "ftac-auth-check",
  "settings": []
}
{% endschema %}
