{% comment %}
  FTAC Secure Download Links
  
  This snippet generates secure download links for member content.
  It checks customer access and provides appropriate download URLs.
  
  Usage:
  {% render 'ftac-download-links', customer: customer, bundle_id: 'basic' %}
{% endcomment %}

{% assign customer = customer %}
{% assign bundle_id = bundle_id | default: 'basic' %}
{% assign has_access = false %}

{% comment %} Verify customer has access to this bundle {% endcomment %}
{% assign required_tag = 'bundle_' | append: bundle_id %}
{% for tag in customer.tags %}
  {% if tag == required_tag %}
    {% assign has_access = true %}
    {% break %}
  {% endif %}
{% endfor %}

{% if has_access %}
  {% comment %} Generate download links based on bundle type {% endcomment %}
  <div class="ftac-download-links" data-bundle="{{ bundle_id }}">
    {% case bundle_id %}
      {% when 'basic' %}
        <div class="ftac-download-item">
          <h4>Future Tech Foundations Guide</h4>
          <p>Complete PDF guide with essential tech knowledge</p>
          <a href="#" onclick="secureDownload('basic', 'foundations-guide.pdf')" class="ftac-download-button">
            Download PDF (2.5 MB)
          </a>
        </div>
        
        <div class="ftac-download-item">
          <h4>App Access Credentials</h4>
          <p>Login details for your premium app access</p>
          <a href="#" onclick="showCredentials('basic')" class="ftac-download-button">
            View Credentials
          </a>
        </div>
        
        <div class="ftac-download-item">
          <h4>Video Tutorial Series</h4>
          <p>Step-by-step video guides</p>
          <a href="#" onclick="accessVideos('basic')" class="ftac-download-button">
            Access Videos
          </a>
        </div>
      
      {% when 'premium' %}
        <div class="ftac-download-item">
          <h4>Complete PDF Library</h4>
          <p>All guides and resources in one package</p>
          <a href="#" onclick="secureDownload('premium', 'complete-library.zip')" class="ftac-download-button">
            Download Library (15.2 MB)
          </a>
        </div>
        
        <div class="ftac-download-item">
          <h4>Premium App Access</h4>
          <p>Enhanced app features and tools</p>
          <a href="#" onclick="showCredentials('premium')" class="ftac-download-button">
            View Credentials
          </a>
        </div>
        
        <div class="ftac-download-item">
          <h4>Video Course Collection</h4>
          <p>Complete video learning series</p>
          <a href="#" onclick="accessVideos('premium')" class="ftac-download-button">
            Access Course
          </a>
        </div>
        
        <div class="ftac-download-item">
          <h4>1-on-1 Support Access</h4>
          <p>Schedule personal sessions with Sarah</p>
          <a href="/pages/contact-support" class="ftac-download-button">
            Schedule Session
          </a>
        </div>
      
      {% when 'enterprise' %}
        <div class="ftac-download-item">
          <h4>Full Resource Library</h4>
          <p>Everything including enterprise tools</p>
          <a href="#" onclick="secureDownload('enterprise', 'enterprise-suite.zip')" class="ftac-download-button">
            Download Suite (25.8 MB)
          </a>
        </div>
        
        <div class="ftac-download-item">
          <h4>Enterprise App Access</h4>
          <p>Full platform with advanced features</p>
          <a href="#" onclick="showCredentials('enterprise')" class="ftac-download-button">
            View Credentials
          </a>
        </div>
        
        <div class="ftac-download-item">
          <h4>Leadership Course</h4>
          <p>Advanced leadership and innovation training</p>
          <a href="#" onclick="accessVideos('enterprise')" class="ftac-download-button">
            Access Course
          </a>
        </div>
        
        <div class="ftac-download-item">
          <h4>Priority Support</h4>
          <p>Direct line to Sarah for immediate help</p>
          <a href="/pages/contact-support" class="ftac-download-button">
            Contact Now
          </a>
        </div>
      
      {% else %}
        <div class="ftac-download-item">
          <h4>Custom Bundle Resources</h4>
          <p>Your specialized learning materials</p>
          <a href="/pages/contact-support" class="ftac-download-button">
            Contact for Access
          </a>
        </div>
    {% endcase %}
  </div>
  
{% else %}
  <div class="ftac-download-error">
    <p>You don't have access to this bundle. Please contact support if you believe this is an error.</p>
    <a href="/pages/contact-support">Contact Sarah</a>
  </div>
{% endif %}

<script>
function secureDownload(bundleId, filename) {
  // Show loading state
  event.target.innerHTML = 'Preparing Download...';
  event.target.style.pointerEvents = 'none';
  
  // TODO: Implement secure download logic
  // This would:
  // 1. Verify customer access server-side
  // 2. Generate temporary download URL
  // 3. Track download for analytics
  // 4. Serve file securely
  
  // For development, simulate the process
  setTimeout(function() {
    // In production, this would be a real secure download URL
    const downloadUrl = `/downloads/secure/${bundleId}/${filename}?token=temp_token`;
    
    // Create temporary download link
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Reset button
    event.target.innerHTML = `Download ${filename.split('.').pop().toUpperCase()}`;
    event.target.style.pointerEvents = 'auto';
    
    // Track download
    trackDownload(bundleId, filename);
  }, 1500);
}

function showCredentials(bundleId) {
  // TODO: Implement credential display
  // This would show app login details securely
  
  alert(`App credentials for ${bundleId} bundle would be displayed here. Implementation pending.`);
}

function accessVideos(bundleId) {
  // TODO: Implement video access
  // This would redirect to secure video platform
  
  alert(`Video access for ${bundleId} bundle would redirect to secure video platform. Implementation pending.`);
}

function trackDownload(bundleId, filename) {
  // TODO: Implement download tracking
  // This would update customer download count and analytics
  
  console.log(`Download tracked: ${bundleId} - ${filename}`);
}
</script>

<style>
.ftac-download-links {
  display: grid;
  gap: var(--ftac-space-4);
}

.ftac-download-item {
  padding: var(--ftac-space-4);
  background-color: rgba(var(--ftac-warm-cream-rgb), 0.2);
  border-radius: var(--ftac-radius-lg);
  border: 1px solid rgba(var(--ftac-academy-blue-rgb), 0.1);
}

.ftac-download-item h4 {
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-lg);
  font-weight: var(--ftac-font-semibold);
  color: var(--ftac-charcoal);
  margin: 0 0 var(--ftac-space-2) 0;
}

.ftac-download-item p {
  font-family: var(--ftac-font-secondary);
  color: var(--ftac-charcoal);
  opacity: 0.8;
  margin: 0 0 var(--ftac-space-4) 0;
}

.ftac-download-button {
  background-color: var(--ftac-academy-blue);
  color: white;
  padding: var(--ftac-space-2) var(--ftac-space-4);
  border-radius: var(--ftac-radius-md);
  text-decoration: none;
  font-family: var(--ftac-font-primary);
  font-size: var(--ftac-text-sm);
  font-weight: var(--ftac-font-medium);
  transition: all 0.3s ease;
  display: inline-block;
}

.ftac-download-button:hover {
  background-color: rgba(var(--ftac-academy-blue-rgb), 0.9);
  transform: translateY(-1px);
}

.ftac-download-error {
  text-align: center;
  padding: var(--ftac-space-8);
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--ftac-radius-lg);
  color: #dc2626;
}

.ftac-download-error a {
  color: var(--ftac-academy-blue);
  text-decoration: none;
  font-weight: var(--ftac-font-medium);
}
</style>
