{{ 'ftac-brand.css' | asset_url | stylesheet_tag }}
{{ 'luxury-components.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  /* Urgency Section */
  .ftac-urgency-section {
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    color: white;
    position: relative;
    overflow: hidden;
  }

  .ftac-urgency-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -30%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: ftacPulse 4s ease-in-out infinite;
  }

  @keyframes ftacPulse {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.1; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.2; }
  }

  .ftac-urgency-content {
    text-align: center;
    position: relative;
    z-index: 2;
  }

  .ftac-urgency-title {
    font-family: 'Playfair Display', serif;
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .ftac-urgency-subtitle {
    font-family: 'Open Sans', sans-serif;
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }

  /* Countdown Timer */
  .ftac-countdown-timer {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin: 2rem 0;
    flex-wrap: wrap;
  }

  .ftac-countdown-item {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 1.5rem 1rem;
    min-width: 100px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .ftac-countdown-number {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  }

  .ftac-countdown-label {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.9;
  }

  /* Scarcity Indicators */
  .ftac-scarcity-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 3rem;
  }

  .ftac-scarcity-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 200, 221, 0.3);
    box-shadow: 0 10px 30px rgba(255, 200, 221, 0.2);
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .ftac-scarcity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--ftac-sage-accent), var(--ftac-blush-primary));
  }

  .ftac-scarcity-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
    color: var(--ftac-blush-primary);
  }

  .ftac-scarcity-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--ftac-charcoal-text);
    margin-bottom: 0.5rem;
  }

  .ftac-scarcity-description {
    font-family: 'Open Sans', sans-serif;
    color: var(--ftac-charcoal-text);
    opacity: 0.8;
    margin-bottom: 1rem;
  }

  .ftac-scarcity-number {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--ftac-sage-accent);
    display: block;
  }

  /* Progress Bar */
  .ftac-progress-container {
    margin: 1rem 0;
  }

  .ftac-progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 200, 221, 0.2);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
  }

  .ftac-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--ftac-sage-accent), var(--ftac-blush-primary));
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
  }

  .ftac-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s ease-in-out infinite;
  }

  .ftac-progress-text {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.85rem;
    color: var(--ftac-charcoal-text);
    margin-top: 0.5rem;
    text-align: center;
  }

  /* Floating Notifications */
  .ftac-floating-notification {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    padding: 1rem 1.5rem;
    border: 1px solid rgba(255, 200, 221, 0.3);
    box-shadow: 0 10px 30px rgba(255, 200, 221, 0.3);
    z-index: 1000;
    transform: translateX(-120%);
    transition: transform 0.5s ease;
    max-width: 300px;
  }

  .ftac-floating-notification.show {
    transform: translateX(0);
  }

  .ftac-notification-content {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .ftac-notification-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--ftac-blush-primary), var(--ftac-peach-glow));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: 'Playfair Display', serif;
    font-weight: 700;
  }

  .ftac-notification-text {
    flex: 1;
  }

  .ftac-notification-name {
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    color: var(--ftac-charcoal-text);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
  }

  .ftac-notification-action {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.8rem;
    color: var(--ftac-blush-primary);
  }

  .ftac-notification-time {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.75rem;
    color: var(--ftac-charcoal-text);
    opacity: 0.6;
    margin-top: 0.25rem;
  }

  /* Responsive Design */
  @media screen and (max-width: 768px) {
    .ftac-countdown-timer {
      gap: 1rem;
    }
    
    .ftac-countdown-item {
      min-width: 80px;
      padding: 1rem 0.75rem;
    }
    
    .ftac-countdown-number {
      font-size: 2rem;
    }
    
    .ftac-scarcity-indicators {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .ftac-floating-notification {
      left: 10px;
      right: 10px;
      max-width: none;
    }
  }
{%- endstyle -%}

<div class="ftac-urgency-section section-{{ section.id }}-padding">
  <div class="page-width">
    <div class="ftac-urgency-content">
      {% if section.settings.show_countdown %}
        <h2 class="ftac-urgency-title">{{ section.settings.countdown_title | default: "Limited Time Offer!" }}</h2>
        <p class="ftac-urgency-subtitle">{{ section.settings.countdown_subtitle | default: "Don't miss out on this exclusive opportunity" }}</p>
        
        <div class="ftac-countdown-timer" id="ftac-countdown-{{ section.id }}">
          <div class="ftac-countdown-item">
            <span class="ftac-countdown-number" id="days">00</span>
            <span class="ftac-countdown-label">Days</span>
          </div>
          <div class="ftac-countdown-item">
            <span class="ftac-countdown-number" id="hours">00</span>
            <span class="ftac-countdown-label">Hours</span>
          </div>
          <div class="ftac-countdown-item">
            <span class="ftac-countdown-number" id="minutes">00</span>
            <span class="ftac-countdown-label">Minutes</span>
          </div>
          <div class="ftac-countdown-item">
            <span class="ftac-countdown-number" id="seconds">00</span>
            <span class="ftac-countdown-label">Seconds</span>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

{% if section.settings.show_scarcity %}
  <div class="page-width section-{{ section.id }}-padding">
    <div class="ftac-scarcity-indicators">
      {% for block in section.blocks %}
        {% case block.type %}
          {% when 'scarcity_indicator' %}
            <div class="ftac-scarcity-card" {{ block.shopify_attributes }}>
              <span class="ftac-scarcity-icon">{{ block.settings.icon | default: "🔥" }}</span>
              <h3 class="ftac-scarcity-title">{{ block.settings.title | default: "Limited Spots" }}</h3>
              <p class="ftac-scarcity-description">{{ block.settings.description | default: "Only a few spots remaining" }}</p>
              
              {% if block.settings.show_progress %}
                <div class="ftac-progress-container">
                  <div class="ftac-progress-bar">
                    <div class="ftac-progress-fill" style="width: {{ block.settings.progress_percentage | default: 75 }}%"></div>
                  </div>
                  <p class="ftac-progress-text">{{ block.settings.progress_text | default: "75% claimed" }}</p>
                </div>
              {% endif %}
              
              {% if block.settings.number != blank %}
                <span class="ftac-scarcity-number">{{ block.settings.number }}</span>
              {% endif %}
            </div>
        {% endcase %}
      {% endfor %}
    </div>
  </div>
{% endif %}

{% if section.settings.show_notifications %}
  <div class="ftac-floating-notification" id="ftac-notification">
    <div class="ftac-notification-content">
      <div class="ftac-notification-avatar">S</div>
      <div class="ftac-notification-text">
        <div class="ftac-notification-name">Sarah from California</div>
        <div class="ftac-notification-action">just purchased the Creative Bundle</div>
        <div class="ftac-notification-time">2 minutes ago</div>
      </div>
    </div>
  </div>
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Countdown Timer
  {% if section.settings.show_countdown %}
    const countdownDate = new Date("{{ section.settings.countdown_end | default: '2024-12-31T23:59:59' }}").getTime();
    
    function updateCountdown() {
      const now = new Date().getTime();
      const distance = countdownDate - now;
      
      if (distance > 0) {
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);
        
        document.getElementById('days').textContent = days.toString().padStart(2, '0');
        document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
        document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
        document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
      } else {
        document.getElementById('days').textContent = '00';
        document.getElementById('hours').textContent = '00';
        document.getElementById('minutes').textContent = '00';
        document.getElementById('seconds').textContent = '00';
      }
    }
    
    updateCountdown();
    setInterval(updateCountdown, 1000);
  {% endif %}
  
  // Floating Notifications
  {% if section.settings.show_notifications %}
    const notifications = [
      { name: 'Sarah from California', action: 'just purchased the Creative Bundle', time: '2 minutes ago', avatar: 'S' },
      { name: 'Mike from New York', action: 'joined the membership', time: '5 minutes ago', avatar: 'M' },
      { name: 'Emma from London', action: 'downloaded the starter pack', time: '8 minutes ago', avatar: 'E' },
      { name: 'Alex from Toronto', action: 'upgraded to premium', time: '12 minutes ago', avatar: 'A' }
    ];
    
    let currentNotification = 0;
    
    function showNotification() {
      const notification = document.getElementById('ftac-notification');
      const data = notifications[currentNotification];
      
      notification.querySelector('.ftac-notification-avatar').textContent = data.avatar;
      notification.querySelector('.ftac-notification-name').textContent = data.name;
      notification.querySelector('.ftac-notification-action').textContent = data.action;
      notification.querySelector('.ftac-notification-time').textContent = data.time;
      
      notification.classList.add('show');
      
      setTimeout(() => {
        notification.classList.remove('show');
      }, 4000);
      
      currentNotification = (currentNotification + 1) % notifications.length;
    }
    
    // Show first notification after 3 seconds
    setTimeout(showNotification, 3000);
    
    // Show subsequent notifications every 8 seconds
    setInterval(showNotification, 8000);
  {% endif %}
});
</script>

{% schema %}
{
  "name": "Urgency & Scarcity",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Countdown Timer"
    },
    {
      "type": "checkbox",
      "id": "show_countdown",
      "default": true,
      "label": "Show countdown timer"
    },
    {
      "type": "text",
      "id": "countdown_title",
      "default": "Limited Time Offer!",
      "label": "Countdown title"
    },
    {
      "type": "text",
      "id": "countdown_subtitle",
      "default": "Don't miss out on this exclusive opportunity",
      "label": "Countdown subtitle"
    },
    {
      "type": "text",
      "id": "countdown_end",
      "default": "2024-12-31T23:59:59",
      "label": "Countdown end date (YYYY-MM-DDTHH:MM:SS)"
    },
    {
      "type": "header",
      "content": "Scarcity Indicators"
    },
    {
      "type": "checkbox",
      "id": "show_scarcity",
      "default": true,
      "label": "Show scarcity indicators"
    },
    {
      "type": "header",
      "content": "Social Proof Notifications"
    },
    {
      "type": "checkbox",
      "id": "show_notifications",
      "default": true,
      "label": "Show floating notifications"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 40
    }
  ],
  "blocks": [
    {
      "type": "scarcity_indicator",
      "name": "Scarcity Indicator",
      "settings": [
        {
          "type": "text",
          "id": "icon",
          "default": "🔥",
          "label": "Icon (emoji)"
        },
        {
          "type": "text",
          "id": "title",
          "default": "Limited Spots",
          "label": "Title"
        },
        {
          "type": "textarea",
          "id": "description",
          "default": "Only a few spots remaining at this special price",
          "label": "Description"
        },
        {
          "type": "text",
          "id": "number",
          "default": "7",
          "label": "Number to display"
        },
        {
          "type": "checkbox",
          "id": "show_progress",
          "default": true,
          "label": "Show progress bar"
        },
        {
          "type": "range",
          "id": "progress_percentage",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "label": "Progress percentage",
          "default": 75
        },
        {
          "type": "text",
          "id": "progress_text",
          "default": "75% claimed",
          "label": "Progress text"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Urgency & Scarcity",
      "blocks": [
        {
          "type": "scarcity_indicator",
          "settings": {
            "icon": "🔥",
            "title": "Limited Spots Available",
            "description": "Only a few spots remaining at this exclusive price",
            "number": "7",
            "show_progress": true,
            "progress_percentage": 85,
            "progress_text": "85% claimed"
          }
        },
        {
          "type": "scarcity_indicator",
          "settings": {
            "icon": "⏰",
            "title": "Early Bird Pricing",
            "description": "Special launch pricing ends soon",
            "number": "48hrs",
            "show_progress": true,
            "progress_percentage": 60,
            "progress_text": "60% of time remaining"
          }
        },
        {
          "type": "scarcity_indicator",
          "settings": {
            "icon": "👥",
            "title": "High Demand",
            "description": "Join hundreds of satisfied customers",
            "number": "500+",
            "show_progress": false
          }
        }
      ]
    }
  ]
}
{% endschema %}
