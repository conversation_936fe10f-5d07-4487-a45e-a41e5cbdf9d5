/* Professional Component Library - Modern E-commerce Design Elements */

/* Professional <PERSON><PERSON> Styles with Clean Effects */
.ftac-btn-luxury {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 16px 32px;
  font-family: var(--ftac-font-primary);
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 56px;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(var(--ftac-academy-blue-rgb), 0.2);
}

.ftac-btn-luxury::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.ftac-btn-luxury:hover::before {
  left: 100%;
}

.ftac-btn-primary {
  background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green));
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ftac-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--ftac-academy-blue-rgb), 0.3);
}

.ftac-btn-secondary {
  background: linear-gradient(135deg, var(--ftac-learning-green), #10B981);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ftac-btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--ftac-learning-green-rgb), 0.3);
}

.ftac-btn-outline {
  background: transparent;
  color: var(--ftac-academy-blue);
  border: 2px solid var(--ftac-academy-blue);
  box-shadow: 0 4px 15px rgba(var(--ftac-academy-blue-rgb), 0.2);
}

.ftac-btn-outline:hover {
  background: var(--ftac-academy-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(var(--ftac-academy-blue-rgb), 0.3);
}

/* Luxury Card Components */
.ftac-card-luxury {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 10px 40px rgba(var(--ftac-charcoal-text-rgb), 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.ftac-card-luxury::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--ftac-academy-blue), var(--ftac-learning-green), var(--ftac-charcoal));
}

.ftac-card-luxury:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(var(--ftac-charcoal-text-rgb), 0.12);
}

.ftac-card-product {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(var(--ftac-charcoal-text-rgb), 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.ftac-card-product:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(var(--ftac-charcoal-text-rgb), 0.1);
}

.ftac-card-product .card-image {
  position: relative;
  overflow: hidden;
}

.ftac-card-product .card-image img {
  transition: transform 0.3s ease;
}

.ftac-card-product:hover .card-image img {
  transform: scale(1.05);
}

.ftac-card-product .card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(var(--ftac-academy-blue-rgb), 0.8), rgba(var(--ftac-learning-green-rgb), 0.8));
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ftac-card-product:hover .card-overlay {
  opacity: 1;
}

/* Luxury Input Fields */
.ftac-input-luxury {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid rgba(var(--ftac-academy-blue-rgb), 0.2);
  border-radius: 12px;
  font-family: var(--ftac-font-primary);
  font-size: 16px;
  background: var(--ftac-warm-cream);
  color: var(--ftac-charcoal);
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(var(--ftac-charcoal-text-rgb), 0.04);
}

.ftac-input-luxury:focus {
  outline: none;
  border-color: var(--ftac-academy-blue);
  box-shadow: 0 0 0 4px rgba(var(--ftac-academy-blue-rgb), 0.1), 0 4px 12px rgba(var(--ftac-charcoal-rgb), 0.08);
  transform: translateY(-1px);
}

.ftac-input-luxury::placeholder {
  color: rgba(var(--ftac-charcoal-text-rgb), 0.5);
}

/* Luxury Badge/Tag Components */
.ftac-badge-luxury {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  font-family: var(--ftac-font-primary);
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.ftac-badge-primary {
  background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green));
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ftac-badge-success {
  background: linear-gradient(135deg, var(--ftac-learning-green), #10B981);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ftac-badge-trust {
  background: linear-gradient(135deg, var(--ftac-charcoal), #4B5563);
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Glow Effects */
.ftac-glow-primary {
  box-shadow: 0 0 20px rgba(var(--ftac-academy-blue-rgb), 0.3);
}

.ftac-glow-success {
  box-shadow: 0 0 20px rgba(var(--ftac-learning-green-rgb), 0.3);
}

.ftac-glow-trust {
  box-shadow: 0 0 20px rgba(var(--ftac-charcoal-rgb), 0.3);
}

/* Hover Animations */
.ftac-hover-lift {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ftac-hover-lift:hover {
  transform: translateY(-4px);
}

.ftac-hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ftac-hover-glow:hover {
  box-shadow: 0 8px 32px rgba(var(--ftac-academy-blue-rgb), 0.3);
}

/* Luxury Dividers */
.ftac-divider-luxury {
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--ftac-charcoal), transparent);
  margin: 32px 0;
}

.ftac-divider-dots {
  text-align: center;
  margin: 32px 0;
  position: relative;
}

.ftac-divider-dots::before {
  content: '• • •';
  color: var(--ftac-charcoal);
  font-size: 24px;
  letter-spacing: 8px;
}

/* Luxury Loading Spinner */
.ftac-spinner-luxury {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(var(--ftac-academy-blue-rgb), 0.2);
  border-top: 3px solid var(--ftac-academy-blue);
  border-radius: 50%;
  animation: ftac-spin 1s linear infinite;
}

@keyframes ftac-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Professional Gradient Backgrounds */
.ftac-bg-luxury-primary {
  background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green));
}

.ftac-bg-luxury-secondary {
  background: linear-gradient(135deg, var(--ftac-learning-green), #10B981);
}

.ftac-bg-luxury-trust {
  background: linear-gradient(135deg, var(--ftac-charcoal), #4B5563);
}

.ftac-bg-luxury-cream {
  background: linear-gradient(135deg, var(--ftac-warm-cream), #FFFEF7);
}

/* ===== LUXURY PRODUCT PAGE COMPONENTS ===== */

/* Product Title Styling */
.ftac-luxury-product__title {
  margin-bottom: 1.5rem;
  text-align: center;
}

.ftac-luxury-product__title-text {
  font-family: 'Playfair Display', serif;
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--ftac-charcoal-text);
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.ftac-luxury-product__title-accent {
  margin-top: 0.5rem;
}

.ftac-luxury-product__handcrafted {
  font-family: 'Dancing Script', cursive;
  font-size: 1.2rem;
  color: var(--ftac-academy-blue);
  font-weight: 600;
}

/* Product Price Styling */
.ftac-luxury-product__price {
  margin-bottom: 2rem;
  text-align: center;
}

.ftac-luxury-product__price-container {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 200, 221, 0.3);
  box-shadow: 0 10px 30px rgba(255, 200, 221, 0.15);
  display: inline-block;
  min-width: 200px;
}

.ftac-luxury-price {
  font-family: 'Playfair Display', serif;
  font-size: 2rem;
  font-weight: 700;
  color: var(--ftac-charcoal-text);
}

.ftac-luxury-product__value-prop {
  margin-top: 0.5rem;
}

.ftac-luxury-product__value-text {
  font-family: 'Open Sans', sans-serif;
  font-size: 0.9rem;
  color: var(--ftac-learning-green);
  font-weight: 600;
}

/* Buy Section Styling */
.ftac-luxury-product__buy-section {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  padding: 2rem;
  border: 1px solid rgba(255, 200, 221, 0.2);
  box-shadow: 0 15px 35px rgba(255, 200, 221, 0.2);
  margin: 2rem 0;
}

.ftac-luxury-product__trust-signals {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.ftac-luxury-trust-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(255, 200, 221, 0.2);
  transition: all 0.3s ease;
}

.ftac-luxury-trust-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 200, 221, 0.3);
}

.ftac-luxury-trust-icon {
  font-size: 1.2rem;
}

.ftac-luxury-trust-text {
  font-family: 'Open Sans', sans-serif;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--ftac-charcoal-text);
}

.ftac-luxury-product__buttons {
  margin: 2rem 0;
}

/* Override default button styles for luxury look */
.ftac-luxury-product__buttons .btn,
.ftac-luxury-product__buttons .shopify-payment-button__button {
  background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green)) !important;
  border: none !important;
  border-radius: 25px !important;
  padding: 1.2rem 2.5rem !important;
  font-family: 'Open Sans', sans-serif !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  color: white !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 8px 25px rgba(255, 200, 221, 0.4) !important;
  position: relative !important;
  overflow: hidden !important;
}

.ftac-luxury-product__buttons .btn:hover,
.ftac-luxury-product__buttons .shopify-payment-button__button:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 15px 35px rgba(255, 200, 221, 0.6) !important;
}

.ftac-luxury-product__guarantee {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 200, 221, 0.3);
}

.ftac-luxury-guarantee-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin: 0;
  color: var(--ftac-charcoal-text);
}

.ftac-luxury-guarantee-icon {
  font-size: 1.2rem;
}

/* Product Page Responsive Design */
@media screen and (max-width: 749px) {
  .ftac-luxury-product__trust-signals {
    grid-template-columns: 1fr;
  }

  .ftac-luxury-trust-badge {
    justify-content: flex-start;
  }

  .ftac-luxury-product__title-text {
    font-size: 1.8rem;
  }

  .ftac-luxury-product__buy-section {
    padding: 1.5rem;
  }

  .ftac-luxury-product__price-container {
    min-width: auto;
    width: 100%;
  }
}

/* Luxury Product Cards for Shop Grid */
.card-wrapper {
  transition: all 0.4s ease;
}

.card-wrapper:hover {
  transform: translateY(-8px);
}

.card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(15px);
  border-radius: 25px !important;
  border: 1px solid rgba(255, 200, 221, 0.2) !important;
  box-shadow: 0 10px 30px rgba(255, 200, 221, 0.1) !important;
  overflow: hidden;
  transition: all 0.4s ease;
  position: relative;
}

.card:hover {
  box-shadow: 0 20px 50px rgba(255, 200, 221, 0.25) !important;
  border-color: var(--ftac-academy-blue) !important;
}

.card::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent, rgba(255, 200, 221, 0.05), transparent);
  transform: rotate(45deg);
  animation: shimmer 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.card__media {
  position: relative;
  overflow: hidden;
  border-radius: 20px 20px 0 0;
}

.card__media img {
  transition: all 0.4s ease;
}

.card:hover .card__media img {
  transform: scale(1.05);
}

.card__content {
  padding: 1.5rem !important;
  position: relative;
  z-index: 2;
}

.card__heading {
  font-family: 'Playfair Display', serif !important;
  font-weight: 700 !important;
  font-size: 1.2rem !important;
  color: var(--ftac-charcoal-text) !important;
  margin-bottom: 0.5rem !important;
  line-height: 1.3 !important;
}

.card__heading a {
  color: inherit !important;
  text-decoration: none !important;
  transition: color 0.3s ease;
}

.card__heading a:hover {
  color: var(--ftac-academy-blue) !important;
}

.price {
  font-family: 'Open Sans', sans-serif !important;
  font-weight: 600 !important;
  color: var(--ftac-charcoal-text) !important;
}

.price__regular {
  font-size: 1.1rem !important;
  color: var(--ftac-academy-blue) !important;
}

.price__sale {
  color: var(--ftac-learning-green) !important;
}

.quick-add__btn {
  background: linear-gradient(135deg, var(--ftac-academy-blue), var(--ftac-learning-green)) !important;
  color: white !important;
  border: none !important;
  border-radius: 20px !important;
  padding: 0.75rem 1.5rem !important;
  font-family: 'Open Sans', sans-serif !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 200, 221, 0.3) !important;
  width: 100% !important;
}

.quick-add__btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(255, 200, 221, 0.4) !important;
}
